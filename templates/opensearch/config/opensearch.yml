bootstrap.memory_lock: false # Swapping is disabled in Kubernetes by default.
cluster.name: {{ .Cluster.Name }}
cluster.routing.allocation.awareness.attributes: availability_zone
cluster.routing.allocation.allow_rebalance: indices_primaries_active
{{- if .IsSingleNodeCluster }}
discovery.type: single-node
{{- else }}
cluster.initial_master_nodes: 
  {{- range $key, $value := .InitialClusterManagerNodes }}
  - {{ $value }}
  {{- end }}
{{- end }}
discovery.seed_hosts:
  - ${DISCOVERY_SERVICE}
http.port: 9200
indices.breaker.fielddata.limit: 15%
indices.breaker.total.use_real_memory: false
indices.fielddata.cache.size: 10%
network.breaker.inflight_requests.limit: 65%
network.host: 0.0.0.0
node.attr.availability_zone: ${AVAILABILITY_ZONE}
node.roles: ${NODE_ROLES}
node.name: ${HOSTNAME}
plugins.alerting.destination.allow_list: ["chime", "slack", "custom_webhook", "test_action"]
{{- if ge .TargetVersion.Major 2 }}
opensearch.notifications.core.allowed_config_types: [ "slack", "chime", "webhook", "microsoft_teams"]
{{- end }}
plugins.security.ssl.transport.enabled: true
plugins.security.ssl.transport.pemcert_filepath: "tls-transport/crt.pem"
plugins.security.ssl.transport.pemkey_filepath: "tls-transport/key.pem"
plugins.security.ssl.transport.pemtrustedcas_filepath: "ca/crt.pem"
plugins.security.ssl.transport.enforce_hostname_verification: false
plugins.security.ssl.http.enabled: true
plugins.security.ssl.http.pemcert_filepath: "tls-transport/crt.pem"
plugins.security.ssl.http.pemkey_filepath: "tls-transport/key.pem"
plugins.security.ssl.http.pemtrustedcas_filepath: "ca/crt.pem"
plugins.security.allow_unsafe_democertificates: false
plugins.security.allow_default_init_securityindex: false
plugins.security.authcz.admin_dn:
  - 'CN=admin,OU=PerfX,O=SAP SE,L=Karlsruhe,ST=Baden-Wuerttemberg,C=DE'
  - 'CN=admin,OU=Cloud Logging,O=SAP SE,L=Karlsruhe,ST=Baden-Wuerttemberg,C=DE'
plugins.security.nodes_dn:
  - 'CN=node,OU=PerfX,O=SAP SE,L=Karlsruhe,ST=Baden-Wuerttemberg,C=DE'
  - 'CN=node,OU=Cloud Logging,O=SAP SE,L=Karlsruhe,ST=Baden-Wuerttemberg,C=DE'
plugins.security.audit.type: internal_opensearch
plugins.security.enable_snapshot_restore_privilege: true
plugins.security.check_snapshot_restore_write_privileges: true
plugins.security.restapi.roles_enabled: ["admin", "manage_opendistro_config"]
plugins.security.unsupported.restapi.allow_securityconfig_modification: true
plugins.security.restapi.password_validation_regex: "(?=.*[a-zA-Z]).{20,}"
plugins.security.restapi.password_validation_error_message: "Password must be at least 20 upper case or lower case."
