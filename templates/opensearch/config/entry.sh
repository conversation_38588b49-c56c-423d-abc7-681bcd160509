#!/bin/bash
set -euo pipefail

log() {
    echo "$(date -u) entry.sh: $1"
}

log "Fetching info of the node where this pod is scheduled..."
NODE_INFO=$(curl -sSk -H "Authorization: Bearer $(cat /var/run/secrets/kubernetes.io/serviceaccount/token)" https://$KUBERNETES_SERVICE_HOST:$KUBERNETES_SERVICE_PORT_HTTPS/api/v1/nodes/${KUBERNETES_NODE_NAME})

{{- if ge .TargetVersion.Major 2 }}
AVAILABILITY_ZONE=$(echo $NODE_INFO | python3 -c "import sys, json; print(json.load(sys.stdin)['metadata']['labels'].get('topology.kubernetes.io/zone', ''))")
{{- else }}
AVAILABILITY_ZONE=$(echo $NODE_INFO | python2 -c "import sys, json; print(json.load(sys.stdin)['metadata']['labels'].get('topology.kubernetes.io/zone', ''))")
{{- end }}

if [[ $AVAILABILITY_ZONE == null || -z "$AVAILABILITY_ZONE" ]]; then
    log "Error: AVAILABILITY_ZONE of this pod could not be determined. Exiting."
    exit 1
fi
export AVAILABILITY_ZONE

{{- if ge .TargetVersion.Major 2 }}
log "Fetching list of available plugins..."

./bin/opensearch-plugin list

log "Disabling plugins..."

./bin/opensearch-plugin remove opensearch-geospatial --purge
./bin/opensearch-plugin remove opensearch-performance-analyzer --purge
./bin/opensearch-plugin remove opensearch-neural-search --purge
./bin/opensearch-plugin remove opensearch-ltr --purge
./bin/opensearch-plugin remove opensearch-skills --purge
./bin/opensearch-plugin remove opensearch-flow-framework --purge
./bin/opensearch-plugin remove query-insights --purge

log "Fetching list of plugins..."

./bin/opensearch-plugin list

{{- end }}

log "The pod is created in AVAILABILITY_ZONE=${AVAILABILITY_ZONE}"
log "Starting OpenSearch default entry-point /usr/share/opensearch/opensearch-docker-entrypoint.sh"
set +euo pipefail
source /usr/share/opensearch/opensearch-docker-entrypoint.sh
