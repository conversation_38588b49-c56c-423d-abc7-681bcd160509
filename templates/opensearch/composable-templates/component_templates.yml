component-templates: |-
  {
    "default-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "settings": {
          "index": {
            "number_of_shards": "{{ .NumberOfDataNodes }}",
            "number_of_replicas": "{{ .ReplicaShards }}",
            "mapping": {
              "ignore_malformed": "true"
            }
          }
        }
      }
    },
    "default-no-scale-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "settings": {
          "index": {
            "number_of_shards": "{{ .MinNumberOfDataNodes }}",
            "number_of_replicas": "{{ .ReplicaShards }}",
            "mapping": {
              "ignore_malformed": "true"
            }
          }
        }
      }
    },
    "default-cfsyslog-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "mappings": {
          "properties": {
            "@timestamp": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "date"
            },
            "bytes_dropped": {
              "doc_values": true,
              "type": "long"
            },
            "bytes_shipped": {
              "doc_values": true,
              "type": "long"
            },
            "logs_dropped": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "long"
            },
            "logs_shipped": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "long"
            },
            "request_received_at": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "date"
            },
            "request_sent_at": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "date"
            },
            "request_size_b": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "long"
            },
            "request": {
              "type": "text",
              "fields": {
                "keyword": {
                  "type": "keyword"
                }
              }
            },
            "response_received_at": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "date"
            },
            "response_sent_at": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "date"
            },
            "response_size_b": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "long"
            },
            "response_status": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "integer"
            },
            "size": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "long"
            },
            "timestamp": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "long"
            },
            "written_at": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "date"
            },
            "written_ts": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "long"
            },
            "instance_id": {
              "type": "text",
              "fields": {
                "keyword": {
                  "type": "keyword"
                }
              }
            },
            "gorouter_time": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "float"
            },
            "response_time": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "float"
            },
            "response_time_ms": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "float"
            },
            "syslog_priority": {
              "doc_values": true,
              "ignore_malformed": true,
              "type": "integer"
            },
            "component_instance": {
              "type": "text",
              "fields": {
                "keyword": {
                  "type": "keyword"
                }
              }
            }
          }
        }
      }
    },
    "default-rejected-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "mappings": {
          "properties": {
            "@timestamp": {
              "ignore_malformed": true,
              "type": "date",
              "doc_values": true
            },
            "fluentd_tag": {
              "type": "text",
              "fields": {
                "keyword": {
                  "type": "keyword"
                }
              }
            },
            "index_prefix": {
              "type": "text",
              "fields": {
                "keyword": {
                  "type": "keyword"
                }
              }
            },
            "payload": {
              "type": "text",
              "index": false,
              "store": true
            }
          }
        }
      }
    },
    "default-otel-metrics-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "mappings": {
          "dynamic_templates": [
            {
              "metric_attributes_map": {
                "path_match": "metric.attributes.*",
                "mapping": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            },
            {
              "exemplars_attributes_map": {
                "path_match": "exemplars.attributes.exemplar.attributes.*",
                "mapping": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            }
          ],
          "properties": {
            "aggregationTemporality": {
              "type": "keyword",
              "ignore_above": 256
            },
            "bucketCounts": {
              "type": "long"
            },
            "bucketCountsList": {
              "type": "long"
            },
            "buckets": {
              "type": "nested",
              "properties": {
                "count" : {
                  "type" : "long"
                },
                "max" : {
                  "type" : "double"
                },
                "min" : {
                  "type" : "double"
                }
              }
            },
            "count" : {
              "type" : "long"
            },
            "description": {
              "type": "text"
            },
            "exemplars": {
              "type": "nested",
              "properties": {
                "spanId": {
                  "type": "keyword",
                  "ignore_above": 32
                },
                "time": {
                  "type": "date_nanos"
                },
                "traceId": {
                  "type": "keyword",
                  "ignore_above": 64
                },
                "value": {
                  "type": "double"
                }
              }
            },
            "explicitBounds" : {
              "type" : "double"
            },
            "explicitBoundsCount" : {
              "type" : "long"
            },
            "flags": {
              "type": "long"
            },
            "isMonotonic": {
              "type": "boolean"
            },
            "kind": {
              "type": "keyword",
              "ignore_above": 256
            },
            "name": {
              "type": "keyword",
              "ignore_above": 256
            },
            "negative" : {
              "type" : "long"
            },
            "negativeBuckets" : {
              "type": "nested",
              "properties" : {
                "count" : {
                  "type" : "long"
                },
                "max" : {
                  "type" : "double"
                },
                "min" : {
                  "type" : "double"
                }
              }
            },
            "negativeOffset" : {
              "type" : "long"
            },
            "positive" : {
              "type" : "long"
            },
            "positiveBuckets" : {
              "type": "nested",
              "properties" : {
                "count" : {
                  "type" : "long"
                },
                "max" : {
                  "type" : "double"
                },
                "min" : {
                  "type" : "double"
                }
              }
            },
            "positiveOffset" : {
              "type" : "long"
            },
            "quantileValuesCount" : {
              "type" : "long"
            },
            "quantiles" : {
              "type": "nested",
              "properties" : {
                "quantile" : {
                  "type" : "double"
                },
                "value" : {
                  "type" : "double"
                }
              }
            },
            "scale" : {
              "type" : "long"
            },
            "schemaUrl": {
              "type": "keyword",
              "ignore_above": 256
            },
            "serviceName": {
              "type": "keyword",
              "ignore_above": 256
            },
            "startTime": {
              "type": "date_nanos"
            },
            "sum" : {
              "type" : "double"
            },
            "time": {
              "type": "date_nanos"
            },
            "unit": {
              "type": "keyword",
              "ignore_above": 256
            },
            "value": {
              "type": "double"
            },
            "zeroCount" : {
              "type" : "long"
            }
          }
        }
      }
    },
    "default-otel-logs-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "mappings": {
          "dynamic_templates": [
            {
              "log_attributes_map": {
                "path_match": "log.attributes.*",
                "mapping": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            }
          ],
          "properties": {
            "body": {
              "type": "text"
            },
            "droppedAttributesCount": {
              "type": "long"
            },
            "flags": {
              "type": "long"
            },
            "observedTimestamp": {
              "type": "date_nanos"
            },
              "schemaUrl": {
              "type": "keyword",
              "ignore_above": 256
            },
            "serviceName": {
              "type": "keyword",
              "ignore_above": 256
            },
            "severityNumber": {
              "type": "integer"
            },
            "severityText": {
              "type": "keyword",
              "ignore_above": 256
            },
            "spanId": {
              "type": "keyword",
              "ignore_above": 32
            },
            "time": {
              "type": "date_nanos"
            },
            "traceId": {
              "type": "keyword",
              "ignore_above": 64
            }
          }
        }
      }
    },
    "default-otel-request-logs-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "mappings": {
          "properties": {
            "log.attributes.http@request@duration": {
              "type": "integer",
              "ignore_malformed": "true"
            },
            "log.attributes.http@request@method": {
              "type": "keyword",
              "ignore_above": 16
            },
            "log.attributes.http@response@body@size": {
              "type": "long",
              "ignore_malformed": "true"
            },
            "log.attributes.http@request@body@size": {
              "type": "long",
              "ignore_malformed": "true"
            },
            "log.attributes.http@request@size": {
              "type": "long",
              "ignore_malformed": "true"
            },
            "log.attributes.http@response@size": {
              "type": "long",
              "ignore_malformed": "true"
            },
            "log.attributes.http@response@status_code": {
              "type": "integer",
              "ignore_malformed": "true"
            },
            "log.attributes.http@status_code": {
              "type": "integer",
              "ignore_malformed": "true"
            },
            "log.attributes.network@peer@address": {
              "type": "keyword",
              "ignore_above": 256
            },
            "log.attributes.network@protocol@name": {
              "type": "keyword",
              "ignore_above": 32
            }
          }
        }
      }
    },
    "default-otel-traces-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "settings": {
          "index": {
            "plugins": {
              "index_state_management": {
                "rollover_alias": "otel-v1-apm-span"
              }
            }
          }
        },
        "mappings": {
          "dynamic_templates": [
            {
              "events_attributes_map": {
                "path_match": "events.attributes.*",
                "mapping": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            },
            {
              "links_attributes_map": {
                "path_match": "links.attributes.*",
                "mapping": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            },
            {
              "span_attributes_map": {
                "path_match": "span.attributes.*",
                "mapping": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            }
          ],
          "properties": {
            "droppedAttributesCount": {
              "type" : "long"
            },
            "droppedEventsCount": {
              "type" : "long"
            },
            "droppedLinksCount": {
              "type" : "long"
            },
            "durationInNanos": {
              "type": "long"
            },
            "endTime": {
              "type": "date_nanos"
            },
            "events": {
              "type": "nested",
              "properties": {
                "droppedAttributesCount": {
                  "type" : "long"
                },
                "name": {
                  "ignore_above": 256,
                  "type": "keyword"
                },
                "time": {
                  "type": "date_nanos"
                }
              }
            },
            "kind": {
              "ignore_above": 128,
              "type": "keyword"
            },
            "links": {
              "type": "nested",
              "properties": {
                "droppedAttributesCount": {
                  "type" : "long"
                },
                "spanId": {
                  "ignore_above": 32,
                  "type": "keyword"
                },
                "traceId": {
                  "ignore_above": 64,
                  "type": "keyword"
                },
                "traceState": {
                  "type": "text",
                  "fields": {
                    "keyword": {
                      "type": "keyword",
                      "ignore_above": 1024
                    }
                  }
                }
              }
            },
            "name": {
              "ignore_above": 1024,
              "type": "keyword"
            },
            "parentSpanId": {
              "ignore_above": 32,
              "type": "keyword"
            },
            "serviceName": {
              "ignore_above": 256,
              "type": "keyword"
            },
            "spanId": {
              "ignore_above": 32,
              "type": "keyword"
            },
            "startTime": {
              "type": "date_nanos"
            },
            "status": {
              "type": "object",
              "properties": {
                "code": {
                  "type": "integer"
                },
                "message": {
                  "ignore_above": 1024,
                  "type": "keyword"
                }
              }
            },
            "traceGroup": {
              "ignore_above": 1024,
              "type": "keyword"
            },
            "traceGroupFields": {
              "type": "object",
              "properties": {
                "durationInNanos": {
                  "type": "long"
                },
                "endTime": {
                  "type": "date_nanos"
                },
                "statusCode": {
                  "type": "integer"
                }
              }
            },
            "traceId": {
              "ignore_above": 64,
              "type": "keyword"
            },
            "traceState": {
              "type": "text",
              "fields": {
                "keyword": {
                  "type": "keyword",
                  "ignore_above": 1024
                }
              }
            }
          }
        }
      }
    },
    "default-otel-common-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "mappings": {
          "_source": {
            "enabled": true
          },
          "dynamic_templates": [
            {
              "instrumentation_scope_attributes_map": {
                "path_match": "instrumentationScope.attributes.*",
                "mapping": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            },
            {
              "resource_attributes_map": {
                "path_match": "resource.attributes.*",
                "mapping": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            }
          ],
          "date_detection": false,
          "properties": {
            "instrumentationLibrary" : {
              "properties" : {
                "name" : {
                  "type" : "keyword",
                  "ignore_above": 256
                },
                "version" : {
                  "type" : "keyword",
                  "ignore_above": 256
                }
              }
            },
            "instrumentationScope": {
              "properties": {
                "name": {
                  "type": "keyword",
                  "ignore_above": 256
                },
                "version": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            }
          }
        }
      }
    },
    "default-otel-sap-cf-resources-component": {
      "_meta": {
        "origin": "managed-content-default"
      },
      "template": {
        "mappings": {
          "properties": {
            "resource": {
              "properties": {
                "attributes": {
                  "properties": {
                    "sap@cf@app_id": {
                      "type": "keyword",
                      "ignore_above": 40
                    },
                    "sap@cf@app_name": {
                      "type": "keyword",
                      "ignore_above": 256
                    },
                    "sap@cf@instance_id": {
                      "type": "keyword",
                      "ignore_above": 4
                    },
                    "sap@cf@org_id": {
                      "type": "keyword",
                      "ignore_above": 40
                    },
                    "sap@cf@org_name": {
                      "type": "keyword",
                      "ignore_above": 256
                    },
                    "sap@cf@process@id": {
                      "type": "keyword",
                      "ignore_above": 40
                    },
                    "sap@cf@process@type": {
                      "type": "keyword",
                      "ignore_above": 20
                    },
                    "sap@cf@space_id": {
                      "type": "keyword",
                      "ignore_above": 40
                    },
                    "sap@cf@space_name": {
                      "type": "keyword",
                      "ignore_above": 256
                    }
                  }
                }
              }
            },
            "app_id": {
              "type": "alias",
              "path": "resource.attributes.sap@cf@app_id"
            },
            "app_name": {
              "type": "alias",
              "path": "resource.attributes.sap@cf@app_name"
            },
            "instance_id": {
              "type": "alias",
              "path": "resource.attributes.sap@cf@instance_id"
            },
            "organization_id": {
              "type": "alias",
              "path": "resource.attributes.sap@cf@org_id"
            },
            "organization_name": {
              "type": "alias",
              "path": "resource.attributes.sap@cf@org_name"
            },
            "process_id": {
              "type": "alias",
              "path": "resource.attributes.sap@cf@process@id"
            },
            "process_type": {
              "type": "alias",
              "path": "resource.attributes.sap@cf@process@type"
            },
            "space_id": {
              "type": "alias",
              "path": "resource.attributes.sap@cf@space_id"
            },
            "space_name": {
              "type": "alias",
              "path": "resource.attributes.sap@cf@space_name"
            }
          }
        }
      }
    }
  }
