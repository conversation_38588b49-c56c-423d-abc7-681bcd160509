_meta:
  type: "internalusers"
  config_version: 2

admin:
  reserved: true
  hash: {{ .Credentials.admin.Hash }}
  description: "admin user"

writer:
  reserved: true
  hash: {{ .Credentials.writer.Hash }}
  description: "user for log ingestion (fluentd) into opensearch"

otelWriter:
  reserved: true
  hash: {{ .Credentials.otelWriter.Hash }}
  description: "user for otel ingestion into opensearch"

configmanager:
  reserved: true
  hash: {{ .Credentials.configmanager.Hash }}
  description: "user for operator access to opensearch"

# deprecated 
# still in use, but should be replaced with, e.g., "osdServer" user
kibanaserver:
  reserved: true
  hash: {{ .Credentials.kibanaserver.Hash }}
  description: "opensearch dashboards user"

# deprecated
# still in use, but should be removed entirely
statuser:
  reserved: true
  hash: {{ .Credentials.statuser.Hash }}
  description: "user for operator access to opensearch"

# deprecated
# still in use, but should be replaced with something else
esexporter:
  reserved: true
  hash: {{ .Credentials.esexporter.Hash }}
  description: "esexporter user"
