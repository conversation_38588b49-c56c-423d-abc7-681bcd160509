retention-policy: |-
  {
    "description": "[maintained by SAP] This policy deletes managed indices (logs-*, metrics-*, cls-rejected-*) according to the set retentionPeriod service parameter. Do not change the retention period by editing this policy, but instead set it via service instance update (see documentation). This policy gets updated automatically after a few minutes.",
    "default_state": "hot",
    "states": [
      {
        "name": "hot",
        "actions": [],
        "transitions": [
          {
            "state_name": "delete",
            "conditions": {
              "min_index_age": "{{ .RetentionPeriodDays }}d"
            }
          }
        ]
      },
      {
        "name": "delete",
        "actions": [
          {
            "retry": {
              "count": 3,
              "backoff": "exponential",
              "delay": "1m"
            },
            "delete": {}
          }
        ],
        "transitions": []
      }
    ],
    "ism_template": [
      {
        "index_patterns": [
          "logs-*",
          "metrics-*",
          "cls-rejected-*"
        ],
        "priority": 10,
        "last_updated_time": 1674124646430
      }
    ]
  }
security-auditlog-retention-policy: |-
  {
    "description": "[maintained by SAP] This policy deletes managed indices (security-auditlog-*) after 7 days. Do not change the retention period by editing this policy, but instead provide a custom policy with a higher priority (see documentation).",
    "default_state": "hot",
    "states": [
      {
        "name": "hot",
        "actions": [],
        "transitions": [
          {
            "state_name": "delete",
            "conditions": {
              "min_index_age": "7d"
            }
          }
        ]
      },
      {
        "name": "delete",
        "actions": [
          {
            "retry": {
              "count": 3,
              "backoff": "exponential",
              "delay": "1m"
            },
            "delete": {}
          }
        ],
        "transitions": []
      }
    ],
    "ism_template": [
      {
        "index_patterns": [
          "security-auditlog-*"
        ],
        "priority": 10,
        "last_updated_time": 1674124646430
      }
    ]
  }
otel-raw-span-policy: |-
  {
    "description": "[maintained by SAP] This policy is managing raw spans for trace analytics (index: otel-v1-apm-span-*). It performs index rollovers and deletes the managed indices according to the set retentionPeriod service parameter. Do not change the retention period by editing this policy, but instead set it via service instance update (see documentation). This policy gets updated automatically after a few minutes.",
    "default_state": "current_write_index",
    "states": [
      {
        "name": "current_write_index",
        "actions": [
          {
            "retry": {
              "count": 3,
              "backoff": "exponential",
              "delay": "1m"
            },
            "rollover": {
              {{- if ge .SupportVersion.Major 2 }}
              "copy_alias": false,
              {{- end }}
              "min_primary_shard_size": "{{ .RolloverMinPrimaryShardSize }}",
              "min_index_age": "{{ .RolloverMinIndexAge }}"
            }
          }
        ],
        "transitions": [
          {
            "state_name": "warm"
          }
        ]
      },
      {
        "name": "warm",
        "actions": [],
        "transitions": [
          {
            "state_name": "delete",
            "conditions": {
              "min_index_age": "{{ .RetentionPeriodDays }}d"
            }
          }
        ]
      },
      {
        "name": "delete",
        "actions": [
          {
            "retry": {
              "count": 3,
              "backoff": "exponential",
              "delay": "1m"
            },
            "delete": {}
          }
        ],
        "transitions": []
      }
    ],
    "ism_template": [
      {
        "index_patterns": [
          "otel-v1-apm-span-*"
        ],
        "priority": 15,
        "last_updated_time": 1652878644000
      }
    ]
  }
