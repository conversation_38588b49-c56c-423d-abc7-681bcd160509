<source>
  @type http_backpressure
  @label @JSON
  @log_level info
  add_http_headers true
  <parse>
    @type json
    keep_time_key true
    time_key date
  </parse>
  port 24224
  bind 0.0.0.0
  keepalive_timeout 60s
  buffer_id json_buffer
  buffer_threshold_ratio 0.95 
</source>
<label @JSON>
  # fluentd metrics calculation
  <filter **>
    @type prometheus
    @id fluentd_json_input
    @log_level warn
    <metric>
      name fluentd_input_status_num_records_total
      type counter
      desc The total number of incoming records
      <labels>
        tag ${tag}
      </labels>
    </metric>
  </filter>
  # custom index handling (matching all that is not a metric)
  <filter **>
    @type custom_index
    prefix logs-json
    http_field CUSTOM_INDEX
    tag_field myindextag
    custom_index_identifier customindex
  </filter>
  <filter **>
    @type sap_passport
  </filter>
  <filter **>
    @type tracing_parser
    <w3c_tracing>
      sources traceparent|w3c_traceparent
      trace_field w3c_trace-id
      parent_field w3c_parent-id
      target_field w3c_traceparent
      delete_source true
    </w3c_tracing>
    <unified_correlation>
      source_order w3c|sap|default
      <trace>
        field trace_id
        <sources>
          w3c w3c_trace-id|unparsed
          sap sap_transaction_id|uuid:32
          default correlation_id|uuid:32|unparsed
        </sources>
      </trace>
      <span>
        field span_id
        <sources>
          w3c w3c_parent-id|unparsed
          sap sap_connection_id:sap_connection_counter|passport_span|unparsed
          default1 request_id|uuid:16:16|unparsed
          default2 vcap_request_id|uuid:16:16|unparsed
        </sources>
      </span>
    </unified_correlation>
  </filter>
  @include sanitize_json.conf
  <match **>
    @include output_settings.conf
    remove_keys myindextag
    <buffer tag, myindextag>
      @id json_buffer
      @include buffer_settings.conf
    </buffer>
  </match>
</label>
