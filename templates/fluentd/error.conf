<label @ERROR>
  # fluentd metrics calculation
  <filter **>
    @type prometheus
    @id fluentd_error_input
    @log_level warn
    <metric>
      name fluentd_num_errors_total
      type counter
      desc The total number of rejected records, to be written to the error index
      <labels>
        tag ${tag}
      </labels>
    </metric>
  </filter>
  # Custom error parser, stringifies records, and ingests them parse_errors
  <filter **>
    @type error_parser
    tag_field myindextag
    index_prefix cls-rejected
  </filter>
  <match **>
    @include output_settings.conf
    remove_keys myindextag
    emit_error_label_event false
    <buffer tag, myindextag>
      @include error_buffer_settings.conf
    </buffer>
  </match>
</label>
