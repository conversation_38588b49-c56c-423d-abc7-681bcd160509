<source>
  @type http
  @label @CFSYSLOG
  @log_level info
  port 24222
  bind 0.0.0.0
  keepalive_timeout 60s
  add_http_headers true
  <parse>
    @type syslog_batching
    expression /^\<(?<syslog_priority>[0-9]{1,3})\>[1-9]\d{0,2} (?<syslog_time>[^ ]+) (?<syslog_hostname>[^ ]+) (?<cf_app_id>[^ ]+) (?<source_type>[^ ]+) (?<syslog_msgid>[^ ]+) (?<syslog_extradata>(\[(.*?)\]|[^ ])) (?<message_payload>.+)$/
  </parse>
</source>
<label @CFSYSLOG>
  <filter **>
    @type prometheus
    @id fluentd_cfsyslog_input
    @log_level warn
    <metric>
      name fluentd_input_status_num_records_total
      type counter
      desc The total number of incoming records
      <labels>
        tag ${tag}
      </labels>
    </metric>
  </filter>
  # custom index handling
  <filter **>
    @type custom_index
    prefix logs-cfsyslog
    http_field custom_index
    tag_field myindextag
    custom_index_identifier customindex
  </filter>
  <match cfsyslog>
    @type rewrite_tag_filter
    emit_mode record
    <rule>
      key syslog_priority
      pattern /^.*/
      tag cfsyslog.${tag}
    </rule>
    <rule>
      key message
      pattern /^.*/
      tag unparsed.${tag}
    </rule>
  </match>
  # This filter covers different types of cloud foundry logs.
  # The log types are available at:
  # https://docs.cloudfoundry.org/devguide/deploy-apps/streaming-logs.html#format
  <filter cfsyslog.**>
    @type parser
    key_name message_payload
    reserve_data true
    <parse>
      @type multi_format
      <pattern>
        # Parse APP logs
        format json
      </pattern>
      <pattern>
        # Parse RTR logs
        format regexp
        expression /^(?<request_host>[^ ]*) (?<user>[^ ]*) \[(?<request_received_at>[^\]]*)\] "(?<method>\S+)(?: +(?<request>[^\"]*?)(?<protocol> +\S*)?)?" (?<response_status>[^ ]*) (?<request_size_b>[^ ]*) (?<response_size_b>[^ ]*) "(?<referer>[^\"]*)" "(?<user_agent>[^\"]*)" "(?<remote_address>[^\"]*)" "(?<dest_ip_and_port>[^\"]*)"(?<additional_data>.*)$/
        types response_status:integer,request_size_b:integer,response_size_b:integer
      </pattern>
      <pattern>
        # Parse API, STG, LGR, SSH, CELL logs
        format none
      </pattern>
    </parse>
    remove_key_name_field true
  </filter>
  # Drop custom metrics
  <filter cfsyslog.**>
    @type grep
    <exclude>
      key type
      pattern /^metrics$/
    </exclude>
  </filter>
  # Pre-process 'extradata' field for ltsv parser
  <filter cfsyslog.**>
    @type record_transformer
    enable_ruby true
    <record>
      # Remove opening and closing bracket from SD-ELEMENT
      # Assumption: STRUCTURED-DATA comprises only a single SD-ELEMENT
      syslog_extradata ${record["syslog_extradata"].delete_prefix("[").delete_suffix("]")}
    </record>
  </filter>
  # Parse field 'extradata' which corresponds to STRUCTURED-DATA of syslog message_payload using the ltsv parser
  <filter cfsyslog.**>
    @type parser
    key_name syslog_extradata # Parse the 'extradata' field
    reserve_data true # Keep other fields in record
    remove_key_name_field true # Remove the 'extradata' field after parsing
    <parse>
      @type ltsv_with_quotes
      delimiter ' '
      label_delimiter =
    </parse>
  </filter>
  <filter cfsyslog.**>
    @type record_transformer
    enable_ruby true
    remove_keys tags@47450
    <record>
      @timestamp ${record["written_at"] ? record["written_at"] : (record["syslog_time"] ? (record["syslog_time"][0..22] + "Z") : (time.strftime('%Y-%m-%dT%H:%M:%S.%L') + "Z"))}
      app_id ${record["app_id"] ? record["app_id"] : "-"}
      app_name ${record["app_name"] ? record["app_name"] : "-"}
      deployment ${record["deployment"] ? record["deployment"] : "-"}
      index ${record["index"] ? record["index"] : "-"}
      instance_id ${record["instance_id"] ? record["instance_id"] : "-"}
      ip ${record["ip"] ? record["ip"] : "-"}
      job ${record["job"] ? record["job"] : "-"}
      organization_id ${record["organization_id"] ? record["organization_id"] : "-"}
      organization_name ${record["organization_name"] ? record["organization_name"] : "-"}
      origin ${record["origin"] ? record["origin"] : "-"}
      process_id ${record["process_id"] ? record["process_id"] : "-"}
      process_instance_id ${record["process_instance_id"] ? record["process_instance_id"] : "-"}
      process_type ${record["process_type"] ? record["process_type"] : nil}
      type ${record["type"] ? record["type"] : "log"}
      source_id ${record["source_id"] ? record["source_id"] : "-"}
      source_type ${record["source_type"] ? record["source_type"] : "-"}
      space_id ${record["space_id"] ? record["space_id"] : "-"}
      space_name ${record["space_name"] ? record["space_name"] : "-"}
      __v1_type ${record["__v1_type"] ? record["__v1_type"] : "-"}
      component ${record["component"] ? record["component"] : "-"}
    </record>
  </filter>
  <match cfsyslog.**>
    @type rewrite_tag_filter
    emit_mode record
    <rule>
      # Parses additional_data field of router logs. In addition,
      # it is possible to provide additional_data directly in a json object.
      # The parsed additional_data is further transformed.
      key additional_data
      pattern /^.*/
      tag additional_data.${tag}
    </rule>
    <rule>
      # Default. No further transformation of records.
      key myindextag
      pattern /^.*/
      tag no_additional_data.${tag}
    </rule>
  </match>
  <filter additional_data.**>
    @type parser
    key_name additional_data # Parse the 'additional_data' field
    reserve_data true # Keep other fields in record
    remove_key_name_field true # Remove the 'additional_data' field after parsing
    <parse>
      @type ltsv_with_quotes
      delimiter ' '
      label_delimiter :
    </parse>
  </filter>
  <filter additional_data.**>
    @type record_transformer
    enable_ruby true
    <record>
      client_ip ${record["true_client_ip"] ? record["true_client_ip"] : "-"}
      x_forwarded_for ${record["x_forwarded_for"] ? record["x_forwarded_for"] : "-"}
      x_forwarded_proto ${record["x_forwarded_proto"] ? record["x_forwarded_proto"] : "-"}
      vcap_request_id ${record["vcap_request_id"] ? record["vcap_request_id"] : "-"}
      response_time_ms ${record["response_time"] ? record["response_time"].to_f * 1000 : -1.0}
      gorouter_time_ms ${record["gorouter_time"] ? record["gorouter_time"].to_f * 1000 : -1.0}
      failed_attempts_time_ms ${record["failed_attempts_time"] ? record["failed_attempts_time"].to_f * 1000 : -1.0}
      dns_time_ms ${record["dns_time"] ? record["dns_time"].to_f * 1000 : -1.0}
      dial_time_ms ${record["dial_time"] ? record["dial_time"].to_f * 1000 : -1.0}
      tls_time_ms ${record["tls_time"] ? record["tls_time"].to_f * 1000 : -1.0}
      backend_time_ms ${record["backend_time"] ? record["backend_time"].to_f * 1000 : -1.0}
      failed_attempts ${record["failed_attempts"] ? record["failed_attempts"].to_i : -1}
      app_id ${record["app_id"] ? record["app_id"] : "-"}
      app_index ${record["app_index"] ? record["app_index"] : "-"}
      x_cf_routererror ${record["x_cf_routererror"] ? record["x_cf_routererror"] : "-"}
      correlation_id ${record["x_correlationid"] ? record["x_correlationid"] : "-"}
      tenant_id ${record.has_key?("tenant_id") ? record["tenant_id"] : (record.has_key?("tenantid") ? record["tenantid"] : "-")}
      sap_passport ${record["sap_passport"] ? record["sap_passport"] : "-"}
      x_scp_request_id ${record["x_scp_request_id"] ? record["x_scp_request_id"] : "-"}
      x_cf_app_instance ${record["x_cf_app_instance"] ? record["x_cf_app_instance"] : "-"}
      x_b3_traceid ${record["x_b3_traceid"] ? record["x_b3_traceid"] : "-"}
      x_b3_spanid ${record["x_b3_spanid"] ? record["x_b3_spanid"] : "-"}
      x_b3_parentspanid ${record["x_b3_parentspanid"] ? record["x_b3_parentspanid"] : "-"}
      b3 ${record["b3"] ? record["b3"] : "-"}
      traceparent ${record["traceparent"] ? record["traceparent"] : "-"}
      type ${ (record["source_type"].include? "RTR") ? "request" : record["type"]}
    </record>
  </filter>
  <filter **>
    @type sap_passport
  </filter>
  <filter **>
    @type tracing_parser
    <w3c_tracing>
      sources traceparent|w3c_traceparent
      trace_field w3c_trace-id
      parent_field w3c_parent-id
      target_field w3c_traceparent
      delete_source true
    </w3c_tracing>
    <unified_correlation>
      source_order w3c|sap|default
      <trace>
        field trace_id
        <sources>
          w3c w3c_trace-id|unparsed
          sap sap_transaction_id|uuid:32
          default1 correlation_id|uuid:32|unparsed
          default2 x_b3_traceid|unparsed
          default3 b3_traceid|unparsed
        </sources>
      </trace>
      <span>
        field span_id
        <sources>
          w3c w3c_parent-id|unparsed
          sap sap_connection_id:sap_connection_counter|passport_span|unparsed
          default1 request_id|uuid:16:16|unparsed
          default2 vcap_request_id|uuid:16:16|unparsed
          default3 x_request_id|uuid:16:16|unparsed
        </sources>
      </span>
    </unified_correlation>
  </filter>
  @include sanitize_syslog.conf
  <match **>
    @include output_settings.conf
    remove_keys myindextag
    <buffer tag, myindextag>
      @include buffer_settings.conf
    </buffer>
  </match>
</label>
