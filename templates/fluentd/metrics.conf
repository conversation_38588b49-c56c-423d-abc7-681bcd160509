<source>
  @type http
  @label @METRICS
  @log_level info
  <parse>
    @type json
  </parse>
  port 24226
  bind 0.0.0.0
  keepalive_timeout 60s
</source>
# main metrics parsing
<label @METRICS>
  <match metrics.telegraf.**>
    @type metrics_parser
  </match>
</label>
<label telegraf>
  <filter **>
    @type custom_index
    prefix metrics
    tag_field myindextag
    custom_index_identifier metrics
  </filter>
  <match **>
    @include output_settings.conf
    remove_keys myindextag
    <buffer tag, myindextag>
      @include buffer_settings.conf
    </buffer>
  </match>
</label>
