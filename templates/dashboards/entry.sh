#!/bin/bash
set -euo pipefail

log() {
    echo "$(date -u) entry.sh: $1"
}

{{- if ge .Version.Major 2 }}
log "Fetching list of available plugins..."

./bin/opensearch-dashboards-plugin list

log "Disabling plugins..."

./bin/opensearch-dashboards-plugin remove mlCommonsDashboards
./bin/opensearch-dashboards-plugin remove assistantDashboards
./bin/opensearch-dashboards-plugin remove customImportMapDashboards
./bin/opensearch-dashboards-plugin remove flowFrameworkDashboards
./bin/opensearch-dashboards-plugin remove searchRelevanceDashboards
./bin/opensearch-dashboards-plugin remove queryInsightsDashboards

log "Fetching list of plugins..."

./bin/opensearch-dashboards-plugin list

{{- end }}

log "Starting OpenSearch Dashboards default entry-point /usr/share/opensearch/opensearch-dashboards-docker-entrypoint.sh"
set +euo pipefail
source /usr/share/opensearch-dashboards/opensearch-dashboards-docker-entrypoint.sh
