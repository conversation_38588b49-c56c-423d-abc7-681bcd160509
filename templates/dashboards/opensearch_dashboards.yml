server.host: "0.0.0.0"
opensearch.hosts:
  - https://opensearch-client:9200
opensearch.ssl.verificationMode: none
opensearch.username: "{{ .OpenSearchCredentials.Username }}"
opensearch.password: "{{ .OpenSearchCredentials.Password }}"
opensearch.requestHeadersWhitelist: ["securitytenant","Authorization"]

opensearch_security.multitenancy.enabled: true
opensearch_security.multitenancy.tenants.preferred: ["Global"]
opensearch_security.readonly_mode.roles: ["kibana_read_only"]
opensearch_security.auth.unauthenticated_routes: []

opensearchDashboards.branding:
  logo:
    defaultUrl: "https://{{ .Host }}/ui/assets/cls/customlogo.svg"
  applicationTitle: "SAP Cloud Logging"

# Use this setting if you are running kibana without https
opensearch_security.cookie.secure: false
vega.enableExternalUrls: true

# To configure a WMS map server for use with Kibana, see:
# https://opendistro.github.io/for-elasticsearch-docs/docs/kibana/maptiles/
map.includeElasticMapsService: false
  
  {{- if .OpenSearch.AuthSAML }}
  {{- if (eq .OpenSearch.AuthSAML.Enabled true) }}
opensearch_security.auth.type: "saml"
  {{- if (eq .OpenSearch.AuthSAML.Initiated true) }}
server.xsrf.whitelist: ["/_opendistro/_security/saml/acs/idpinitiated", "/_opendistro/_security/saml/acs", "/_opendistro/_security/saml/logout"]
  {{- else }}
server.xsrf.whitelist: ["/_opendistro/_security/saml/acs", "/_opendistro/_security/saml/logout"]
  {{- end }}
  {{- end }}
  {{- end }}

  {{- if .OpenSearch.AuthOpenID }}
  {{- if (eq .OpenSearch.AuthOpenID.Enabled true) }}
opensearch_security.auth.type: "openid"
opensearch_security.openid.connect_url: "{{ .OpenSearch.AuthOpenID.OpenIDConnectURL }}"
opensearch_security.openid.client_id: "{{ .OpenSearch.AuthOpenID.OpenIDClientID }}"
opensearch_security.openid.client_secret: "{{ .OpenSearch.AuthOpenID.OpenIDClientSecret }}"
opensearch_security.openid.scope: "{{ .OpenSearch.AuthOpenID.OpenIDScopes }}"
opensearch_security.openid.base_redirect_url: "https://{{ .Host }}"
  {{- end }}
  {{- end }}
