/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1beta1

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// Instance is the Schema for the instances API
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:printcolumn:name="plan",type="string",JSONPath=".metadata.labels.plan_name"
// +kubebuilder:printcolumn:name="lifecycleStatus",type="string",JSONPath=".status.lifecycleStatus"
// +kubebuilder:printcolumn:name="healthStatus",type="string",JSONPath=".status.healthStatus"
// +kubebuilder:printcolumn:name="age",type="date",JSONPath=".metadata.creationTimestamp"
type Instance struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   InstanceSpec   `json:"spec,omitempty"`
	Status InstanceStatus `json:"status,omitempty"`
}

// InstanceSpec defines the desired state of Instance
type InstanceSpec struct {
	OpenSearch              OpenSearch         `json:"openSearch,omitempty"`
	InstanceMaintainer      InstanceMaintainer `json:"instanceMaintainer,omitempty"`
	DataPrepper             DataPrepper        `json:"dataPrepper,omitempty"`
	Fluentd                 Fluentd            `json:"fluentd,omitempty"`
	Context                 Context            `json:"context,omitempty"`
	RotateRootCA            bool               `json:"rotateRootCA,omitempty"`
	SkipReconciliationUntil string             `json:"skipReconciliationUntil,omitempty"`
	FeatureFlags            string             `json:"featureFlags,omitempty"`
}

// InstanceStatus defines the observed state of Instance
// @ToDo failed state is not reflected
type InstanceStatus struct {
	// +listType=map
	// +listMapKey=type
	// +patchStrategy=merge
	// +patchMergeKey=type
	// +optional
	//nolint:golines // broken due to patchStrategy and patchMergeKey tags
	Conditions []metav1.Condition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type"`
	// +optional
	LifecycleStatus LifecycleStatus `json:"lifecycleStatus,omitempty"`
	// +optional
	LifecycleReason string `json:"lifecycleReason,omitempty"`
	// +optional
	HealthStatus HealthStatus `json:"healthStatus,omitempty"`
	// OpenSearchClusterSupportVersion is the lowest node version running in the OS cluster. Depending components
	// and contents need to be compatible with this version.
	// +optional
	// +kubebuilder:validation:Pattern=`^\d+(\.\d+){1,2}$`
	OpenSearchClusterSupportVersion string `json:"openSearchClusterSupportVersion,omitempty"`
	// OpenSearchClusterTargetVersion is the target OpenSearch version that is either being deployed or
	// is being upgraded to.
	// +optional
	// +kubebuilder:validation:Pattern=`^\d+(\.\d+){1,2}$`
	OpenSearchClusterTargetVersion string `json:"openSearchClusterTargetVersion,omitempty"`
}

// LifecycleStatus keeps the status of the instance related to lifecycle operations (create, update, delete)
type LifecycleStatus string

// HealthStatus keeps the status of the instance based on component conditions
type HealthStatus string

// OpenSearch defines the desired state of the OpenSearch components
type OpenSearch struct {
	Cluster         Cluster         `json:"cluster"`
	Dashboards      Dashboards      `json:"dashboards"`
	AuthSAML        AuthSAML        `json:"authSAML,omitempty"`
	AuthOpenID      AuthOpenID      `json:"authOpenID,omitempty"`
	IndexManagement IndexManagement `json:"indexManagement,omitempty"`
}

// Cluster defines the desired state of the OpenSearch cluster
type Cluster struct {
	// +optional
	Name string `json:"name"`
	// +listType=map
	// +listMapKey=name
	// +optional
	NodePools []NodePool `json:"nodePools"`

	// ExposeHttpEndpoint defines if the OpenSearch HTTP endpoint should be exposed via an ingress to the internet.
	// +optional
	ExposeHttpEndpoint bool `json:"exposeHttpEndpoint,omitempty"`

	// MaxDataNodes is the upper limit for the number of data nodes in the cluster which is configurable by the customer.
	// It is considered for the autoscalers of the data node pools. If there are multiple data node pools (for example,
	// in case of small plan), fixed scale pools take precedence over scaling pools.
	MaxDataNodes int32 `json:"maxDataNodes,omitempty"`

	// AdditionalConfig is a map of additional configuration settings that can be passed to the OpenSearch cluster.
	AdditionalConfig map[string]string `json:"additionalConfig,omitempty"`
}

type NodePool struct {
	Name        string                      `json:"name"`
	Autoscaler  DiskUsageBasedAutoScaler    `json:"autoscaler,omitempty"`
	Resources   corev1.ResourceRequirements `json:"resources,omitempty"`
	Jvm         string                      `json:"jvm,omitempty"`
	Roles       []NodeRole                  `json:"roles"`
	Persistence Persistence                 `json:"persistence,omitempty"`
}

type Autoscaler struct {
	// MinReplicas is the lower limit for the number of replicas to which the autoscaler
	// can scale down. It defaults to 0 pods.
	// +optional
	// +kubebuilder:default=0
	// +kubebuilder:validation:Minimum:=0
	MinReplicas int32 `json:"minReplicas"`

	// MaxReplicas is the upper limit for the number of replicas to which the autoscaler can scale up.
	// It cannot be less that minReplicas and defaults to 0 pods.
	// +optional
	// +kubebuilder:default=0
	// +kubebuilder:validation:Minimum:=0
	MaxReplicas int32 `json:"maxReplicas"`
}

type DiskUsageBasedAutoScaler struct {
	Autoscaler `json:",inline"`

	// DiskUsageThreshold is the threshold for disk usage percentage to trigger scaling.
	// If not set (or set to 0), the autoscaler will not scale based on disk usage.
	// +optional
	// +kubebuilder:validation:Minimum:=0
	// +kubebuilder:validation:Maximum:=100
	DiskUsageThreshold int32 `json:"diskUsageThreshold"`
}

// NodeRole is the role of a node in the OpenSearch cluster.
// +kubebuilder:validation:Enum=cluster_manager;data;ingest;search;remote_cluster_client
type NodeRole string

type Persistence struct {
	// DiskSize is the size of the disk for persistence.
	// +optional
	DiskSize resource.Quantity `json:"diskSize,omitempty"`
}

const (
	ClusterManager      NodeRole = "cluster_manager"
	Data                NodeRole = "data"
	Ingest              NodeRole = "ingest"
	Search              NodeRole = "search"
	RemoteClusterClient NodeRole = "remote_cluster_client"
)

const (
	InProgress LifecycleStatus = "InProgress"
	Succeeded  LifecycleStatus = "Succeeded"
	Failed     LifecycleStatus = "Failed"
	InDeletion LifecycleStatus = "InDeletion"
)

const (
	Up   HealthStatus = "Up"
	Down HealthStatus = "Down"
)

type Dashboards struct {
	Replicas    int32                       `json:"replicas"`
	Resources   corev1.ResourceRequirements `json:"resources,omitempty"`
	CustomLabel string                      `json:"customLabel,omitempty"`
}

type AuthSAML struct {
	Enabled     bool   `json:"enabled,omitempty"`
	AdminGroup  string `json:"adminGroup,omitempty"`
	RolesKey    string `json:"rolesKey,omitempty"`
	Initiated   bool   `json:"initiated,omitempty"`
	Idp         Idp    `json:"idp,omitempty"`
	Sp          Sp     `json:"sp,omitempty"`
	ExchangeKey string `json:"exchangeKey,omitempty"`
}

type Idp struct {
	MetadataUrl     string `json:"metadataUrl,omitempty"`
	EntityId        string `json:"entityId,omitempty"`
	EnabledSSL      bool   `json:"enableSSL,omitempty"`
	VerifyHostnames bool   `json:"verifyHostnames,omitempty"`
}

type Sp struct {
	EntityId                    string `json:"entityId,omitempty"`
	SignaturePrivateKey         string `json:"signaturePrivateKey,omitempty"`
	SignaturePrivateKeyPassword string `json:"signaturePrivateKeyPassword,omitempty"`
}

type AuthOpenID struct {
	Enabled              bool   `json:"enabled,omitempty"`
	RolesKey             string `json:"rolesKey,omitempty"`
	AdminGroup           string `json:"adminGroup,omitempty"`
	SubjectKey           string `json:"subjectKey,omitempty"`
	OpenIDConnectURL     string `json:"openIDConnectURL,omitempty"`
	OpenIDScopes         string `json:"openIDScopes,omitempty"`
	OpenIDClientID       string `json:"openIDClientID,omitempty"`
	OpenIDClientSecret   string `json:"openIDClientSecret,omitempty"`
	VerifyHostnames      bool   `json:"verifyHostnames,omitempty"`
	PEMTrustedCASContent string `json:"pemTrustedCASContent,omitempty"`
}

type IndexManagement struct {
	// RetentionPeriod is the time period after which we delete data indices from the cluster.
	// +kubebuilder:validation:Minimum:=1
	// +kubebuilder:validation:Maximum:=90
	RetentionPeriodDays int32 `json:"retentionPeriodDays,omitempty"`

	// RolloverMinPrimaryShardSize is the minimum size of the primary shards of an data index before it is rolled over.
	RolloverMinPrimaryShardSize resource.Quantity `json:"rolloverMinPrimaryShardSize,omitempty"`

	// RolloverMinIndexAgeDays is the minimum age of an index before it is rolled over.
	RolloverMinIndexAge string `json:"rolloverMinIndexAge,omitempty"`
}

type InstanceMaintainer struct {
	Resources     corev1.ResourceRequirements `json:"resources,omitempty"`
	BackupEnabled bool                        `json:"backupEnabled,omitempty"`
}

type DataPrepper struct {
	// Enabled indicates whether Data Prepper should be deployed or not.
	// Defaults to false
	// +optional
	Enabled          bool                        `json:"enabled"`
	Replicas         int32                       `json:"replicas"`
	Resources        corev1.ResourceRequirements `json:"resources,omitempty"`
	AdditionalConfig map[string]string           `json:"additionalConfig,omitempty"`
}

type Fluentd struct {
	// Enabled indicates whether Fluentd should be deployed or not.
	// Defaults to true
	// +optional
	Enabled bool `json:"enabled"`

	// MaxIngestInstances is the upper limit for the number of Fluentd instances which is configurable by the customer.
	MaxIngestInstances int32 `json:"maxIngestInstances,omitempty"`

	Autoscaler       Autoscaler                  `json:"autoscaler,omitempty"`
	Resources        corev1.ResourceRequirements `json:"resources,omitempty"`
	AdditionalConfig map[string]string           `json:"additionalConfig,omitempty"`
}

type Context struct {
	ClusterName string `json:"clusterName,omitempty"`
	LicenseType string `json:"licenseType,omitempty"`
}

// InstanceList contains a list of Instance
// +kubebuilder:object:root=true
type InstanceList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`

	Items []Instance `json:"items"`
}

func init() {
	SchemeBuilder.Register(&Instance{}, &InstanceList{})
}
