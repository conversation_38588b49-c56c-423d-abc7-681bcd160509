/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"crypto/tls"
	"flag"
	"net/http"
	"os"
	"time"

	//+kubebuilder:scaffold:imports

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/controller"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers/helpers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/scaling"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients"
	"github.tools.sap/perfx/cloud-logging-operator/internal/status"
	"github.tools.sap/perfx/cloud-logging-operator/internal/ticker"

	// Import all Kubernetes client auth plugins (e.g. Azure, GCP, OIDC, etc.)
	// to ensure that exec-entrypoint and run can make use of them.
	"github.com/cisco-open/operator-tools/pkg/reconciler"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	_ "k8s.io/client-go/plugin/pkg/client/auth"
	"k8s.io/klog/v2"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
	metricsserver "sigs.k8s.io/controller-runtime/pkg/metrics/server"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
)

func main() {
	scheme := runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(clv1beta1.AddToScheme(scheme))
	//+kubebuilder:scaffold:scheme

	setupLog := ctrl.Log.WithName("setup")

	var configFile string

	flag.StringVar(&configFile, "config", "config.yaml", "path to the configuration file")

	opts := zap.Options{}
	opts.BindFlags(flag.CommandLine)
	flag.Parse()

	ctrl.SetLogger(zap.New(zap.UseFlagOptions(&opts)))

	setupLog.Info("Howdy, this is cloud-logging-operator (CLOE)")

	// read configuration from config.yaml
	cfg := config.GetInstance()
	err := cfg.Read(configFile, false)
	if err != nil {
		setupLog.Error(err, "Unable to read configuration")
		os.Exit(1)
	}

	// validate configuration
	err = config.Validate()
	if err != nil {
		setupLog.Error(err, "Invalid configuration")
		os.Exit(1)
	}

	// If the enable-http2 flag is false (the default), http/2 should be disabled
	// due to its vulnerabilities. More specifically, disabling http/2 will
	// prevent from being vulnerable to the HTTP/2 Stream Cancellation and
	// Rapid Reset CVEs. For more information see:
	// - https://github.com/advisories/GHSA-qppj-fm5r-hxr3
	// - https://github.com/advisories/GHSA-4374-p667-p6c8
	disableHTTP2 := func(c *tls.Config) {
		setupLog.Info("disabling http/2")
		c.NextProtos = []string{"http/1.1"}
	}

	tlsOpts := []func(*tls.Config){
		disableHTTP2,
	}

	webhookServer := webhook.NewServer(webhook.Options{
		TLSOpts: tlsOpts,
	})

	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Logger: ctrl.Log.WithName("manager"),
		Scheme: scheme,
		Metrics: metricsserver.Options{
			BindAddress:   ":8080",
			SecureServing: false,
			TLSOpts:       tlsOpts,
		},
		WebhookServer:          webhookServer,
		HealthProbeBindAddress: ":8081",
		LeaderElection:         true,
		LeaderElectionID:       "cloud-logging-operator-leader",
		// LeaderElectionReleaseOnCancel defines if the leader should step down voluntarily
		// when the Manager ends. This requires the binary to immediately end when the
		// Manager is stopped, otherwise, this setting is unsafe. Setting this significantly
		// speeds up voluntary leader transitions as the new leader don't have to wait
		// LeaseDuration time first.
		//
		// In the default scaffold provided, the program ends immediately after
		// the manager stops, so would be fine to enable this option. However,
		// if you are doing or is intended to do any operation such as perform cleanups
		// after the manager stops then its usage might be unsafe.
		LeaderElectionReleaseOnCancel: true,
	})
	if err != nil {
		setupLog.Error(err, "Unable to start manager")
		os.Exit(1)
	}

	// align log format for packages using klog (e.g., client-go/tools/eaderelection)
	// related issue: https://github.com/kubernetes-sigs/controller-runtime/issues/2656
	klog.SetLogger(mgr.GetLogger())

	k8sClient := mgr.GetClient()

	httpClient := http.Client{
		Timeout: 5 * time.Second,
		Transport: &http.Transport{
			TLSHandshakeTimeout: 10 * time.Second,
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 5,
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // #nosec G402, required for OpenSearch client
			},
		},
	}

	apiClient := clients.NewAPIClient(httpClient)

	imClient := clients.NewInstanceMaintainerClient(apiClient)
	imService := services.NewInstanceMaintainerService(imClient)

	resourceReconciler := reconciler.NewReconcilerWith(k8sClient,
		reconciler.WithLog(ctrl.Log.WithName("resource-reconciler")))
	conditionHelper := conditions.NewConditionHelper()
	passwordStoreService := services.NewPasswordStoreService(k8sClient)

	allowedProdFeatureFlags := cfg.GetAllowedProdFeatureFlags()
	featureConfiguration := services.NewFeatureConfiguration(config.GetSupportedFeatureFlags(), allowedProdFeatureFlags)

	dashboardsClient := clients.NewDashboardsClient(apiClient)
	dashboardsService := services.NewDashboardsService(dashboardsClient, passwordStoreService)
	openSearchClient := clients.NewOpenSearchClient(apiClient)
	openSearchService := services.NewOpenSearchService(openSearchClient, passwordStoreService)

	realTimeProvider := services.NewRealTimeProvider()

	openSearchTopologyService := services.NewOpenSearchTopologyService(k8sClient)
	topologyHelper := helpers.NewTopologyHelper(openSearchTopologyService)

	imageHelper := helpers.NewImageHelper(featureConfiguration)

	componentReconcilers := []reconcilers.ComponentReconciler{
		reconcilers.NewInstanceMaintainerReconciler(k8sClient, conditionHelper,
			resourceReconciler, imService, featureConfiguration, "./templates/instance_maintainer"),
		reconcilers.NewDataPrepperReconciler(k8sClient, conditionHelper, resourceReconciler, passwordStoreService,
			"./templates/dataprepper"),
		reconcilers.NewFluentdReconciler(k8sClient, conditionHelper, resourceReconciler, passwordStoreService,
			"./templates/fluentd"),
		reconcilers.NewOpenSearchReconciler(k8sClient, conditionHelper, resourceReconciler, topologyHelper, imageHelper,
			passwordStoreService, openSearchService, openSearchTopologyService, featureConfiguration, "./templates/opensearch"),
		reconcilers.NewOpenSearchUpdateReconciler(k8sClient, conditionHelper, openSearchTopologyService, openSearchService),
		reconcilers.NewDashboardsReconciler(k8sClient, conditionHelper, dashboardsService,
			resourceReconciler, imageHelper, passwordStoreService, featureConfiguration, "./templates/dashboards"),
		reconcilers.NewElasticsearchExporterReconciler(k8sClient, conditionHelper, resourceReconciler, passwordStoreService,
			"./templates/elasticsearch_exporter"),
		reconcilers.NewIngestPkiReconciler(k8sClient, conditionHelper, resourceReconciler),
	}

	statusConditionUpdater := status.NewStatusConditionUpdater(k8sClient, componentReconcilers)
	statusConditionUpdaterTicker := ticker.NewRunnableTicker(k8sClient, statusConditionUpdater, 15*time.Second)

	openSearchAutoscaler := scaling.NewOpenSearchAutoscaler(k8sClient, openSearchTopologyService, openSearchService,
		conditionHelper, realTimeProvider)
	autoscalingTicker := ticker.NewRunnableTicker(k8sClient, openSearchAutoscaler, 5*time.Minute)

	if err = (controller.NewInstanceReconciler(k8sClient, mgr.GetScheme(), metrics.Registry,
		componentReconcilers)).SetupWithManager(mgr, passwordStoreService); err != nil {
		setupLog.Error(err, "Unable to create controller", "controller", "Instance")
		os.Exit(1)
	}

	//+kubebuilder:scaffold:builder

	if err = mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "Unable to set up health check")
		os.Exit(1)
	}
	if err = mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "Unable to set up ready check")
		os.Exit(1)
	}

	ctx := ctrl.SetupSignalHandler()

	setupLog.Info("Starting status updater")
	statusConditionUpdaterTicker.Start(ctx, mgr.Elected())

	setupLog.Info("Starting autoscaler for OpenSearch")
	autoscalingTicker.Start(ctx, mgr.Elected())

	setupLog.Info("Starting manager")
	if err = mgr.Start(ctx); err != nil {
		setupLog.Error(err, "Problem running manager")
		os.Exit(1)
	}
}
