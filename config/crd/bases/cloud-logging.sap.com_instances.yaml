---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: instances.cloud-logging.sap.com
spec:
  group: cloud-logging.sap.com
  names:
    kind: Instance
    listKind: InstanceList
    plural: instances
    singular: instance
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .metadata.labels.plan_name
      name: plan
      type: string
    - jsonPath: .status.lifecycleStatus
      name: lifecycleStatus
      type: string
    - jsonPath: .status.healthStatus
      name: healthStatus
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: age
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: Instance is the Schema for the instances API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: InstanceSpec defines the desired state of Instance
            properties:
              context:
                properties:
                  clusterName:
                    type: string
                  licenseType:
                    type: string
                type: object
              dataPrepper:
                properties:
                  additionalConfig:
                    additionalProperties:
                      type: string
                    type: object
                  enabled:
                    description: |-
                      Enabled indicates whether Data Prepper should be deployed or not.
                      Defaults to false
                    type: boolean
                  replicas:
                    format: int32
                    type: integer
                  resources:
                    description: ResourceRequirements describes the compute resource
                      requirements.
                    properties:
                      claims:
                        description: |-
                          Claims lists the names of resources, defined in spec.resourceClaims,
                          that are used by this container.

                          This is an alpha field and requires enabling the
                          DynamicResourceAllocation feature gate.

                          This field is immutable. It can only be set for containers.
                        items:
                          description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                          properties:
                            name:
                              description: |-
                                Name must match the name of one entry in pod.spec.resourceClaims of
                                the Pod where this field is used. It makes that resource available
                                inside a container.
                              type: string
                            request:
                              description: |-
                                Request is the name chosen for a request in the referenced claim.
                                If empty, everything from the claim is made available, otherwise
                                only the result of this request.
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Limits describes the maximum amount of compute resources allowed.
                          More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Requests describes the minimum amount of compute resources required.
                          If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                          otherwise to an implementation-defined value. Requests cannot exceed Limits.
                          More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                        type: object
                    type: object
                required:
                - replicas
                type: object
              featureFlags:
                type: string
              fluentd:
                properties:
                  additionalConfig:
                    additionalProperties:
                      type: string
                    type: object
                  autoscaler:
                    properties:
                      maxReplicas:
                        default: 0
                        description: |-
                          MaxReplicas is the upper limit for the number of replicas to which the autoscaler can scale up.
                          It cannot be less that minReplicas and defaults to 0 pods.
                        format: int32
                        minimum: 0
                        type: integer
                      minReplicas:
                        default: 0
                        description: |-
                          MinReplicas is the lower limit for the number of replicas to which the autoscaler
                          can scale down. It defaults to 0 pods.
                        format: int32
                        minimum: 0
                        type: integer
                    type: object
                  enabled:
                    description: |-
                      Enabled indicates whether Fluentd should be deployed or not.
                      Defaults to true
                    type: boolean
                  maxIngestInstances:
                    description: MaxIngestInstances is the upper limit for the number
                      of Fluentd instances which is configurable by the customer.
                    format: int32
                    type: integer
                  resources:
                    description: ResourceRequirements describes the compute resource
                      requirements.
                    properties:
                      claims:
                        description: |-
                          Claims lists the names of resources, defined in spec.resourceClaims,
                          that are used by this container.

                          This is an alpha field and requires enabling the
                          DynamicResourceAllocation feature gate.

                          This field is immutable. It can only be set for containers.
                        items:
                          description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                          properties:
                            name:
                              description: |-
                                Name must match the name of one entry in pod.spec.resourceClaims of
                                the Pod where this field is used. It makes that resource available
                                inside a container.
                              type: string
                            request:
                              description: |-
                                Request is the name chosen for a request in the referenced claim.
                                If empty, everything from the claim is made available, otherwise
                                only the result of this request.
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Limits describes the maximum amount of compute resources allowed.
                          More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Requests describes the minimum amount of compute resources required.
                          If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                          otherwise to an implementation-defined value. Requests cannot exceed Limits.
                          More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                        type: object
                    type: object
                type: object
              instanceMaintainer:
                properties:
                  backupEnabled:
                    type: boolean
                  resources:
                    description: ResourceRequirements describes the compute resource
                      requirements.
                    properties:
                      claims:
                        description: |-
                          Claims lists the names of resources, defined in spec.resourceClaims,
                          that are used by this container.

                          This is an alpha field and requires enabling the
                          DynamicResourceAllocation feature gate.

                          This field is immutable. It can only be set for containers.
                        items:
                          description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                          properties:
                            name:
                              description: |-
                                Name must match the name of one entry in pod.spec.resourceClaims of
                                the Pod where this field is used. It makes that resource available
                                inside a container.
                              type: string
                            request:
                              description: |-
                                Request is the name chosen for a request in the referenced claim.
                                If empty, everything from the claim is made available, otherwise
                                only the result of this request.
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Limits describes the maximum amount of compute resources allowed.
                          More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Requests describes the minimum amount of compute resources required.
                          If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                          otherwise to an implementation-defined value. Requests cannot exceed Limits.
                          More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                        type: object
                    type: object
                type: object
              openSearch:
                description: OpenSearch defines the desired state of the OpenSearch
                  components
                properties:
                  authOpenID:
                    properties:
                      adminGroup:
                        type: string
                      enabled:
                        type: boolean
                      openIDClientID:
                        type: string
                      openIDClientSecret:
                        type: string
                      openIDConnectURL:
                        type: string
                      openIDScopes:
                        type: string
                      pemTrustedCASContent:
                        type: string
                      rolesKey:
                        type: string
                      subjectKey:
                        type: string
                      verifyHostnames:
                        type: boolean
                    type: object
                  authSAML:
                    properties:
                      adminGroup:
                        type: string
                      enabled:
                        type: boolean
                      exchangeKey:
                        type: string
                      idp:
                        properties:
                          enableSSL:
                            type: boolean
                          entityId:
                            type: string
                          metadataUrl:
                            type: string
                          verifyHostnames:
                            type: boolean
                        type: object
                      initiated:
                        type: boolean
                      rolesKey:
                        type: string
                      sp:
                        properties:
                          entityId:
                            type: string
                          signaturePrivateKey:
                            type: string
                          signaturePrivateKeyPassword:
                            type: string
                        type: object
                    type: object
                  cluster:
                    description: Cluster defines the desired state of the OpenSearch
                      cluster
                    properties:
                      additionalConfig:
                        additionalProperties:
                          type: string
                        description: AdditionalConfig is a map of additional configuration
                          settings that can be passed to the OpenSearch cluster.
                        type: object
                      exposeHttpEndpoint:
                        description: ExposeHttpEndpoint defines if the OpenSearch
                          HTTP endpoint should be exposed via an ingress to the internet.
                        type: boolean
                      maxDataNodes:
                        description: |-
                          MaxDataNodes is the upper limit for the number of data nodes in the cluster which is configurable by the customer.
                          It is considered for the autoscalers of the data node pools. If there are multiple data node pools (for example,
                          in case of small plan), fixed scale pools take precedence over scaling pools.
                        format: int32
                        type: integer
                      name:
                        type: string
                      nodePools:
                        items:
                          properties:
                            autoscaler:
                              properties:
                                diskUsageThreshold:
                                  description: |-
                                    DiskUsageThreshold is the threshold for disk usage percentage to trigger scaling.
                                    If not set (or set to 0), the autoscaler will not scale based on disk usage.
                                  format: int32
                                  maximum: 100
                                  minimum: 0
                                  type: integer
                                maxReplicas:
                                  default: 0
                                  description: |-
                                    MaxReplicas is the upper limit for the number of replicas to which the autoscaler can scale up.
                                    It cannot be less that minReplicas and defaults to 0 pods.
                                  format: int32
                                  minimum: 0
                                  type: integer
                                minReplicas:
                                  default: 0
                                  description: |-
                                    MinReplicas is the lower limit for the number of replicas to which the autoscaler
                                    can scale down. It defaults to 0 pods.
                                  format: int32
                                  minimum: 0
                                  type: integer
                              type: object
                            jvm:
                              type: string
                            name:
                              type: string
                            persistence:
                              properties:
                                diskSize:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: DiskSize is the size of the disk for
                                    persistence.
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                              type: object
                            resources:
                              description: ResourceRequirements describes the compute
                                resource requirements.
                              properties:
                                claims:
                                  description: |-
                                    Claims lists the names of resources, defined in spec.resourceClaims,
                                    that are used by this container.

                                    This is an alpha field and requires enabling the
                                    DynamicResourceAllocation feature gate.

                                    This field is immutable. It can only be set for containers.
                                  items:
                                    description: ResourceClaim references one entry
                                      in PodSpec.ResourceClaims.
                                    properties:
                                      name:
                                        description: |-
                                          Name must match the name of one entry in pod.spec.resourceClaims of
                                          the Pod where this field is used. It makes that resource available
                                          inside a container.
                                        type: string
                                      request:
                                        description: |-
                                          Request is the name chosen for a request in the referenced claim.
                                          If empty, everything from the claim is made available, otherwise
                                          only the result of this request.
                                        type: string
                                    required:
                                    - name
                                    type: object
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - name
                                  x-kubernetes-list-type: map
                                limits:
                                  additionalProperties:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  description: |-
                                    Limits describes the maximum amount of compute resources allowed.
                                    More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                  type: object
                                requests:
                                  additionalProperties:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  description: |-
                                    Requests describes the minimum amount of compute resources required.
                                    If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                    otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                    More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                  type: object
                              type: object
                            roles:
                              items:
                                description: NodeRole
                                enum:
                                - cluster_manager
                                - data
                                - ingest
                                - search
                                - remote_cluster_client
                                type: string
                              type: array
                          required:
                          - name
                          - roles
                          type: object
                        type: array
                        x-kubernetes-list-map-keys:
                        - name
                        x-kubernetes-list-type: map
                    type: object
                  dashboards:
                    properties:
                      customLabel:
                        type: string
                      replicas:
                        format: int32
                        type: integer
                      resources:
                        description: ResourceRequirements describes the compute resource
                          requirements.
                        properties:
                          claims:
                            description: |-
                              Claims lists the names of resources, defined in spec.resourceClaims,
                              that are used by this container.

                              This is an alpha field and requires enabling the
                              DynamicResourceAllocation feature gate.

                              This field is immutable. It can only be set for containers.
                            items:
                              description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                              properties:
                                name:
                                  description: |-
                                    Name must match the name of one entry in pod.spec.resourceClaims of
                                    the Pod where this field is used. It makes that resource available
                                    inside a container.
                                  type: string
                                request:
                                  description: |-
                                    Request is the name chosen for a request in the referenced claim.
                                    If empty, everything from the claim is made available, otherwise
                                    only the result of this request.
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                          limits:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            description: |-
                              Limits describes the maximum amount of compute resources allowed.
                              More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                            type: object
                          requests:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            description: |-
                              Requests describes the minimum amount of compute resources required.
                              If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                              otherwise to an implementation-defined value. Requests cannot exceed Limits.
                              More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                            type: object
                        type: object
                    required:
                    - replicas
                    type: object
                  indexManagement:
                    properties:
                      retentionPeriodDays:
                        description: RetentionPeriod is the time period after which
                          we delete data indices from the cluster.
                        format: int32
                        maximum: 90
                        minimum: 1
                        type: integer
                      rolloverMinIndexAge:
                        description: RolloverMinIndexAgeDays is the minimum age of
                          an index before it is rolled over.
                        type: string
                      rolloverMinPrimaryShardSize:
                        anyOf:
                        - type: integer
                        - type: string
                        description: RolloverMinPrimaryShardSize is the minimum size
                          of the primary shards of an data index before it is rolled
                          over.
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                    type: object
                required:
                - cluster
                - dashboards
                type: object
              rotateRootCA:
                type: boolean
              skipReconciliationUntil:
                type: string
            type: object
          status:
            description: |-
              InstanceStatus defines the observed state of Instance
              @ToDo failed state is not reflected
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              healthStatus:
                description: HealthStatus keeps the status of the instance based on
                  component conditions
                type: string
              lifecycleReason:
                type: string
              lifecycleStatus:
                description: LifecycleStatus keeps the status of the instance related
                  to lifecycle operations (create, update, delete)
                type: string
              openSearchClusterSupportVersion:
                description: |-
                  OpenSearchClusterSupportVersion is the lowest node version running in the OS cluster. Depending components
                  and contents need to be compatible with this version.
                pattern: ^\d+(\.\d+){1,2}$
                type: string
              openSearchClusterTargetVersion:
                description: |-
                  OpenSearchClusterTargetVersion is the target OpenSearch version that is either being deployed or
                  is being upgraded to.
                pattern: ^\d+(\.\d+){1,2}$
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
