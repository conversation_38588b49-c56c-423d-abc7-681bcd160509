package reconcilers

import (
	"context"
	"errors"
	"fmt"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/templating"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	appsv1 "k8s.io/api/apps/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	FluentdReconcilerName = "FluentdReconciler"
	fluentdOSUsername     = "writer"
)

type FluentdReconciler struct {
	client              client.Client
	conditionHelper     conditions.ConditionHelper
	resourceReconciler  reconciler.ResourceReconciler
	passwordStoreReader services.PasswordStoreService
	templatedConfig     templating.TemplatedConfig
}

func NewFluentdReconciler(client client.Client, conditionHelper conditions.ConditionHelper,
	resourceReconciler reconciler.ResourceReconciler, passwordStoreReader services.PasswordStoreService,
	templatePath string) ComponentReconciler {
	return &FluentdReconciler{
		client:              client,
		conditionHelper:     conditionHelper,
		resourceReconciler:  resourceReconciler,
		passwordStoreReader: passwordStoreReader,
		templatedConfig: templating.NewTemplatedConfig(templatePath, "cfsyslog.conf", "error.conf", "fluent.conf",
			"json.conf", "metrics.conf", "monitoring.conf", "output_settings.conf", "buffer_settings.conf", "error_buffer_settings.conf", "sanitize_json.conf", "sanitize_syslog.conf"),
	}
}

func (r *FluentdReconciler) Reconcile(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	result := reconciler.CombinedResult{}

	// Check if the instance CR is lacking the required configuration.
	if instance.Spec.Fluentd.Resources.Requests.Cpu().IsZero() ||
		instance.Spec.Fluentd.MaxIngestInstances == 0 {
		result.CombineErr(errors.New("fluentd configuration is not available in instance CR"))
		r.conditionHelper.SetReconcileCondition(instance, conditions.FluentdReconciled, result)

		// Do not return an error here, as retrying would not help in this case
		return result.Result, nil
	}

	_, totalOk := instance.Spec.Fluentd.AdditionalConfig["output_total_limit_size"]
	_, chunkOk := instance.Spec.Fluentd.AdditionalConfig["output_chunk_limit_size"]
	if !totalOk || !chunkOk {
		result.CombineErr(errors.New("fluentd AdditionalConfig for 'output_chunk_limit_size'" +
			" or 'output_total_limit_size' are not available in instance CR"))
		r.conditionHelper.SetReconcileCondition(instance, conditions.FluentdReconciled, result)

		// Do not return an error here, as retrying would not help in this case
		return result.Result, nil
	}

	// Get credentials object from password-store
	osCredentials, err := r.passwordStoreReader.GetOpenSearchCredentials(ctx, instance, fluentdOSUsername)
	if err != nil {
		return ctrl.Result{}, err
	}

	// Get alias rollover enabled flag from cloud-logging-operator config
	aliasRolloverEnabled := config.GetInstance().GetAliasRolloverEnabled()

	// Source object for config templates
	configSource := struct {
		Fluentd              clv1beta1.Fluentd
		Credentials          models.Credentials
		AliasRolloverEnabled bool
	}{
		Fluentd:              instance.Spec.Fluentd,
		Credentials:          osCredentials,
		AliasRolloverEnabled: aliasRolloverEnabled,
	}

	// Reconcile fluentd related Kubernetes resources
	service := builders.BuildFluentdService(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, service, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(service, reconciler.StatePresent))

	ingress := builders.BuildFluentdIngress(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, ingress, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(ingress, reconciler.StatePresent))

	ingressMtls := builders.BuildFluentdIngressMtls(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, ingressMtls, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(ingressMtls, reconciler.StatePresent))

	// Only create, never update. Logging-operator is still responsible for its content.
	// TODO: When migrating the credential generation to the operator, consider updating this secrets
	// based on the password store. Don't forget to set the owner reference and use StatePresent.
	authSecret := builders.BuildFluentdIngressAuthSecret(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, authSecret, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(authSecret, reconciler.StateCreated))

	configSecret := builders.BuildFluentdConfigSecret(instance)
	result.CombineErr(r.templatedConfig.PopulateSecret(templating.PARSE_AS_FILES,
		configSource, configSecret))
	result.CombineErr(ctrl.SetControllerReference(instance, configSecret, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(configSecret, reconciler.StatePresent))

	autoscaler := builders.BuildFluentdAutoscaler(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, autoscaler, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(autoscaler, reconciler.StatePresent))

	// Only create/reconcile the PDB for instances with multiple fluentd pods
	podDisruptionBudget := builders.BuildFluentdPodDisruptionBudget(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, podDisruptionBudget, r.client.Scheme()))
	if instance.Spec.Fluentd.Autoscaler.MinReplicas > 1 {
		result.Combine(r.resourceReconciler.ReconcileResource(podDisruptionBudget, reconciler.StatePresent))
	} else {
		result.Combine(r.resourceReconciler.ReconcileResource(podDisruptionBudget, reconciler.StateAbsent))
	}

	deployment := builders.BuildFluentdDeployment(instance)
	result.CombineErr(utils.SetHashFromSecret(deployment.Spec.Template.Annotations, configSecret))
	result.CombineErr(ctrl.SetControllerReference(instance, deployment, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(deployment, reconciler.DynamicDesiredState{
		BeforeUpdateFunc: func(current, desired runtime.Object) error {
			// preserve the current number of replicas as set by the autoscaler
			//nolint:errcheck // desired and current are always of type *appsv1.Deployment
			desired.(*appsv1.Deployment).Spec.Replicas = current.(*appsv1.Deployment).Spec.Replicas
			return nil
		},
	}))

	// Set available conditions
	result.CombineErr(r.updateAvailableCondition(ctx, instance, deployment))

	// Set reconcile condition eventually
	r.conditionHelper.SetReconcileCondition(instance, conditions.FluentdReconciled, result)
	return result.Result, result.Err
}

func (r *FluentdReconciler) CleanUp(context.Context, *clv1beta1.Instance) (ctrl.Result, error) {
	// Nothing to do here, as the fluentd resources are owned by the instance
	return ctrl.Result{}, nil
}

// GetValidConditionNames returns a list of condition names related to the component.
// If the component can be disabled AND is disabled, only the '...Reconciled' condition should be returned.
func (r *FluentdReconciler) GetValidConditionNames(*clv1beta1.Instance) []string {
	validConditions := []string{
		conditions.FluentdReconciled,
		conditions.FluentdAvailable,
	}
	return validConditions
}

// UpdateStatusConditions updates the components status condition(s). This method is called frequently and
// should be lightweight. It should only update the local instance, which will be persisted by the watcher
// after all component reconcilers have been called.
func (r *FluentdReconciler) UpdateStatusConditions(context.Context, *clv1beta1.Instance) bool {
	// not (yet) implemented for Fluentd
	return false
}

// GetName returns the name of the reconciler for monitoring and logging purposes.
func (r *FluentdReconciler) GetName() string {
	return FluentdReconcilerName
}

//nolint:dupl // This availability condition differs from component to component
func (r *FluentdReconciler) updateAvailableCondition(ctx context.Context, instance *clv1beta1.Instance, deployment *appsv1.Deployment) error {
	// Determine ready status based on replica availability
	err := r.client.Get(ctx, client.ObjectKeyFromObject(deployment), deployment)
	if err != nil {
		if apierrors.IsNotFound(err) {
			r.conditionHelper.SetCondition(instance, metav1.Condition{
				Type:    conditions.FluentdAvailable,
				Status:  metav1.ConditionFalse,
				Reason:  conditions.NotFound,
				Message: "Deployment does not exist (yet)",
			})

			// Do not return an error in this case as rescheduling would not help
			// Immediately after the deployment is created we cannot expect it to exist already (async processing)
			return nil
		}
		return err
	}

	if deployment.Status.AvailableReplicas >= 1 {
		r.conditionHelper.SetCondition(instance, metav1.Condition{
			Type:    conditions.FluentdAvailable,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.Available,
			Message: fmt.Sprintf("%d of %d replica(s) are available", deployment.Status.AvailableReplicas, deployment.Status.Replicas),
		})
		return nil
	}
	r.conditionHelper.SetCondition(instance, metav1.Condition{
		Type:    conditions.FluentdAvailable,
		Status:  metav1.ConditionFalse,
		Reason:  conditions.NotAvailable,
		Message: "No replicas are available",
	})
	return nil
}
