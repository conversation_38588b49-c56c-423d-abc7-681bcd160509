package reconcilers

import (
	"context"
	"fmt"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/templating"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	elasticsearchExporterReconcilerName = "ElasticsearchExporterReconciler"
	esExporterOSUsername                = "esexporter"
)

type elasticsearchExporterReconciler struct {
	client               client.Client
	conditionHelper      conditions.ConditionHelper
	resourceReconciler   reconciler.ResourceReconciler
	passwordStoreService services.PasswordStoreService
	templatedSecret      templating.TemplatedConfig
}

func NewElasticsearchExporterReconciler(client client.Client, conditionHelper conditions.ConditionHelper,
	resourceReconciler reconciler.ResourceReconciler, passwordStoreService services.PasswordStoreService,
	templatePath string) ComponentReconciler {
	return &elasticsearchExporterReconciler{
		client:               client,
		conditionHelper:      conditionHelper,
		resourceReconciler:   resourceReconciler,
		passwordStoreService: passwordStoreService,
		templatedSecret:      templating.NewTemplatedConfig(templatePath, "credentials.txt"),
	}
}

func (r *elasticsearchExporterReconciler) Reconcile(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	result := reconciler.CombinedResult{}

	// Get credentials objects from password-store
	osCredentials, err := r.passwordStoreService.GetOpenSearchCredentials(ctx, instance, esExporterOSUsername)
	if err != nil {
		return ctrl.Result{}, err
	}

	// Source object for secret template
	configSource := struct {
		OpenSearchCredentials models.Credentials
	}{
		OpenSearchCredentials: osCredentials,
	}

	service := builders.BuildElasticsearchExporterService(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, service, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(service, reconciler.StatePresent))

	secret := builders.BuildElasticsearchExporterSecret(instance)
	result.CombineErr(r.templatedSecret.PopulateSecret(templating.PARSE_AS_FILES, configSource, secret))
	result.CombineErr(ctrl.SetControllerReference(instance, secret, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(secret, reconciler.StatePresent))

	deployment := builders.BuildElasticsearchExporterDeployment(instance)
	result.CombineErr(utils.SetHashFromSecret(deployment.Spec.Template.Annotations, secret))
	result.CombineErr(ctrl.SetControllerReference(instance, deployment, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(deployment, reconciler.StatePresent))

	// TODO: Move this to the NetworkPolicy reconciler (PERFX-7591)
	networkPolicy := builders.BuildElasticsearchExporterNetworkPolicy(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, networkPolicy, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(networkPolicy, reconciler.StatePresent))

	// Set available condition based on deployment status
	result.CombineErr(r.updateAvailableCondition(ctx, instance, deployment))

	// Set reconcile condition eventually
	r.conditionHelper.SetReconcileCondition(instance, conditions.ElasticsearchExporterReconciled, result)

	return result.Result, result.Err
}

func (r *elasticsearchExporterReconciler) CleanUp(context.Context, *clv1beta1.Instance) (ctrl.Result, error) {
	// Nothing to do here yet.
	return ctrl.Result{}, nil
}

// GetValidConditionNames returns a list of condition names related to the component.
// If the component can be disabled AND is disabled, only the '...Reconciled' condition should be returned.
func (r *elasticsearchExporterReconciler) GetValidConditionNames(*clv1beta1.Instance) []string {
	// Elasticsearch exporter is not relevant for the healthiness of teh service instance.
	// Therefore we do not want its conditions to be considered.
	return []string{}
}

// UpdateStatusConditions updates the components status condition(s). This method is called frequently and
// should be lightweight. It should only update the local instance, which will be persisted by the watcher
// after all component reconcilers have been called.
func (r *elasticsearchExporterReconciler) UpdateStatusConditions(context.Context, *clv1beta1.Instance) bool {
	return false
}

// GetName returns the name of the reconciler for monitoring and logging purposes.
func (r *elasticsearchExporterReconciler) GetName() string {
	return elasticsearchExporterReconcilerName
}

//nolint:dupl // This availability condition differs from component to component
func (r *elasticsearchExporterReconciler) updateAvailableCondition(ctx context.Context, instance *clv1beta1.Instance, deployment *appsv1.Deployment) error {
	// Determine ready status based on replica availability
	err := r.client.Get(ctx, client.ObjectKeyFromObject(deployment), deployment)
	if err != nil {
		if errors.IsNotFound(err) {
			r.conditionHelper.SetCondition(instance, metav1.Condition{
				Type:    conditions.ElasticsearchExporterAvailable,
				Status:  metav1.ConditionFalse,
				Reason:  conditions.NotFound,
				Message: "Deployment does not exist (yet)",
			})

			// Do not return an error in this case as rescheduling would not help
			// Immediately after the deployment is created we cannot expect it to exist already (async processing)
			return nil
		}
		return err
	}

	if deployment.Status.AvailableReplicas >= 1 {
		r.conditionHelper.SetCondition(instance, metav1.Condition{
			Type:    conditions.ElasticsearchExporterAvailable,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.Available,
			Message: fmt.Sprintf("%d of %d replica(s) are available", deployment.Status.AvailableReplicas, deployment.Status.Replicas),
		})
		return nil
	}

	r.conditionHelper.SetCondition(instance, metav1.Condition{
		Type:    conditions.ElasticsearchExporterAvailable,
		Status:  metav1.ConditionFalse,
		Reason:  conditions.NotAvailable,
		Message: "No replicas are available",
	})

	return nil
}
