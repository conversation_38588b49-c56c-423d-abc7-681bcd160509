package reconcilers_test

import (
	"context"
	"fmt"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	semverv3 "github.com/Masterminds/semver/v3"
	"github.com/cisco-open/operator-tools/pkg/reconciler"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	policyv1 "k8s.io/api/policy/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("OpenSearchReconciler", func() {
	const (
		instanceName           = "cls-test"
		namespace              = "sf-test"
		openSearchTemplatePath = "../../templates/opensearch"
	)

	var (
		instance                      *clv1beta1.Instance
		openSearchReconciler          reconcilers.ComponentReconciler
		resourceReconciler            reconciler.ResourceReconciler
		topologyHelperMock            *fake.TopologyHelperMock
		passwordStoreServiceMock      *fake.PasswordStoreServiceMock
		openSearchServiceMock         *fake.OpenSearchServiceMock
		openSearchTopologyServiceMock *fake.OpenSearchTopologyServiceMock
		featureConfigurationMock      *fake.FeatureConfigurationMock
		imageHelperMock               *fake.ImageHelperMock
		conditionHelper               = conditions.NewConditionHelper()
		ctx                           = context.Background()
		instanceNamespacedName        = types.NamespacedName{
			Name:      instanceName,
			Namespace: namespace,
		}
		sts1 *appsv1.StatefulSet
		sts2 *appsv1.StatefulSet
		hs1  *corev1.Service
		hs2  *corev1.Service
		pdb1 *policyv1.PodDisruptionBudget
		pdb2 *policyv1.PodDisruptionBudget
	)

	BeforeEach(func() {
		resourceReconciler = reconciler.NewReconcilerWith(k8sClient)
		topologyHelperMock = fake.NewTopologyHelperMock()
		openSearchTopologyServiceMock = fake.NewOpenSearchTopologyServiceMock()
		passwordStoreServiceMock = fake.NewPasswordStoreServiceMock()
		openSearchServiceMock = fake.NewOpenSearchServiceMock()
		imageHelperMock = fake.NewImageHelperMock()
		featureConfigurationMock = fake.NewFeatureConfigurationMock()

		Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:        instanceName,
				Namespace:   namespace,
				Annotations: map[string]string{},
			},
			Spec: clv1beta1.InstanceSpec{
				OpenSearch: clv1beta1.OpenSearch{
					Cluster: clv1beta1.Cluster{
						Name: "cls-test",
						NodePools: []clv1beta1.NodePool{
							{
								Name: "group-1",
								Autoscaler: clv1beta1.DiskUsageBasedAutoScaler{
									Autoscaler: clv1beta1.Autoscaler{
										MinReplicas: 3,
										MaxReplicas: 3,
									},
								},
								Resources: corev1.ResourceRequirements{},
								Roles:     []clv1beta1.NodeRole{clv1beta1.ClusterManager},
								Jvm:       "jvm-arguments",
								Persistence: clv1beta1.Persistence{
									DiskSize: resource.MustParse("1Gi"),
								},
							},
							{
								Name: "group-2",
								Autoscaler: clv1beta1.DiskUsageBasedAutoScaler{
									Autoscaler: clv1beta1.Autoscaler{
										MinReplicas: 2,
										MaxReplicas: 10,
									},
									DiskUsageThreshold: 60,
								},
								Resources: corev1.ResourceRequirements{},
								Roles:     []clv1beta1.NodeRole{clv1beta1.Data},
								Jvm:       "jvm-arguments",
								Persistence: clv1beta1.Persistence{
									DiskSize: resource.MustParse("100Gi"),
								},
							},
						},
						ExposeHttpEndpoint: true,
						MaxDataNodes:       4,
						AdditionalConfig: map[string]string{
							"rollover_min_primary_shard_size": "20Gi",
						},
					},
					IndexManagement: clv1beta1.IndexManagement{
						RetentionPeriodDays:         30,
						RolloverMinPrimaryShardSize: resource.MustParse("20Gi"),
						RolloverMinIndexAge:         "30d",
					},
				},
			},
		}

		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace,
			},
		}
		err := k8sClient.Get(ctx, types.NamespacedName{Name: namespace}, ns)
		if err != nil && apierrors.IsNotFound(err) {
			Expect(k8sClient.Create(ctx, ns)).To(Succeed())
		}

		err = k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{})
		if err != nil && apierrors.IsNotFound(err) {
			Expect(k8sClient.Create(ctx, instance)).To(Succeed())
		}

		// Prepare the StatefulSets for the OpenSearch instance
		sts1 = builders.BuildOpenSearchStatefulSet(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0], "opensearch:1.0.0")
		sts2 = builders.BuildOpenSearchStatefulSet(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1], "opensearch:1.0.0")
		sts1.Spec.Replicas = ptr.To(int32(3))
		sts2.Spec.Replicas = ptr.To(int32(2))

		// Prepare the PodDisruptionBudgets for the OpenSearch instance
		pdb1 = builders.BuildOpenSearchPodDisruptionBudget(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0])
		pdb2 = builders.BuildOpenSearchPodDisruptionBudget(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1])

		// Prepare the HeadlessServices for the OpenSearch instance
		hs1 = builders.BuildOpenSearchHeadlessService(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0])
		hs2 = builders.BuildOpenSearchHeadlessService(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1])

		topology := &models.Topology{
			NodeGroups: []models.NodeGroup{
				{
					NodePool:            &instance.Spec.OpenSearch.Cluster.NodePools[0],
					StatefulSet:         sts1,
					HeadlessService:     hs1,
					PodDisruptionBudget: pdb1,
				},
				{
					NodePool:            &instance.Spec.OpenSearch.Cluster.NodePools[1],
					StatefulSet:         sts2,
					HeadlessService:     hs2,
					PodDisruptionBudget: pdb2,
				},
			},
		}

		openSearchTopologyServiceMock.PrepareDiscover(topology, nil)

		// Prepare a topology patch for the OpenSearch instance
		topologyHelperMock.Prepare(models.TopologyPatch{
			Topology:         topology,
			AbsentNodeGroups: []models.NodeGroup{},
			TargetImage:      utils.VersionTuple{Version: semverv3.MustParse("1.0.0"), Image: "opensearch:1.0.0", Channel: "test"},
		}, nil)

		imageHelperMock.PrepareGetAvailableImages(map[string]string{
			"stable":      "docker.io/opensearchproject/opensearch:1.0.0",
			"preview":     "docker.io/opensearchproject/opensearch:2.0.0",
			"development": "docker.io/opensearchproject/opensearch:2.1.0",
		})

		openSearchReconciler = reconcilers.NewOpenSearchReconciler(k8sClient, conditionHelper, resourceReconciler,
			topologyHelperMock, imageHelperMock, passwordStoreServiceMock, openSearchServiceMock, openSearchTopologyServiceMock,
			featureConfigurationMock, openSearchTemplatePath)
	})

	AfterEach(func() {
		Expect(k8sClient.DeleteAllOf(ctx, &clv1beta1.Instance{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &appsv1.StatefulSet{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &appsv1.Deployment{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &policyv1.PodDisruptionBudget{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.ConfigMap{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Service{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Secret{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &networkingv1.Ingress{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.ServiceAccount{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &rbacv1.ClusterRole{})).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &rbacv1.ClusterRoleBinding{})).To(Succeed())
	})

	Context("Reconcile", func() {
		When("the OpenSearch configuration is present in the instance CR", func() {
			BeforeEach(func() {
				result, err := openSearchReconciler.Reconcile(ctx, instance)
				Expect(err).ToNot(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
			})

			It("should call the topology helper", func() {
				Expect(topologyHelperMock.IsProcessCalled).To(BeTrue())
				Expect(topologyHelperMock.Instance).To(Equal(instance))
			})

			It("should reconcile dynamic OpenSearch resources", func() {
				sts1 = &appsv1.StatefulSet{}
				err := k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-group-1", Namespace: namespace}, sts1)
				Expect(err).To(Not(HaveOccurred()))
				Expect(sts1.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(sts1.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(sts1.ObjectMeta.Labels["cluster-manager"]).To(Equal("true"))
				Expect(sts1.ObjectMeta.Labels["data"]).To(Equal("false"))
				Expect(sts1.ObjectMeta.Labels["app"]).To(Equal("opensearch"))
				Expect(sts1.Spec.Template.ObjectMeta.Annotations["operator.cloud-logging.sap.com/opensearch-config-hash"]).To(Not(BeEmpty()))

				sts2 = &appsv1.StatefulSet{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-group-2", Namespace: namespace}, sts2)
				Expect(err).To(Not(HaveOccurred()))
				Expect(sts2.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(sts2.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(sts2.ObjectMeta.Labels["cluster-manager"]).To(Equal("false"))
				Expect(sts2.ObjectMeta.Labels["data"]).To(Equal("true"))
				Expect(sts2.ObjectMeta.Labels["app"]).To(Equal("opensearch"))
				Expect(sts2.Spec.Template.ObjectMeta.Annotations["operator.cloud-logging.sap.com/opensearch-config-hash"]).To(Not(BeEmpty()))

				pdb1 = &policyv1.PodDisruptionBudget{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-group-1-pdb", Namespace: namespace}, pdb1)
				Expect(err).To(Not(HaveOccurred()))

				pdb2 = &policyv1.PodDisruptionBudget{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-group-2-pdb", Namespace: namespace}, pdb2)
				Expect(err).To(Not(HaveOccurred()))

				headlessService1 := &corev1.Service{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-group-1-headless", Namespace: namespace}, headlessService1)
				Expect(err).To(Not(HaveOccurred()))

				headlessService2 := &corev1.Service{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-group-2-headless", Namespace: namespace}, headlessService2)
				Expect(err).To(Not(HaveOccurred()))
			})

			It("should reconcile all static OpenSearch resources", func() {
				serviceAccount := &corev1.ServiceAccount{}
				err := k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-account", Namespace: namespace}, serviceAccount)
				Expect(err).To(Not(HaveOccurred()))
				Expect(serviceAccount.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(serviceAccount.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(serviceAccount.ObjectMeta.Labels["app"]).To(Equal("opensearch"))

				clusterRole := &rbacv1.ClusterRole{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-clusterrole"}, clusterRole)
				Expect(err).To(Not(HaveOccurred()))
				Expect(clusterRole.ObjectMeta.Labels["app"]).To(Equal("opensearch"))

				clusterRoleBinding := &rbacv1.ClusterRoleBinding{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: fmt.Sprintf("opensearch-clusterrolebinding-%s", namespace)}, clusterRoleBinding)
				Expect(err).To(Not(HaveOccurred()))
				Expect(clusterRoleBinding.ObjectMeta.Labels["app"]).To(Equal("opensearch"))

				configMap := &corev1.ConfigMap{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-config", Namespace: namespace}, configMap)
				Expect(err).To(Not(HaveOccurred()))
				Expect(configMap.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(configMap.OwnerReferences[0].Name).To(Equal("cls-test"))

				securitySecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-security", Namespace: namespace}, securitySecret)
				Expect(err).To(Not(HaveOccurred()))
				Expect(securitySecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(securitySecret.OwnerReferences[0].Name).To(Equal("cls-test"))

				discoveryService := &corev1.Service{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-discovery", Namespace: namespace}, discoveryService)
				Expect(err).To(Not(HaveOccurred()))
				Expect(discoveryService.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(discoveryService.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(discoveryService.ObjectMeta.Labels["app"]).To(Equal("opensearch"))
				Expect(discoveryService.ObjectMeta.Labels["cluster-manager"]).To(Equal("true"))
				Expect(discoveryService.Spec.Selector["app"]).To(Equal("opensearch"))
				Expect(discoveryService.Spec.Selector["cluster-manager"]).To(Equal("true"))

				legacyClientService := &corev1.Service{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-client", Namespace: namespace}, legacyClientService)
				Expect(err).To(Not(HaveOccurred()))
				Expect(legacyClientService.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(legacyClientService.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(legacyClientService.ObjectMeta.Labels["app"]).To(Equal("opensearch"))
				Expect(legacyClientService.Spec.Selector["app"]).To(Equal("opensearch"))

				clientService := &corev1.Service{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-client", Namespace: namespace}, clientService)
				Expect(err).To(Not(HaveOccurred()))
				Expect(clientService.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(clientService.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(clientService.ObjectMeta.Labels["app"]).To(Equal("opensearch"))
				Expect(clientService.Spec.Selector["app"]).To(Equal("opensearch"))

				composableTemplatesConfig := &corev1.ConfigMap{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-composable-templates-config", Namespace: namespace}, composableTemplatesConfig)
				Expect(err).To(Not(HaveOccurred()))
				Expect(composableTemplatesConfig.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(composableTemplatesConfig.OwnerReferences[0].Name).To(Equal("cls-test"))

				ismPoliciesConfig := &corev1.ConfigMap{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-ism-policies-config", Namespace: namespace}, ismPoliciesConfig)
				Expect(err).To(Not(HaveOccurred()))
				Expect(composableTemplatesConfig.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(composableTemplatesConfig.OwnerReferences[0].Name).To(Equal("cls-test"))

				ingress := &networkingv1.Ingress{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-ingress", Namespace: namespace}, ingress)
				Expect(err).To(Not(HaveOccurred()))
				Expect(ingress.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(ingress.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(ingress.ObjectMeta.Labels["app"]).To(Equal("opensearch"))
			})

			It("should set the reconciled condition", func() {
				condition := conditionHelper.GetCondition(instance, conditions.OpenSearchReconciled)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Succeeded))
				Expect(condition.Message).To(Equal("Successfully reconciled resources (image channel: test - 1.0.0)"))
			})

			It("should set the cluster target version in the CR", func() {
				Expect(instance.Status.OpenSearchClusterTargetVersion).To(Equal("1.0.0"))
			})

			When("all OpenSearch pods are available", func() {
				BeforeEach(func() {
					sts1.Status = appsv1.StatefulSetStatus{
						Replicas:      3,
						ReadyReplicas: 3,
					}
					Expect(k8sClient.Status().Update(ctx, sts1)).To(Succeed())

					sts2.Status = appsv1.StatefulSetStatus{
						Replicas:      2,
						ReadyReplicas: 2,
					}
					Expect(k8sClient.Status().Update(ctx, sts2)).To(Succeed())

					// Reconcile again to set the available condition
					result, err := openSearchReconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
				})

				It("should set the available condition", func() {
					condition := conditionHelper.GetCondition(instance, conditions.OpenSearchAvailable)
					Expect(condition.Status).To(Equal(metav1.ConditionTrue))
					Expect(condition.Reason).To(Equal(conditions.Available))
					Expect(condition.Message).To(Equal("3 of 3 elastic-group-1 replica(s) are available, 2 of 2 elastic-group-2 replica(s) are available"))
				})
			})

			When("a sufficient number of OpenSearch pods are available", func() {
				BeforeEach(func() {
					sts1.Status = appsv1.StatefulSetStatus{
						Replicas:      3,
						ReadyReplicas: 2,
					}
					Expect(k8sClient.Status().Update(ctx, sts1)).To(Succeed())

					sts2.Status = appsv1.StatefulSetStatus{
						Replicas:      2,
						ReadyReplicas: 2,
					}
					Expect(k8sClient.Status().Update(ctx, sts2)).To(Succeed())

					// Reconcile again to set the available condition
					result, err := openSearchReconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
				})

				It("should set the available condition", func() {
					condition := conditionHelper.GetCondition(instance, conditions.OpenSearchAvailable)
					Expect(condition.Status).To(Equal(metav1.ConditionTrue))
					Expect(condition.Reason).To(Equal(conditions.Available))
					Expect(condition.Message).To(Equal("2 of 3 elastic-group-1 replica(s) are available, 2 of 2 elastic-group-2 replica(s) are available"))
				})
			})

			When("an insufficient number of OpenSearch pods are available", func() {
				BeforeEach(func() {
					sts1.Status = appsv1.StatefulSetStatus{
						Replicas:      3,
						ReadyReplicas: 3,
					}
					Expect(k8sClient.Status().Update(ctx, sts1)).To(Succeed())

					sts2.Status = appsv1.StatefulSetStatus{
						Replicas:      2,
						ReadyReplicas: 0,
					}
					Expect(k8sClient.Status().Update(ctx, sts2)).To(Succeed())

					// Reconcile again to set the available condition
					result, err := openSearchReconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
				})

				It("should set the available condition", func() {
					condition := conditionHelper.GetCondition(instance, conditions.OpenSearchAvailable)
					Expect(condition.Status).To(Equal(metav1.ConditionFalse))
					Expect(condition.Reason).To(Equal(conditions.NotAvailable))
					Expect(condition.Message).To(Equal("3 of 3 elastic-group-1 replica(s) are available, 0 of 2 elastic-group-2 replica(s) are available"))
				})
			})

			When("the ingress is not enabled", func() {
				BeforeEach(func() {
					instance.Spec.OpenSearch.Cluster.ExposeHttpEndpoint = false

					result, err := openSearchReconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
				})

				It("should remove the ingress", func() {
					ingress := &networkingv1.Ingress{}
					err := k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-ingress", Namespace: namespace}, ingress)
					Expect(err).To(HaveOccurred())
				})
			})

			When("a NodePool is removed", func() {
				When("an associated PDB is available", func() {
					BeforeEach(func() {
						// Prepare a topology patch that removes the second NodePool
						topologyHelperMock.Prepare(models.TopologyPatch{
							Topology: &models.Topology{
								NodeGroups: []models.NodeGroup{
									{
										NodePool:            &instance.Spec.OpenSearch.Cluster.NodePools[0],
										StatefulSet:         sts1,
										HeadlessService:     hs1,
										PodDisruptionBudget: pdb1,
									},
								},
							},
							AbsentNodeGroups: []models.NodeGroup{
								{
									NodePool:            nil, // don't care about the NodePool here, as we simulate it's supposed to be absent.
									StatefulSet:         sts2,
									HeadlessService:     hs2,
									PodDisruptionBudget: pdb2,
								},
							},
							TargetImage: utils.VersionTuple{Version: semverv3.MustParse("1.0.0"), Image: "opensearch:1.0.0"},
						}, nil)

						// Reconcile again to remove the resource of the second NodePool
						result, err := openSearchReconciler.Reconcile(ctx, instance)
						Expect(err).ToNot(HaveOccurred())
						Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
					})

					It("should remove the StatefulSet, PodDisruptionBudget, and HeadlessService", func() {
						sts := &appsv1.StatefulSet{}
						err := k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-group-2", Namespace: namespace}, sts)
						Expect(err).To(HaveOccurred())

						pdb := &policyv1.PodDisruptionBudget{}
						err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-group-2-pdb", Namespace: namespace}, pdb)
						Expect(err).To(HaveOccurred())

						headlessService := &corev1.Service{}
						err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-group-2-headless", Namespace: namespace}, headlessService)
						Expect(err).To(HaveOccurred())
					})
				})

				When("no associated PDB is available", func() {
					BeforeEach(func() {
						// Prepare a topology patch that removes the second NodePool
						topologyHelperMock.Prepare(models.TopologyPatch{
							Topology: &models.Topology{
								NodeGroups: []models.NodeGroup{
									{
										NodePool:            &instance.Spec.OpenSearch.Cluster.NodePools[0],
										StatefulSet:         sts1,
										HeadlessService:     hs1,
										PodDisruptionBudget: pdb1,
									},
								},
							},
							AbsentNodeGroups: []models.NodeGroup{
								{
									NodePool:            nil, // don't care about the NodePool here, as we simulate it's supposed to be absent.
									StatefulSet:         sts2,
									HeadlessService:     hs2,
									PodDisruptionBudget: nil,
								},
							},
							TargetImage: utils.VersionTuple{Version: semverv3.MustParse("1.0.0"), Image: "opensearch:1.0.0"},
						}, nil)

						// Reconcile again to remove the resource of the second NodePool
						result, err := openSearchReconciler.Reconcile(ctx, instance)
						Expect(err).ToNot(HaveOccurred())
						Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
					})

					It("should remove the StatefulSet and HeadlessService", func() {
						sts := &appsv1.StatefulSet{}
						err := k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-group-2", Namespace: namespace}, sts)
						Expect(err).To(HaveOccurred())

						headlessService := &corev1.Service{}
						err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-group-2-headless", Namespace: namespace}, headlessService)
						Expect(err).To(HaveOccurred())
					})
				})
			})
		})

		When("some configuration is missing in the instance CR", func() {
			BeforeEach(func() {
				instance.Spec.OpenSearch = clv1beta1.OpenSearch{}

				result, err := openSearchReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
			})

			It("should not requeue and not reconcile resources", func() {
				sts := &appsv1.StatefulSet{}
				err := k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-group-1", Namespace: namespace}, sts)
				Expect(err).To(HaveOccurred())

				cm := &corev1.ConfigMap{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "opensearch-config", Namespace: namespace}, cm)
				Expect(err).To(HaveOccurred())
			})
		})
	})

	Context("CleanUp", func() {
		BeforeEach(func() {
			clusterRoleBinding := builders.BuildOpenSearchClusterRoleBinding(instance)
			Expect(k8sClient.Create(ctx, clusterRoleBinding)).To(Succeed())
		})

		It("should remove the ClusterRoleBinding", func() {
			result, err := openSearchReconciler.CleanUp(ctx, instance)
			Expect(err).NotTo(HaveOccurred())
			Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
		})
	})

	Context("GetValidConditionNames", func() {
		It("should return a list of valid condition names", func() {
			Expect(openSearchReconciler.GetValidConditionNames(instance)).To(Equal([]string{
				conditions.OpenSearchReconciled,
				conditions.OpenSearchAvailable,
				conditions.OpenSearchHealth,
				conditions.OpenSearchAutoscaling,
			}))
		})
	})

	Context("GetName", func() {
		It("should return the name of the reconciler", func() {
			Expect(openSearchReconciler.GetName()).To(Equal("OpenSearchReconciler"))
		})
	})
})
