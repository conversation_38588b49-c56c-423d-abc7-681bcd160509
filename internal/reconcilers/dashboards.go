package reconcilers

import (
	"context"
	"errors"
	"fmt"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders/helpers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	reconcilerhelpers "github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers/helpers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/templating"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	semverv3 "github.com/Masterminds/semver/v3"
	"github.com/cisco-open/operator-tools/pkg/reconciler"
	appsv1 "k8s.io/api/apps/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	dashboardsReconcilerName = "DashboardsReconciler"
	openSearchUsername       = "kibanaserver"
)

type DashboardsReconciler struct {
	client                             client.Client
	conditionHelper                    conditions.ConditionHelper
	resourceReconciler                 reconciler.ResourceReconciler
	dashboardsService                  services.DashboardsService
	imageHelper                        reconcilerhelpers.ImageHelper
	passwordStoreReader                services.PasswordStoreService
	featureConfig                      services.FeatureConfiguration
	templatedConfigurationSecret       templating.TemplatedConfig
	templatedConfigurationSavedObjects templating.TemplatedConfig
}

func NewDashboardsReconciler(client client.Client, conditionHelper conditions.ConditionHelper,
	dashboardsService services.DashboardsService, resourceReconciler reconciler.ResourceReconciler,
	imageHelper reconcilerhelpers.ImageHelper, passwordStoreReader services.PasswordStoreService,
	featureConfig services.FeatureConfiguration, templatePath string) ComponentReconciler {
	return &DashboardsReconciler{
		client:                             client,
		conditionHelper:                    conditionHelper,
		resourceReconciler:                 resourceReconciler,
		dashboardsService:                  dashboardsService,
		imageHelper:                        imageHelper,
		passwordStoreReader:                passwordStoreReader,
		featureConfig:                      featureConfig,
		templatedConfigurationSecret:       templating.NewTemplatedConfig(templatePath, "opensearch_dashboards.yml", "customlogo.svg", "entry.sh"),
		templatedConfigurationSavedObjects: templating.NewTemplatedConfig(templatePath, "saved-objects.yml"),
	}
}

//nolint:funlen // Just a complex function
func (d *DashboardsReconciler) Reconcile(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	result := reconciler.CombinedResult{}
	cfg := config.GetInstance()

	// Check if the instance CR is lacking the required configuration.
	if instance.Spec.OpenSearch.Dashboards.Resources.Requests.Cpu().IsZero() ||
		instance.Spec.OpenSearch.Dashboards.Replicas == 0 {
		result.CombineErr(errors.New("opensearch dashboards configuration is not available in instance CR"))
		d.conditionHelper.SetReconcileCondition(instance, conditions.DashboardsReconciled, result)

		// Do not return an error here, as retrying would not help in this case
		return result.Result, nil
	}

	// Determine image channel to be used
	desiredImageChannel := config.StableChannel
	if d.featureConfig.IsFeatureSet(instance, config.FeatureFlagUseOpenSearchDevelopmentVersion) && config.GetInstance().GetImages().Dashboards.Development != "" {
		desiredImageChannel = config.DevelopmentChannel
	} else if d.featureConfig.IsFeatureSet(instance, config.FeatureFlagUpgradeToOpenSearchV2) && config.GetInstance().GetImages().Dashboards.Preview != "" {
		desiredImageChannel = config.PreviewChannel
	}

	// Discover the image used in the deployment running in the cluster
	discoveredImage, err := d.getImageFromDeployment(instance)
	if err != nil {
		// In case the deployment does not exist yet, we can ignore the error (e.g. during initial creation)
		if !apierrors.IsNotFound(err) {
			result.CombineErr(fmt.Errorf("failed to get image from deployment: %w", err))
			d.conditionHelper.SetReconcileCondition(instance, conditions.DashboardsReconciled, result)
			return result.Result, result.Err
		}
	}

	// Get available images from the configuration
	availableImages := d.imageHelper.GetAvailableImages(instance, cfg.GetImages().Dashboards)

	// Find a matching image for the OpenSearch support version
	dashboardsImage, err := utils.GetMatchingImage(availableImages, discoveredImage, desiredImageChannel, instance.Status.OpenSearchClusterSupportVersion)
	if err != nil {
		result.CombineErr(fmt.Errorf("failed to determine OpenSearch Dashboards image: %w", err))
		d.conditionHelper.SetReconcileCondition(instance, conditions.DashboardsReconciled, result)

		// Do not return an error here, as retrying would not help in this case.
		// If OSD is running already, it will continue doing so.
		return result.Result, nil
	}

	// Get credentials objects from password-store
	osCredentials, err := d.passwordStoreReader.GetOpenSearchCredentials(ctx, instance, openSearchUsername)
	if err != nil {
		return ctrl.Result{}, err
	}

	// Source object for config templates
	configSource := struct {
		Host                  string
		OpenSearch            clv1beta1.OpenSearch
		OpenSearchCredentials models.Credentials
		Version               *semverv3.Version
	}{
		Host:                  helpers.GetDashboardsHost(instance, cfg.GetIngress()),
		OpenSearch:            instance.Spec.OpenSearch,
		OpenSearchCredentials: osCredentials,
		Version:               dashboardsImage.Version,
	}

	service := builders.BuildDashboardsService(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, service, d.client.Scheme()))
	result.Combine(d.resourceReconciler.ReconcileResource(service, reconciler.StatePresent))

	// saved-objects CM
	savedObjectsConfigMap := builders.BuildDashboardsSavedObjectsConfigMap(instance)
	result.CombineErr(d.templatedConfigurationSavedObjects.PopulateConfigMap(templating.MERGE_ALL, nil, savedObjectsConfigMap))
	result.CombineErr(ctrl.SetControllerReference(instance, savedObjectsConfigMap, d.client.Scheme()))
	result.Combine(d.resourceReconciler.ReconcileResource(savedObjectsConfigMap, reconciler.StatePresent))

	// config secret
	configSecret := builders.BuildDashboardsConfigSecret(instance)
	result.CombineErr(d.templatedConfigurationSecret.PopulateSecret(templating.PARSE_AS_FILES, configSource, configSecret))
	result.CombineErr(ctrl.SetControllerReference(instance, configSecret, d.client.Scheme()))
	result.Combine(d.resourceReconciler.ReconcileResource(configSecret, reconciler.StatePresent))

	// deployment (with config secret hash)
	deployment := builders.BuildDashboardsDeployment(instance, dashboardsImage.Image)
	result.CombineErr(utils.SetHashFromSecret(deployment.Spec.Template.Annotations, configSecret))
	result.CombineErr(ctrl.SetControllerReference(instance, deployment, d.client.Scheme()))
	result.Combine(d.resourceReconciler.ReconcileResource(deployment, reconciler.StatePresent))

	// Only create/reconcile the PDB for instances with multiple OSD pods
	podDisruptionBudget := builders.BuildDashboardsPodDisruptionBudget(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, podDisruptionBudget, d.client.Scheme()))
	if instance.Spec.OpenSearch.Dashboards.Replicas > 1 {
		result.Combine(d.resourceReconciler.ReconcileResource(podDisruptionBudget, reconciler.StatePresent))
	} else {
		result.Combine(d.resourceReconciler.ReconcileResource(podDisruptionBudget, reconciler.StateAbsent))
	}

	// ingress pointing to new dashboards service
	dashboardsIngress := builders.BuildDashboardsIngress(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, dashboardsIngress, d.client.Scheme()))
	result.Combine(d.resourceReconciler.ReconcileResource(dashboardsIngress, reconciler.StatePresent))

	// legacy ingress pointing to new dashboards service
	kibanaIngress := builders.BuildKibanaIngress(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, kibanaIngress, d.client.Scheme()))
	result.Combine(d.resourceReconciler.ReconcileResource(kibanaIngress, reconciler.StatePresent))

	// Set available conditions
	result.CombineErr(d.updateAvailableCondition(ctx, instance, deployment))

	// Set reconcile condition eventually
	channelLabel := fmt.Sprintf("image channel: %s - %s", dashboardsImage.Channel, dashboardsImage.Version)
	d.conditionHelper.SetReconcileCondition(instance, conditions.DashboardsReconciled, result, channelLabel)

	return result.Result, result.Err
}

// CleanUp cleans up resources outside the instance CR scope.
func (d *DashboardsReconciler) CleanUp(context.Context, *clv1beta1.Instance) (ctrl.Result, error) {
	// Nothing to do here...
	return ctrl.Result{}, nil
}

// GetValidConditionNames returns a list of condition names related to the component.
func (d *DashboardsReconciler) GetValidConditionNames(*clv1beta1.Instance) []string {
	dashboardsConditions := []string{
		conditions.DashboardsReconciled,
		conditions.DashboardsAvailable,
		conditions.DashboardsStatus,
	}

	return dashboardsConditions
}

// UpdateStatusConditions updates the components status condition(s). This method is called frequently and
// should be lightweight. It should only update the local instance, which will be persisted by the watcher
// after all component reconcilers have been called.
func (d *DashboardsReconciler) UpdateStatusConditions(ctx context.Context, instance *clv1beta1.Instance) bool {
	return d.conditionHelper.SetCondition(instance, d.dashboardsService.GetStatusCondition(ctx, instance))
}

// GetName returns the name of the reconciler for monitoring and logging purposes.
func (d *DashboardsReconciler) GetName() string {
	return dashboardsReconcilerName
}

//nolint:dupl // This availability condition differs from component to component
func (d *DashboardsReconciler) updateAvailableCondition(ctx context.Context, instance *clv1beta1.Instance, deployment *appsv1.Deployment) error {
	// Determine ready status based on replica availability
	err := d.client.Get(ctx, client.ObjectKeyFromObject(deployment), deployment)
	if err != nil {
		if apierrors.IsNotFound(err) {
			d.conditionHelper.SetCondition(instance, metav1.Condition{
				Type:    conditions.DashboardsAvailable,
				Status:  metav1.ConditionFalse,
				Reason:  conditions.NotFound,
				Message: "Deployment does not exist (yet)",
			})

			// Do not return an error in this case as rescheduling would not help
			// Immediately after the deployment is created we cannot expect it to exist already (async processing)
			return nil
		}
		return err
	}

	if deployment.Status.AvailableReplicas >= 1 {
		d.conditionHelper.SetCondition(instance, metav1.Condition{
			Type:    conditions.DashboardsAvailable,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.Available,
			Message: fmt.Sprintf("%d of %d replica(s) are available", deployment.Status.AvailableReplicas, deployment.Status.Replicas),
		})
		return nil
	}

	d.conditionHelper.SetCondition(instance, metav1.Condition{
		Type:    conditions.DashboardsAvailable,
		Status:  metav1.ConditionFalse,
		Reason:  conditions.NotAvailable,
		Message: "No replicas are available",
	})

	return nil
}

// getImageFromDeployment retrieves the image from the running dashboards deployment.
func (d *DashboardsReconciler) getImageFromDeployment(instance *clv1beta1.Instance) (string, error) {
	deployment := builders.BuildDashboardsDeployment(instance, "")
	err := d.client.Get(context.Background(), client.ObjectKeyFromObject(deployment), deployment)
	if err != nil {
		return "", err
	}

	image, ok := utils.GetImageFromContainer(deployment.Spec.Template.Spec.Containers, "dashboards")
	if !ok {
		return "", errors.New("container 'dashboards' not found in deployment")
	}

	return image, nil
}
