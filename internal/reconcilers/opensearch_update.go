package reconcilers

import (
	"context"
	"fmt"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	clientModels "github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	semverv3 "github.com/Masterminds/semver/v3"
	"github.com/go-logr/logr"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

const (
	openSearchUpdateReconcilerName = "OpenSearchUpdateReconciler"

	// controllerRevisionHashLabel is the label key for the revision hash of a StatefulSet pod.
	controllerRevisionHashLabel = "controller-revision-hash"

	// openSearchRequeueDelay is the delay for requeuing in case of OpenSearch not being ready to continue or reporting an error.
	openSearchRequeueDelay = 15 * time.Second

	// openSearchUpdateStuckTime is the time to wait for an update to complete before considering it stuck.
	// Stuck updates are reported to the controller and should result in an health status DOWN.
	openSearchUpdateStuckTime = 60 * time.Minute

	// openSearchContainerName is the name of the OpenSearch container in the StatefulSet pod.
	openSearchContainerName = "opensearch"
)

type OpenSearchUpdateReconciler struct {
	client                   client.Client
	conditionHelper          conditions.ConditionHelper
	openSearchService        services.OpenSearchService
	topologyDiscoveryService services.OpenSearchTopologyService
	logger                   logr.Logger
}

func NewOpenSearchUpdateReconciler(client client.Client, conditionHelper conditions.ConditionHelper,
	topologyDiscoveryService services.OpenSearchTopologyService, openSearchService services.OpenSearchService) ComponentReconciler {
	return &OpenSearchUpdateReconciler{
		logger:                   ctrl.Log.WithName("opensearch-update-reconciler"),
		client:                   client,
		conditionHelper:          conditionHelper,
		topologyDiscoveryService: topologyDiscoveryService,
		openSearchService:        openSearchService,
	}
}

// Reconcile checks if the StatefulSets of the given nodeGroups have pending updates.
// If there are pending updates, it will restart the pods with outdated revisions one by one.
func (r *OpenSearchUpdateReconciler) Reconcile(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	log := r.logger.WithValues("instance", instance.Name)

	// Only proceed if the OpenSearch resources are successfully reconciled
	if r.conditionHelper.GetConditionStatus(instance, conditions.OpenSearchReconciled) != metav1.ConditionTrue {
		return reconcile.Result{}, nil
	}

	// Discover the current topology of OpenSearch StatefulSets
	topology, err := r.topologyDiscoveryService.Discover(ctx, instance)
	if err != nil {
		return ctrl.Result{}, fmt.Errorf("failed to discover the current topology: %w", err)
	}

	// Check if an upgrade is required (due to new image version) or in progress
	isUpgrade, err := r.isUpgradeRequiredOrInProgress(ctx, instance, topology)
	if err != nil {
		return ctrl.Result{}, fmt.Errorf("failed to check if an upgrade is required or in progress: %w", err)
	}

	if isUpgrade {
		// Sort the topology node groups for an upgrade procedure (data nodes first)
		topology.Sort(models.SortOrderUpgrade)
	} else {
		// Sort the topology node groups for a regular update procedure (cluster_manager nodes first)
		topology.Sort(models.SortOrderNormal)
	}

	// Find the first StatefulSet which pods are not yet updated to the latest revision
	stsWithPendingUpdate, err := r.findStatefulSetWithPendingUpdate(ctx, topology)
	if err != nil {
		return ctrl.Result{}, fmt.Errorf("failed to find StatefulSet with pending update: %w", err)
	}

	if stsWithPendingUpdate != nil {
		// Perform the update procedure for the StatefulSet with pending updates
		return r.executeUpdate(ctx, log, instance, isUpgrade, stsWithPendingUpdate)
	}

	// Finalize the update procedure if all StatefulSets are up-to-date
	return r.finalizeUpdate(ctx, log, instance)
}

// executeUpdate performs the update procedure for the given StatefulSet with pending updates.
// It sets the OpenSearchUpdated condition to False to indicate that an update is in progress.
// Then it finds the first pod with an outdated revision in the StatefulSet and restarts it.
// In case of an upgrade it takes care of blocking replica shard allocation.
func (r *OpenSearchUpdateReconciler) executeUpdate(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, isUpgrade bool, stsWithPendingUpdate *appsv1.StatefulSet) (ctrl.Result, error) {
	if isUpgrade {
		// Set the OpenSearchUpgraded condition to False if an upgrade is required
		// This helps to avoid costly checks to determine if an upgrade is in progress
		// until the upgrade is completed.
		if changed := r.conditionHelper.SetCondition(instance, metav1.Condition{
			Type:   conditions.OpenSearchUpgraded,
			Status: metav1.ConditionFalse,
			Reason: conditions.InProgress,
			Message: fmt.Sprintf("Upgrading pods to from image version %s to %s",
				instance.Status.OpenSearchClusterSupportVersion,
				instance.Status.OpenSearchClusterTargetVersion),
		}); changed {
			log.Info("Running image version upgrade for OpenSearch pods",
				"version", instance.Status.OpenSearchClusterTargetVersion,
				"previousVersion", instance.Status.OpenSearchClusterSupportVersion)
		}
	}
	// Set the OpenSearchUpdated condition to False to indicate that an update is in progress
	if changed := r.conditionHelper.SetCondition(instance, metav1.Condition{
		Type:    conditions.OpenSearchUpdated,
		Status:  metav1.ConditionFalse,
		Reason:  conditions.InProgress,
		Message: fmt.Sprintf("Updating '%s' pods to latest revision.", stsWithPendingUpdate.Name),
	}); changed {
		log.Info("Running rolling update procedure for StatefulSet", "name", stsWithPendingUpdate.Name)
		// Since the condition was changed, we need to requeue to avoid any reconciling conflicts
		// with OpenSearchReconciler.
		return ctrl.Result{RequeueAfter: openSearchRequeueDelay}, nil
	}

	// Find the first pod with an outdated revision in the StatefulSet
	pod, found, err := r.findPodWithOutdatedRevision(ctx, stsWithPendingUpdate)
	if err != nil {
		return ctrl.Result{}, fmt.Errorf("failed to find pod with outdated revision: %w", err)
	}

	// If the pod is nil or has a deletion timestamp, we can just return.
	// A new pod will be created by the StatefulSet controller shortly, which will trigger
	// the continuation of the update.
	if !found || pod.DeletionTimestamp != nil {
		return ctrl.Result{}, nil
	}

	// In case the StatefulSet reports non-ready pods, we need to wait for the pods to become ready.
	// Before returning in this case, we check if any container is in a CrashLoopBackOff state and restart it.
	if stsWithPendingUpdate.Status.ReadyReplicas != ptr.Deref(stsWithPendingUpdate.Spec.Replicas, 1) {
		for _, container := range pod.Status.ContainerStatuses {
			// If any container is getting crashed, restart it by deleting the pod so that new update in sts can take place.
			if !container.Ready && container.State.Waiting != nil && container.State.Waiting.Reason == "CrashLoopBackOff" {
				log.Info("Restarting stuck pod with outdated revision", "name", pod.Name, "revision", pod.Labels[controllerRevisionHashLabel])
				err = r.client.Delete(ctx, &pod)
				if err != nil {
					return ctrl.Result{}, fmt.Errorf("failed to delete stuck pod %s: %w", pod.Name, err)
				}
			}
		}

		return ctrl.Result{}, nil
	}

	// Determine OpenSearch version based on the StatefulSet image
	stsImage, _ := utils.GetImageFromContainer(stsWithPendingUpdate.Spec.Template.Spec.Containers, openSearchContainerName)
	stsImageTag := utils.GetImageTag(stsImage)
	stsVersion, err := semverv3.NewVersion(stsImageTag)
	if err != nil {
		return ctrl.Result{}, fmt.Errorf("failed to parse OpenSearch image version tag '%s' of StatefulSet '%s': %w",
			stsImageTag, stsWithPendingUpdate.Name, err)
	}

	updatedNodes, err := r.openSearchService.GetNumberOfNodesMatchingVersion(ctx, instance, fmt.Sprintf("%s-*", stsWithPendingUpdate.Name), stsVersion)
	if err != nil {
		log.Error(err, "Failed to get the number of updated nodes, requeuing...")
		return ctrl.Result{RequeueAfter: openSearchRequeueDelay}, nil
	}

	// Wait for all updated nodes to join the cluster before proceeding.
	if updatedNodes < int(stsWithPendingUpdate.Status.UpdatedReplicas) {
		log.Info("Waiting for OpenSearch nodes to be updated and joining the cluster, requeuing...", "updatedNodes",
			updatedNodes, "updatedReplicas", stsWithPendingUpdate.Status.UpdatedReplicas)
		return ctrl.Result{RequeueAfter: openSearchRequeueDelay}, nil
	}

	// Upgrades require special handling and checks to ensure the cluster remains healthy.
	if isUpgrade {
		// Enable allocation for all shards to allow the cluster to recover.
		// Otherwise the cluster might be stuck in a yellow state.
		err = r.openSearchService.SetShardAllocation(ctx, instance, clientModels.ShardAllocationAll)
		if err != nil {
			log.Error(err, "OpenSearch failed to enable shard allocation, requeuing...")
			return ctrl.Result{RequeueAfter: openSearchRequeueDelay}, nil
		}

		// Check if the OpenSearch cluster is ready for an upgrade of the given node
		if ready, reason := r.checkHealthForUpgrade(ctx, instance, pod.Name); !ready {
			log.Info("OpenSearch node is not ready for an version upgrade, requeuing...", "reason", reason, "name", pod.Name)
			return ctrl.Result{RequeueAfter: openSearchRequeueDelay}, nil
		}

		// Disable shard allocation for non-primary shards before restarting the pod
		err = r.openSearchService.SetShardAllocation(ctx, instance, clientModels.ShardAllocationPrimaries)
		if err != nil {
			log.Error(err, "OpenSearch failed to disable shard allocation, requeuing...")
			return ctrl.Result{RequeueAfter: openSearchRequeueDelay}, nil
		}
	} else {
		// Check if the OpenSearch cluster is ready for an update of the given node
		if ready, reason := r.checkHealthForUpdate(ctx, instance); !ready {
			log.Info("OpenSearch cluster is not ready for a node restart, requeuing...", "reason", reason)
			return ctrl.Result{RequeueAfter: openSearchRequeueDelay}, nil
		}
	}

	// Eventually, we can restart the pod with the outdated revision
	log.Info("Restarting pod with outdated revision", "name", pod.Name, "revision", pod.Labels[controllerRevisionHashLabel])
	err = r.client.Delete(ctx, &pod)
	if err != nil {
		return reconcile.Result{}, fmt.Errorf("failed to delete pod %s: %w", pod.Name, err)
	}

	return ctrl.Result{}, nil
}

// finalizeUpdate finalizes the update procedure by setting the OpenSearchUpgraded and OpenSearchUpdated conditions to True.
// In case of an upgrade, it also reenables allocation for all shards.
func (r *OpenSearchUpdateReconciler) finalizeUpdate(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance) (ctrl.Result, error) {
	isUpgradeInProgress := r.conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpgraded) == metav1.ConditionFalse
	// All StatefulSets are up-to-date at this point
	if isUpgradeInProgress {
		err := r.openSearchService.SetShardAllocation(ctx, instance, clientModels.ShardAllocationAll)
		if err != nil {
			log.Error(err, "OpenSearch failed to enable shard allocation, requeuing...")
			return ctrl.Result{RequeueAfter: openSearchRequeueDelay}, nil
		}
	}

	if isUpgradeInProgress {
		log.Info("Upgraded OpenSearch pods to target image version", "version", instance.Status.OpenSearchClusterTargetVersion)
	} else if r.conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpdated) == metav1.ConditionFalse {
		log.Info("Updated OpenSearch pods to latest StatefulSet revisions")
	}

	// Update the OpenSearch cluster support version to signal dependent components
	// that the OpenSearch cluster is updated to the target version.
	if isUpgradeInProgress || instance.Status.OpenSearchClusterSupportVersion == "" {
		instance.Status.OpenSearchClusterSupportVersion = instance.Status.OpenSearchClusterTargetVersion
	}

	// Set OpenSearchUpgraded condition to True. (Noop if not upgrading)
	r.conditionHelper.SetCondition(instance, metav1.Condition{
		Type:    conditions.OpenSearchUpgraded,
		Status:  metav1.ConditionTrue,
		Reason:  conditions.Succeeded,
		Message: fmt.Sprintf("Pods are upgraded to image version %s", instance.Status.OpenSearchClusterSupportVersion),
	})

	// Set OpenSearchUpdated condition to True. (Noop if not updating)
	// This will also unblock the OpenSearchReconciler.
	r.conditionHelper.SetCondition(instance, metav1.Condition{
		Type:    conditions.OpenSearchUpdated,
		Status:  metav1.ConditionTrue,
		Reason:  conditions.Succeeded,
		Message: "Pods are updated to latest StatefulSet revision",
	})

	return ctrl.Result{}, nil
}

// CleanUp cleans up the resources created by the reconciler. Not applicable for this reconciler.
func (r *OpenSearchUpdateReconciler) CleanUp(context.Context, *clv1beta1.Instance) (ctrl.Result, error) {
	// Nothing to do here, as the OpenSearch resources are owned by the instance.
	return ctrl.Result{}, nil
}

// GetValidConditionNames returns a list of condition names related to the reconciler.
func (r *OpenSearchUpdateReconciler) GetValidConditionNames(instance *clv1beta1.Instance) []string {
	validConditions := make([]string, 0)

	// Check if an update is stuck and report it.
	if r.isUpdateStuck(instance) {
		validConditions = append(validConditions, conditions.OpenSearchUpdated)
	}

	return validConditions
}

// UpdateStatusConditions updates the components status condition(s). Not applicable for this reconciler.
func (r *OpenSearchUpdateReconciler) UpdateStatusConditions(context.Context, *clv1beta1.Instance) bool {
	return false
}

// GetName returns the name of the reconciler for monitoring and logging purposes.
func (r *OpenSearchUpdateReconciler) GetName() string {
	return openSearchUpdateReconcilerName
}

// isUpgradeRequiredOrInProgress checks if an upgrade is required or in progress.
// An upgrade is required
// - if the OpenSearchUpgraded condition is False.
// - or if the image version of the StatefulSet pods is outdated.
func (r *OpenSearchUpdateReconciler) isUpgradeRequiredOrInProgress(ctx context.Context, instance *clv1beta1.Instance, topology *models.Topology) (bool, error) {
	// Check if an upgrade is already in progress
	status := r.conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpgraded)
	if status == metav1.ConditionFalse {
		return true, nil
	}

	isUpgradeRequired := false
	for _, nodeGroup := range topology.NodeGroups {
		// Parse image version from StatefulSet
		sts := nodeGroup.StatefulSet
		stsImage, _ := utils.GetImageFromContainer(sts.Spec.Template.Spec.Containers, openSearchContainerName)
		stsImageTag := utils.GetImageTag(stsImage)
		stsVersion, err := semverv3.NewVersion(stsImageTag)
		if err != nil {
			return false, fmt.Errorf("failed to parse OpenSearch image version tag '%s' of StatefulSet '%s': %w", stsImageTag, sts.Name, err)
		}

		// Iterate pods to check for outdated image version
		for i := range ptr.Deref(sts.Spec.Replicas, 1) {
			podName := fmt.Sprintf("%s-%d", sts.Name, i)
			pod := &corev1.Pod{}
			err = r.client.Get(ctx, client.ObjectKey{Namespace: sts.Namespace, Name: podName}, pod)
			if err != nil {
				if errors.IsNotFound(err) {
					continue
				}
				return false, fmt.Errorf("failed to get pod %s: %w", podName, err)
			}

			// Parse image version from pod
			podImage, _ := utils.GetImageFromContainer(pod.Spec.Containers, openSearchContainerName)
			podImageTag := utils.GetImageTag(podImage)

			// Skip if the pod image tag is empty
			if len(podImageTag) == 0 {
				continue
			}

			podVersion, err := semverv3.NewVersion(podImageTag)
			if err != nil {
				return false, fmt.Errorf("failed to parse OpenSearch image version tag '%s' of Pod '%s': %w", podImageTag, pod.Name, err)
			}

			// Check if pod version is outdated
			if stsVersion.GreaterThan(podVersion) {
				isUpgradeRequired = true
				break
			}
		}
	}

	return isUpgradeRequired, nil
}

// findStatefulSetWithPendingUpdate identifies the first StatefulSet with pending updates in the given topology.
// A StatefulSet is considered to have pending updates if the current revision is not equal to the update revision.
// If the StatefulSet has less updated and available replicas than the desired replicas, it is returned.
// If app pods are updated and running, the current revision is updated to the update revision.
func (r *OpenSearchUpdateReconciler) findStatefulSetWithPendingUpdate(ctx context.Context, topology *models.Topology) (*appsv1.StatefulSet, error) {
	var stsWithPendingUpdate *appsv1.StatefulSet
	for _, nodeGroup := range topology.NodeGroups {
		sts := nodeGroup.StatefulSet
		if len(sts.Status.UpdateRevision) == 0 {
			continue
		}

		// Check if the StatefulSet pods need to be updated
		if sts.Status.CurrentRevision != sts.Status.UpdateRevision &&
			(sts.Status.UpdatedReplicas != ptr.Deref(sts.Spec.Replicas, 1) ||
				sts.Status.AvailableReplicas != ptr.Deref(sts.Spec.Replicas, 1)) {
			stsWithPendingUpdate = sts
			break
		}

		// When the pods are updated, ensure that the current revision is updated properly.
		// See related issue: https://github.com/kubernetes/kubernetes/issues/73492
		if sts.Status.CurrentRevision != sts.Status.UpdateRevision {
			sts.Status.CurrentRevision = sts.Status.UpdateRevision
			if err := r.client.Status().Update(ctx, sts); err != nil {
				return nil, err
			}
		}
	}
	return stsWithPendingUpdate, nil
}

// findPodWithOutdatedRevision identifies a pod with an outdated revision in the given StatefulSet.
// The order is based on the pod index.
func (r *OpenSearchUpdateReconciler) findPodWithOutdatedRevision(ctx context.Context, sts *appsv1.StatefulSet) (corev1.Pod, bool, error) {
	for i := range ptr.Deref(sts.Spec.Replicas, 1) {
		name := fmt.Sprintf("%s-%d", sts.Name, i)
		pod := corev1.Pod{}
		err := r.client.Get(ctx, client.ObjectKey{Namespace: sts.Namespace, Name: name}, &pod)
		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			return corev1.Pod{}, false, fmt.Errorf("failed to get pod %s: %w", name, err)
		}

		revision := pod.Labels[controllerRevisionHashLabel]
		if revision != sts.Status.UpdateRevision {
			return pod, true, nil
		}
	}

	return corev1.Pod{}, false, nil
}

// checkHealthForUpgrade checks if the OpenSearch cluster is ready for an upgrade of the given node without risking a red state.
func (r *OpenSearchUpdateReconciler) checkHealthForUpgrade(ctx context.Context, instance *clv1beta1.Instance, nodeName string) (bool, string) {
	// Check the health of the OpenSearch cluster
	health, err := r.openSearchService.GetHealth(ctx, instance)
	if err != nil {
		return false, err.Error()
	}

	// Check if the cluster is green
	if health.Status == clientModels.HealthStatusGreen {
		return true, ""
	}

	if health.Status == clientModels.HealthStatusYellow {
		// Check if there are initializing shards
		if health.InitializingShards > 0 {
			return false, "one or more shards are initializing"
		}

		// Check if there are relocating shards
		if health.RelocatingShards > 0 {
			return false, "one or more shards are relocating"
		}

		// Check if there are delayed unassigned shards. We expect those to
		// re-assign shortly and therefore should wait for them.
		if health.DelayedUnassignedShards > 0 {
			return false, "one or more shards are unassigned due to node left (delayed unassigned)"
		}

		// Check if a node restart is allowed despite yellow state.
		if health.UnassignedShards > 0 {
			if allowed, reason, err := r.openSearchService.IsNodeRestartAllowed(ctx, instance, nodeName); err != nil {
				return false, err.Error()
			} else if allowed {
				return true, reason
			}
		}

		return false, "the cluster is yellow for an unknown reason"
	}

	return false, "the cluster is red"
}

// checkHealthForUpdate checks if the OpenSearch cluster is ready for a node restart without risking a red state.
func (r *OpenSearchUpdateReconciler) checkHealthForUpdate(ctx context.Context, instance *clv1beta1.Instance) (bool, string) {
	// Check the health of the OpenSearch cluster
	health, err := r.openSearchService.GetHealth(ctx, instance)
	if err != nil {
		return false, err.Error()
	}

	// Check if the cluster is green
	if health.Status == clientModels.HealthStatusGreen {
		return true, ""
	}

	if health.Status == clientModels.HealthStatusYellow {
		// Check if there are initializing shards
		if health.InitializingShards > 0 {
			return false, "one or more shards are initializing"
		}

		// Check if there are relocating shards
		if health.RelocatingShards > 0 {
			return false, "one or more shards are relocating"
		}

		// Check if there are delayed unassigned shards. We expect those to
		// re-assign shortly and therefore should wait for them.
		if health.DelayedUnassignedShards > 0 {
			return false, "one or more shards are unassigned due to node left (delayed unassigned)"
		}

		return true, ""
	}

	// If the cluster is red already we can continue with the update. It cannot get worse.
	return true, ""
}

// isUpdateStuck checks if an update is stuck by checking the OpenSearchUpdated condition
// and the time since the last transition.
func (r *OpenSearchUpdateReconciler) isUpdateStuck(instance *clv1beta1.Instance) bool {
	updateCondition := r.conditionHelper.GetCondition(instance, conditions.OpenSearchUpdated)
	if updateCondition != nil && updateCondition.Status == metav1.ConditionFalse {
		timeSinceChange := time.Since(updateCondition.LastTransitionTime.Time)
		if timeSinceChange > openSearchUpdateStuckTime {
			return true
		}
	}
	return false
}
