package reconcilers_test

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	"gopkg.in/yaml.v3"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("InstanceMaintainerReconciler", func() {
	const (
		instanceName                   = "cls-test"
		namespace                      = "sf-test"
		instanceMaintainerTemplatePath = "../../templates/instance_maintainer"
	)

	var (
		instance                      *clv1beta1.Instance
		instanceMaintainerReconciler  reconcilers.ComponentReconciler
		resourceReconciler            reconciler.ResourceReconciler
		featureConfigurationMock      *fake.FeatureConfigurationMock
		instanceMaintainerServiceMock = fake.NewGenericStatusServiceMock()
		conditionHelper               = conditions.NewConditionHelper()
		ctx                           = context.Background()
		instanceNamespacedName        = types.NamespacedName{
			Name:      instanceName,
			Namespace: namespace,
		}
	)

	BeforeEach(func() {
		resourceReconciler = reconciler.NewReconcilerWith(k8sClient)
		featureConfigurationMock = fake.NewFeatureConfigurationMock()

		Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      instanceName,
				Namespace: namespace,
			},
			Spec: clv1beta1.InstanceSpec{
				InstanceMaintainer: clv1beta1.InstanceMaintainer{
					Resources: corev1.ResourceRequirements{},
				},
				OpenSearch: clv1beta1.OpenSearch{
					Cluster: clv1beta1.Cluster{
						NodePools: []clv1beta1.NodePool{},
					},
				},
			},
		}

		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace,
			},
		}

		err := k8sClient.Get(ctx, types.NamespacedName{Name: namespace}, ns)
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the namespace")
			Expect(k8sClient.Create(ctx, ns)).To(Succeed())
		}

		err = k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{})
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the CustomResource for the CLOE instance")
			Expect(k8sClient.Create(ctx, instance)).To(Succeed())
		}

		instanceMaintainerReconciler = reconcilers.NewInstanceMaintainerReconciler(k8sClient, conditionHelper,
			resourceReconciler, instanceMaintainerServiceMock, featureConfigurationMock, instanceMaintainerTemplatePath)
	})

	AfterEach(func() {
		err := k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{})
		Expect(err).NotTo(HaveOccurred())

		By("deleting the CustomResource for the CLOE instance")
		Expect(k8sClient.Delete(ctx, instance)).To(Succeed())
	})

	Context("Reconcile", func() {
		When("Reconcile() is called", func() {
			BeforeEach(func() {
				result, err := instanceMaintainerReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
			})

			It("should reconcile all instance-maintainer resources", func() {
				imDeployment := &appsv1.Deployment{}
				err := k8sClient.Get(ctx, types.NamespacedName{Name: "instance-maintainer-deployment", Namespace: namespace}, imDeployment)
				Expect(err).NotTo(HaveOccurred())
				Expect(imDeployment.OwnerReferences[0].Kind).To(Equal("Instance"))

				imConfigMap := &corev1.ConfigMap{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "instance-maintainer-config", Namespace: namespace}, imConfigMap)
				Expect(err).NotTo(HaveOccurred())
				Expect(imConfigMap.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(imConfigMap.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(imConfigMap.Data["config.yaml"]).ToNot(BeEmpty())

				var config ReconcileConfig
				err = yaml.Unmarshal([]byte(imConfigMap.Data["config.yaml"]), &config)
				Expect(err).NotTo(HaveOccurred())
				Expect(config.Reconcile.SkipReconciliation.Content).To(BeFalse())
				Expect(config.Reconcile.SkipReconciliation.AccessControl).To(BeFalse())
				Expect(config.Reconcile.SkipReconciliation.ComposableTemplates).To(BeFalse())
				Expect(config.Reconcile.SkipReconciliation.ISMPolicies).To(BeFalse())
				Expect(config.Reconcile.SkipReconciliation.SavedObjects).To(BeFalse())

				Expect(featureConfigurationMock.IsIsFeatureSetCalled).To(BeTrue())

				imSvcAccount := &corev1.ServiceAccount{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "instance-maintainer-account", Namespace: namespace}, imSvcAccount)
				Expect(err).NotTo(HaveOccurred())
				Expect(imSvcAccount.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(imSvcAccount.OwnerReferences[0].Name).To(Equal("cls-test"))

				imRole := &rbacv1.Role{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "instance-maintainer-role", Namespace: namespace}, imRole)
				Expect(err).NotTo(HaveOccurred())
				Expect(imRole.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(imRole.OwnerReferences[0].Name).To(Equal("cls-test"))

				imRoleBinding := &rbacv1.RoleBinding{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "instance-maintainer-rolebinding", Namespace: namespace}, imRoleBinding)
				Expect(err).NotTo(HaveOccurred())
				Expect(imRoleBinding.Subjects).To(Equal([]rbacv1.Subject{
					{
						Kind:      "ServiceAccount",
						Name:      "instance-maintainer-account",
						Namespace: namespace,
					},
				}))

				imService := &corev1.Service{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "instance-maintainer-service", Namespace: namespace}, imService)
				Expect(err).NotTo(HaveOccurred())
				Expect(imService.Spec.Ports[0].Name).To(Equal("http"))
				Expect(imService.Spec.Ports[0].Port).To(Equal(int32(8081)))
			})

			It("should set the reconciled condition", func() {
				condition := conditionHelper.GetCondition(instance, conditions.InstanceMaintainerReconciled)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Succeeded))
				Expect(condition.Message).To(Equal("Successfully reconciled resources"))
			})
		})

		When("skipReconciliation feature flags are set", func() {
			BeforeEach(func() {
				featureConfigurationMock.PrepareFeature("skipReconcileContent", true)
				featureConfigurationMock.PrepareFeature("skipReconcileAccessControl", true)
				featureConfigurationMock.PrepareFeature("skipReconcileComposableTemplate", true)
				featureConfigurationMock.PrepareFeature("skipReconcileISMPolicy", true)
				featureConfigurationMock.PrepareFeature("skipReconcileSavedObjects", true)

				result, err := instanceMaintainerReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
			})

			It("should set the skipReconciliation flags in the instance-maintainer config", func() {
				imConfigMap := &corev1.ConfigMap{}
				err := k8sClient.Get(ctx, types.NamespacedName{Name: "instance-maintainer-config", Namespace: namespace}, imConfigMap)
				Expect(err).NotTo(HaveOccurred())

				var config ReconcileConfig
				err = yaml.Unmarshal([]byte(imConfigMap.Data["config.yaml"]), &config)
				Expect(err).NotTo(HaveOccurred())
				Expect(config.Reconcile.SkipReconciliation.Content).To(BeTrue())
				Expect(config.Reconcile.SkipReconciliation.AccessControl).To(BeTrue())
				Expect(config.Reconcile.SkipReconciliation.ComposableTemplates).To(BeTrue())
				Expect(config.Reconcile.SkipReconciliation.ISMPolicies).To(BeTrue())
				Expect(config.Reconcile.SkipReconciliation.SavedObjects).To(BeTrue())

				Expect(featureConfigurationMock.IsIsFeatureSetCalled).To(BeTrue())
			})
		})

		When("instance maintainer deployment has less than 1 available replicas", func() {
			BeforeEach(func() {
				deployment := builders.BuildInstanceMaintainerDeployment(instance)
				deployment.Status = appsv1.DeploymentStatus{
					ReadyReplicas:     0,
					Replicas:          0,
					AvailableReplicas: 0,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to not available (false) state", func() {
				result, err := instanceMaintainerReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				condition := conditionHelper.GetCondition(instance, conditions.InstanceMaintainerAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionFalse))
				Expect(condition.Reason).To(Equal(conditions.NotAvailable))
				Expect(condition.Message).To(Equal("No replicas are available"))
			})
		})

		When("instance maintainer deployment has 1 or more available replicas", func() {
			BeforeEach(func() {
				deployment := builders.BuildInstanceMaintainerDeployment(instance)
				deployment.Status = appsv1.DeploymentStatus{
					ReadyReplicas:     1,
					Replicas:          1,
					AvailableReplicas: 1,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to available (true) state", func() {
				result, err := instanceMaintainerReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				condition := conditionHelper.GetCondition(instance, conditions.InstanceMaintainerAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Available))
				Expect(condition.Message).To(Equal("1 of 1 replica(s) are available"))
			})
		})
	})

	Context("UpdateStatusConditions", func() {
		var condition metav1.Condition
		BeforeEach(func() {
			condition = metav1.Condition{
				Type:    conditions.InstanceMaintainerStatus,
				Status:  metav1.ConditionTrue,
				Reason:  conditions.Succeeded,
				Message: "Up and running",
			}
		})

		When("instance-maintainer status condition is not yet set in instance CR", func() {
			BeforeEach(func() {
				instanceMaintainerServiceMock.PrepareStatusCondition(condition)
			})

			It("should return true", func() {
				Expect(instanceMaintainerReconciler.UpdateStatusConditions(ctx, instance)).To(BeTrue())
			})

			It("should set the status condition in the instance CR", func() {
				Expect(instance.Status.Conditions).To(BeEmpty())
				instanceMaintainerReconciler.UpdateStatusConditions(ctx, instance)
				Expect(instance.Status.Conditions).To(HaveLen(1))

				createdCondition := conditionHelper.GetCondition(instance, conditions.InstanceMaintainerStatus)
				Expect(createdCondition.Status).To(Equal(metav1.ConditionTrue))
				Expect(createdCondition.Reason).To(Equal(conditions.Succeeded))
				Expect(createdCondition.Message).To(Equal("Up and running"))
			})
		})

		When("instance-maintainer status condition is already set", func() {
			BeforeEach(func() {
				instanceMaintainerServiceMock.PrepareStatusCondition(condition)
				conditionHelper.SetCondition(instance, condition)
			})

			It("should return false", func() {
				Expect(instanceMaintainerReconciler.UpdateStatusConditions(ctx, instance)).To(BeFalse())
			})

			It("should not change the status condition in the instance CR", func() {
				Expect(instance.Status.Conditions).To(HaveLen(1))
				instanceMaintainerReconciler.UpdateStatusConditions(ctx, instance)
				Expect(instance.Status.Conditions).To(HaveLen(1))

				createdCondition := conditionHelper.GetCondition(instance, conditions.InstanceMaintainerStatus)
				Expect(createdCondition.Status).To(Equal(metav1.ConditionTrue))
				Expect(createdCondition.Reason).To(Equal(conditions.Succeeded))
				Expect(createdCondition.Message).To(Equal("Up and running"))
			})
		})
	})

	Context("CleanUp", func() {
		It("should return no error and schedule no requeue", func() {
			result, err := instanceMaintainerReconciler.CleanUp(ctx, instance)
			Expect(err).NotTo(HaveOccurred())
			Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
		})
	})

	Context("GetValidConditionNames", func() {
		It("should return a list of valid condition names", func() {
			Expect(instanceMaintainerReconciler.GetValidConditionNames(instance)).To(Equal([]string{
				conditions.InstanceMaintainerReconciled,
				conditions.InstanceMaintainerAvailable,
				conditions.InstanceMaintainerStatus,
			}))
		})
	})

	Context("GetName", func() {
		It("should return the name of the reconciler", func() {
			Expect(instanceMaintainerReconciler.GetName()).To(Equal("InstanceMaintainerReconciler"))
		})
	})
})

type ReconcileConfig struct {
	Reconcile struct {
		SkipReconciliation struct {
			Content             bool `yaml:"content"`
			AccessControl       bool `yaml:"accessControl"`
			ComposableTemplates bool `yaml:"composableTemplates"`
			ISMPolicies         bool `yaml:"ismPolicies"`
			SavedObjects        bool `yaml:"savedObjects"`
		} `yaml:"skipReconciliation"`
	} `yaml:"reconcile"`
}
