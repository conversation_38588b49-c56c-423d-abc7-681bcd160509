package reconcilers_test

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("IngestPkiReconciler", func() {
	const (
		instanceName = "cls-test"
		namespace    = "sf-test"
	)

	var (
		instance               *clv1beta1.Instance
		ingestPkiReconciler    reconcilers.ComponentReconciler
		resourceReconciler     reconciler.ResourceReconciler
		conditionHelper        = conditions.NewConditionHelper()
		ctx                    = context.Background()
		instanceNamespacedName = types.NamespacedName{
			Name:      instanceName,
			Namespace: namespace,
		}
	)

	BeforeEach(func() {
		resourceReconciler = reconciler.NewReconcilerWith(k8sClient)

		Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      instanceName,
				Namespace: namespace,
			},
			Spec: clv1beta1.InstanceSpec{
				RotateRootCA: false,
				OpenSearch: clv1beta1.OpenSearch{
					Cluster: clv1beta1.Cluster{
						NodePools: []clv1beta1.NodePool{},
					},
				},
			},
		}

		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace,
			},
		}

		err := k8sClient.Get(ctx, types.NamespacedName{Name: namespace}, ns)
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the namespace")
			Expect(k8sClient.Create(ctx, ns)).To(Succeed())
		}

		err = k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{})
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the CustomResource for the CLOE instance")
			Expect(k8sClient.Create(ctx, instance)).To(Succeed())
		}

		ingestPkiReconciler = reconcilers.NewIngestPkiReconciler(k8sClient, conditionHelper,
			resourceReconciler)
	})

	AfterEach(func() {
		Expect(k8sClient.DeleteAllOf(ctx, &clv1beta1.Instance{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Secret{}, client.InNamespace(namespace))).To(Succeed())
	})

	Context("Reconcile", func() {
		When("Reconcile() is called", func() {
			BeforeEach(func() {
				result, err := ingestPkiReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
			})

			It("should reconcile all ingestPki resources", func() {
				activeRootCaSecret := &corev1.Secret{}
				err := k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-active", Namespace: namespace}, activeRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(activeRootCaSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(activeRootCaSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(len(activeRootCaSecret.Data["tls.crt"])).To(BeNumerically(">", 0))
				Expect(len(activeRootCaSecret.Data["tls.key"])).To(BeNumerically(">", 0))
				Expect(activeRootCaSecret.Labels["x509-certificate-exporter"]).Should(Equal("scrape"))

				oldRootCaSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-old", Namespace: namespace}, oldRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(oldRootCaSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(oldRootCaSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(oldRootCaSecret.Data["tls.crt"]).To(Equal([]byte("")))
				Expect(oldRootCaSecret.Data["tls.key"]).To(Equal([]byte("")))
				Expect(oldRootCaSecret.Labels["x509-certificate-exporter"]).Should(Equal("scrape"))

				caStoreSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "ca-store", Namespace: namespace}, caStoreSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(caStoreSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(caStoreSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(caStoreSecret.Data["ca.crt"]).To(Equal(activeRootCaSecret.Data["tls.crt"]))
				Expect(caStoreSecret.Labels["x509-certificate-exporter"]).Should(Equal("scrape"))
			})

			It("should set the reconciled condition", func() {
				condition := conditionHelper.GetCondition(instance, conditions.IngestPkiReconciled)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Succeeded))
				Expect(condition.Message).To(Equal("Successfully reconciled resources"))
			})
		})

		When("Reconcile() is called with root CA rotation is requested but not yet triggered", func() {
			BeforeEach(func() {
				result, err := ingestPkiReconciler.Reconcile(ctx, instance) // secrets are already created
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
				instance.Spec.RotateRootCA = true
			})

			It("should trigger rotation", func() {
				result, err := ingestPkiReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				activeRootCaSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-active", Namespace: namespace}, activeRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(activeRootCaSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(activeRootCaSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(len(activeRootCaSecret.Data["tls.crt"])).To(BeNumerically(">", 0))
				Expect(len(activeRootCaSecret.Data["tls.key"])).To(BeNumerically(">", 0))
				Expect(activeRootCaSecret.Labels["x509-certificate-exporter"]).Should(Equal("scrape"))

				oldRootCaSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-old", Namespace: namespace}, oldRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(oldRootCaSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(oldRootCaSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(len(oldRootCaSecret.Data["tls.crt"])).To(BeNumerically(">", 0))
				Expect(len(oldRootCaSecret.Data["tls.key"])).To(BeNumerically(">", 0))
				Expect(oldRootCaSecret.Data["tls.crt"]).ToNot(Equal(activeRootCaSecret.Data["tls.crt"]))
				Expect(oldRootCaSecret.Labels["x509-certificate-exporter"]).Should(Equal("scrape"))

				caCert := append(activeRootCaSecret.Data["tls.crt"], oldRootCaSecret.Data["tls.crt"]...)

				caStoreSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "ca-store", Namespace: namespace}, caStoreSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(caStoreSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(caStoreSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(caStoreSecret.Data["ca.crt"]).To(Equal(caCert))
				Expect(caStoreSecret.Labels["x509-certificate-exporter"]).Should(Equal("scrape"))
			})
		})

		When("Reconcile() is called with root CA rotation is requested and already triggered", func() {
			BeforeEach(func() {
				// make sure the secrets are already created
				result, err := ingestPkiReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				instance.Spec.RotateRootCA = true

				// trigger rotation
				result, err = ingestPkiReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
			})

			It("should not trigger rotation", func() {
				activeRootCaSecret := &corev1.Secret{}
				err := k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-active", Namespace: namespace}, activeRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				activeRootCaSecretResourceVersionOld := activeRootCaSecret.ObjectMeta.ResourceVersion

				oldRootCaSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-old", Namespace: namespace}, oldRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				oldRootCaSecretResourceVersionOld := oldRootCaSecret.ObjectMeta.ResourceVersion

				caStoreSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "ca-store", Namespace: namespace}, caStoreSecret)
				Expect(err).NotTo(HaveOccurred())
				caStoreSecretResourceVersionOld := caStoreSecret.ObjectMeta.ResourceVersion

				// check on next reconcile loop
				result, err := ingestPkiReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				activeRootCaSecret = &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-active", Namespace: namespace}, activeRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(activeRootCaSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(activeRootCaSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				activeRootCaSecretResourceVersionNew := activeRootCaSecret.ObjectMeta.ResourceVersion
				Expect(activeRootCaSecretResourceVersionNew).To(Equal(activeRootCaSecretResourceVersionOld))

				oldRootCaSecret = &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-old", Namespace: namespace}, oldRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(oldRootCaSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(oldRootCaSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				oldRootCaSecretResourceVersionNew := oldRootCaSecret.ObjectMeta.ResourceVersion
				Expect(oldRootCaSecretResourceVersionNew).To(Equal(oldRootCaSecretResourceVersionOld))

				caStoreSecret = &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "ca-store", Namespace: namespace}, caStoreSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(caStoreSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(caStoreSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				caStoreSecretResourceVersionNew := caStoreSecret.ObjectMeta.ResourceVersion
				Expect(caStoreSecretResourceVersionNew).To(Equal(caStoreSecretResourceVersionOld))
			})
		})

		When("Reconcile() is called after a previous root CA rotation with root CA rotation unset", func() {
			BeforeEach(func() {
				// make sure the secrets are already created
				result, err := ingestPkiReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				instance.Spec.RotateRootCA = true

				// trigger rotation
				result, err = ingestPkiReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				instance.Spec.RotateRootCA = false
			})

			It("should complete rotation", func() {
				activeRootCaSecret := &corev1.Secret{}
				err := k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-active", Namespace: namespace}, activeRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				activeRootCaSecretResourceVersionOld := activeRootCaSecret.ObjectMeta.ResourceVersion

				oldRootCaSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-old", Namespace: namespace}, oldRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(len(oldRootCaSecret.Data["tls.crt"])).To(BeNumerically(">", 0))
				Expect(len(oldRootCaSecret.Data["tls.key"])).To(BeNumerically(">", 0))
				Expect(oldRootCaSecret.Data["tls.crt"]).ToNot(Equal(activeRootCaSecret.Data["tls.crt"]))
				oldRootCaSecretResourceVersionOld := oldRootCaSecret.ObjectMeta.ResourceVersion

				caCertOld := append(activeRootCaSecret.Data["tls.crt"], oldRootCaSecret.Data["tls.crt"]...)

				caStoreSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "ca-store", Namespace: namespace}, caStoreSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(caStoreSecret.Data["ca.crt"]).To(Equal(caCertOld))
				caStoreSecretResourceVersionOld := caStoreSecret.ObjectMeta.ResourceVersion

				// check on next reconcile loop
				result, err := ingestPkiReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				activeRootCaSecret = &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-active", Namespace: namespace}, activeRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(activeRootCaSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(activeRootCaSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				activeRootCaSecretResourceVersionNew := activeRootCaSecret.ObjectMeta.ResourceVersion
				Expect(activeRootCaSecretResourceVersionNew).To(Equal(activeRootCaSecretResourceVersionOld))

				oldRootCaSecret = &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "root-ca-old", Namespace: namespace}, oldRootCaSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(oldRootCaSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(oldRootCaSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(oldRootCaSecret.Data["tls.crt"]).To(Equal([]byte("")))
				Expect(oldRootCaSecret.Data["tls.key"]).To(Equal([]byte("")))
				oldRootCaSecretResourceVersionNew := oldRootCaSecret.ObjectMeta.ResourceVersion
				Expect(oldRootCaSecretResourceVersionNew).ToNot(Equal(oldRootCaSecretResourceVersionOld))

				caCertNew := activeRootCaSecret.Data["tls.crt"]

				caStoreSecret = &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "ca-store", Namespace: namespace}, caStoreSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(caStoreSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(caStoreSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(caStoreSecret.Data["ca.crt"]).To(Equal(caCertNew))
				caStoreSecretResourceVersionNew := caStoreSecret.ObjectMeta.ResourceVersion
				Expect(caStoreSecretResourceVersionNew).ToNot(Equal(caStoreSecretResourceVersionOld))
			})
		})
	})

	Context("CleanUp", func() {
		It("should return no error and schedule no requeue", func() {
			result, err := ingestPkiReconciler.CleanUp(ctx, instance)
			Expect(err).NotTo(HaveOccurred())
			Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
		})
	})

	Context("GetValidConditionNames", func() {
		It("should return a list of valid condition names", func() {
			Expect(ingestPkiReconciler.GetValidConditionNames(instance)).To(Equal([]string{
				conditions.IngestPkiReconciled,
			}))
		})
	})

	Context("GetName", func() {
		It("should return the name of the reconciler", func() {
			Expect(ingestPkiReconciler.GetName()).To(Equal("IngestPkiReconciler"))
		})
	})
})
