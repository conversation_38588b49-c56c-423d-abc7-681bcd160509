package reconcilers

import (
	"bytes"
	"context"
	"crypto/rsa"
	"crypto/x509"
	"fmt"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

const (
	IngestPkiReconcilerName = "IngestPkiReconciler"

	// cert validity period values
	certValidityYears  = 10
	certValidityMonths = 0
	certValidityDays   = 0

	caKeySize = 4096
)

type IngestPkiReconciler struct {
	client             client.Client
	conditionHelper    conditions.ConditionHelper
	resourceReconciler reconciler.ResourceReconciler
}

func NewIngestPkiReconciler(client client.Client,
	conditionHelper conditions.ConditionHelper,
	resourceReconciler reconciler.ResourceReconciler) ComponentReconciler {
	return &IngestPkiReconciler{
		client:             client,
		conditionHelper:    conditionHelper,
		resourceReconciler: resourceReconciler,
	}
}

// Reconcile is used to reconcile all Ingest related Root CA certificates. It make sure the Root CA certificates
// are available for the reconciled instance. The 'root-ca-active' Secret is holding the active Root CA in use.
// The 'root-ca-old' Secret is holding the previously used Root CA. The 'ca-store' Secret is holding the combined
// list of certificates from both 'root-ca-active' and 'root-ca-old', accessed by Nginx controller while ingesting.
// Reconcile also handles Root CA rotation when requested.
func (r *IngestPkiReconciler) Reconcile(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	result := reconciler.CombinedResult{}

	// Create active root CA if it does not exist
	activeRootCANamespacedName := builders.BuildActiveRootCANamespaceName(instance)
	activeRootCASecret := &corev1.Secret{}
	exist, err := r.getSecret(ctx, activeRootCANamespacedName, activeRootCASecret)
	if err != nil {
		return ctrl.Result{}, err
	}
	if !exist {
		//nolint:govet // ToDo: Refactor this to not have shadow declarations
		data, err := r.generateActiveRootCAData(instance)
		if err != nil {
			return ctrl.Result{}, err
		}
		activeRootCASecret = builders.BuildActiveRootCASecret(instance, data)
	}
	result.CombineErr(r.setOrUpdateControllerReference(instance, activeRootCASecret))
	result.Combine(r.resourceReconciler.ReconcileResource(activeRootCASecret, reconciler.StatePresent))

	// Create old root CA if does not exists
	oldRootCANamespacedName := builders.BuildOldRootCANamespaceName(instance)
	oldRootCASecret := &corev1.Secret{}
	exist, err = r.getSecret(ctx, oldRootCANamespacedName, oldRootCASecret)
	if err != nil {
		return ctrl.Result{}, err
	}
	if !exist {
		data := r.generateOldRootCAData()
		oldRootCASecret = builders.BuildOldRootCASecret(instance, data)
	}
	result.CombineErr(r.setOrUpdateControllerReference(instance, oldRootCASecret))
	result.Combine(r.resourceReconciler.ReconcileResource(oldRootCASecret, reconciler.StatePresent))

	// Handle root CA rotation
	result.Combine(r.handleRootCARotation(ctx, instance))

	// Create ca-store if does not exists
	caStoreNamespacedName := builders.BuildCAStoreNamespaceName(instance)
	caStoreSecret := &corev1.Secret{}
	exist, err = r.getSecret(ctx, caStoreNamespacedName, caStoreSecret)
	if err != nil {
		return ctrl.Result{}, err
	}
	if !exist {
		data := r.generateCAStoreData()
		caStoreSecret = builders.BuildCAStoreSecret(instance, data)
	}
	result.CombineErr(r.setOrUpdateControllerReference(instance, caStoreSecret))
	result.Combine(r.resourceReconciler.ReconcileResource(caStoreSecret, reconciler.StatePresent))

	// Validate ca-store
	result.Combine(r.validateAndUpdateCaStore(ctx, instance))

	// Set reconcile condition eventually
	r.conditionHelper.SetReconcileCondition(instance, conditions.IngestPkiReconciled, result)
	return result.Result, result.Err
}

func (r *IngestPkiReconciler) CleanUp(context.Context, *clv1beta1.Instance) (ctrl.Result, error) {
	// Nothing to do here, as the DataPrepper resources are owned by the instance
	return ctrl.Result{}, nil
}

// GetValidConditionNames returns a list of condition names related to the component.
// If the component can be disabled AND is disabled, only the '...Reconciled' condition should be returned.
func (r *IngestPkiReconciler) GetValidConditionNames(*clv1beta1.Instance) []string {
	validConditions := []string{
		conditions.IngestPkiReconciled,
	}
	return validConditions
}

// UpdateStatusConditions updates the components status condition(s). This method is called frequently and
// should be lightweight. It should only update the local instance, which will be persisted by the watcher
// after all component reconcilers have been called.
func (r *IngestPkiReconciler) UpdateStatusConditions(context.Context, *clv1beta1.Instance) bool {
	return false
}

// GetName returns the name of the reconciler for monitoring and logging purposes.
func (r *IngestPkiReconciler) GetName() string {
	return IngestPkiReconcilerName
}

// generateActiveRootCAData generates new 'root-ca-active' Secret certificate/key data.
func (r *IngestPkiReconciler) generateActiveRootCAData(instance *clv1beta1.Instance) (map[string][]byte, error) {
	caCert, caPrivateKey, err := r.generateCA(instance.Namespace)
	if err != nil {
		return nil, err
	}

	data := make(map[string][]byte)
	data["tls.crt"] = utils.ConvertCertToPEM(caCert.Raw)
	data["tls.key"] = utils.ConvertKeyToPEM(x509.MarshalPKCS1PrivateKey(caPrivateKey))

	return data, nil
}

// generateOldRootCAData generates empty 'root-ca-old' Secret data.
func (r *IngestPkiReconciler) generateOldRootCAData() map[string][]byte {
	data := make(map[string][]byte)
	data["tls.crt"] = []byte("") // empty string as initial cert
	data["tls.key"] = []byte("") // empty string as initial key

	return data
}

// generateCAStoreData generates empty 'ca-store' Secret data.
func (r *IngestPkiReconciler) generateCAStoreData() map[string][]byte {
	data := make(map[string][]byte)
	data["ca.crt"] = []byte("") // empty string as initial cert

	return data
}

// generateCA generates CA certificate/key by passing certificate parameters.
func (r *IngestPkiReconciler) generateCA(ns string) (*x509.Certificate, *rsa.PrivateKey, error) {
	uniqueInt, err := utils.GenerateRandomInt(6)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to generate random integer: %w", err)
	}

	commonName := "root-ca-" + ns + "-" + uniqueInt.String()
	certValidityPeriod := time.Now().AddDate(certValidityYears, certValidityMonths, certValidityDays)

	caCert, caPrivateKey, err := utils.GenerateCustomCA(
		commonName, caKeySize, certValidityPeriod, uniqueInt)
	if err != nil {
		return caCert, caPrivateKey, fmt.Errorf("failed to create root CA certificate: %w", err)
	}

	return caCert, caPrivateKey, nil
}

// handleRootCARotation handles the Root CA rotation based on the customer request.
// If requested and not done yet, it updates the 'root-ca-active' Secret with a new certificate/key
// while keeping the old certificate/key in the 'root-ca-old' Secret.
// If the rotation is set as finished, the 'root-ca-old' is updated as empty.
func (r *IngestPkiReconciler) handleRootCARotation(ctx context.Context,
	instance *clv1beta1.Instance) (*reconcile.Result, error) {
	result := reconciler.CombinedResult{}

	oldRootCANamespacedName := builders.BuildOldRootCANamespaceName(instance)
	oldRootCASecret := &corev1.Secret{}
	exist, err := r.getSecret(ctx, oldRootCANamespacedName, oldRootCASecret)
	if err != nil || !exist {
		result.CombineErr(fmt.Errorf("failed to fetch Secret: '%s:%s' during root CA rotation: %w",
			oldRootCANamespacedName.Namespace, oldRootCANamespacedName.Name, err))

		return &result.Result, result.Err
	}

	// RotateRootCA = true indicate that the root CA rotation is requested, make sure
	// the old root CA contain cert/key, if not, do a copy of active CA to old CA and
	// issue new cert/key to update the active root CA.
	if instance.Spec.RotateRootCA {
		activeRootCANamespacedName := builders.BuildActiveRootCANamespaceName(instance)
		activeRootCASecret := &corev1.Secret{}
		exist, err = r.getSecret(ctx, activeRootCANamespacedName, activeRootCASecret)
		if err != nil || !exist {
			result.CombineErr(fmt.Errorf("failed to fetch Secret: '%s:%s' during root CA rotation: %w",
				activeRootCANamespacedName.Namespace, activeRootCANamespacedName.Name, err))

			return &result.Result, result.Err
		}

		// Following section will be executed if root CA rotation is requested but not yet completed.
		if bytes.Equal(oldRootCASecret.Data["tls.crt"], []byte("")) ||
			bytes.Equal(oldRootCASecret.Data["tls.key"], []byte("")) ||
			bytes.Equal(oldRootCASecret.Data["tls.key"], activeRootCASecret.Data["tls.key"]) {
			// Issue a new CA cert/key first
			//nolint:govet // ToDo: Refactor this to not have shadow declarations
			caCert, caPrivateKey, err := r.generateCA(activeRootCANamespacedName.Namespace)
			if err != nil {
				result.CombineErr(fmt.Errorf("failed to generate new root CA during root CA rotation: %w", err))
				return &result.Result, result.Err
			}

			// Update old root CA
			dataOld := make(map[string][]byte)
			dataOld["tls.crt"] = activeRootCASecret.Data["tls.crt"]
			dataOld["tls.key"] = activeRootCASecret.Data["tls.key"]
			oldRootCASecret = builders.BuildOldRootCASecret(instance, dataOld)
			if err = r.setOrUpdateControllerReference(instance, oldRootCASecret); err != nil {
				return &result.Result, err
			}
			//nolint:govet // ToDo: Refactor this to not have shadow declarations
			if rs, err := r.resourceReconciler.ReconcileResource(oldRootCASecret, reconciler.StatePresent); err != nil {
				result.CombineErr(fmt.Errorf("failed to update old Secret: '%s:%s'"+
					"during root CA rotation with rotate root CA requested: %v",
					oldRootCANamespacedName.Namespace, oldRootCANamespacedName.Name, err))

				return rs, result.Err
			}

			// Update active root CA at the end
			dataActive := make(map[string][]byte)
			dataActive["tls.crt"] = utils.ConvertCertToPEM(caCert.Raw)
			dataActive["tls.key"] = utils.ConvertKeyToPEM(x509.MarshalPKCS1PrivateKey(caPrivateKey))
			activeRootCASecret = builders.BuildActiveRootCASecret(instance, dataActive)
			if err = r.setOrUpdateControllerReference(instance, activeRootCASecret); err != nil {
				return &result.Result, err
			}
			if rs, err := r.resourceReconciler.ReconcileResource(activeRootCASecret, reconciler.StatePresent); err != nil {
				result.CombineErr(fmt.Errorf("failed to update active Secret: '%s:%s'"+
					"during root CA rotation with rotate root CA requested: %v",
					activeRootCANamespacedName.Namespace, activeRootCANamespacedName.Name, err))

				return rs, result.Err
			}
		}

		return &result.Result, result.Err
	}

	// RotateRootCA = false indicate root CA rotation is not requested, just need to make sure
	// that the old root CA does not contain any certificate and private key.
	if !bytes.Equal(oldRootCASecret.Data["tls.crt"], []byte("")) ||
		!bytes.Equal(oldRootCASecret.Data["tls.key"], []byte("")) {
		data := make(map[string][]byte)
		data["tls.crt"] = []byte("")
		data["tls.key"] = []byte("")
		oldRootCASecret = builders.BuildOldRootCASecret(instance, data)
		if err = r.setOrUpdateControllerReference(instance, oldRootCASecret); err != nil {
			return &result.Result, err
		}
		if rs, err := r.resourceReconciler.ReconcileResource(oldRootCASecret, reconciler.StatePresent); err != nil {
			result.CombineErr(fmt.Errorf("failed to update old Secret: '%s:%s'"+
				"during root CA rotation with rotate root CA not requested: %v",
				oldRootCANamespacedName.Namespace, oldRootCANamespacedName.Name, err))

			return rs, result.Err
		}
	}

	return &result.Result, result.Err
}

// validateAndUpdateCaStore validate and update the 'ca-store' Secret to contain certificates
// from both 'root-ca-active' and 'root-ca-old' Secrets.
func (r *IngestPkiReconciler) validateAndUpdateCaStore(ctx context.Context,
	instance *clv1beta1.Instance) (*reconcile.Result, error) {
	result := reconciler.CombinedResult{}

	activeRootCANamespacedName := builders.BuildActiveRootCANamespaceName(instance)
	activeRootCASecret := &corev1.Secret{}
	exist, err := r.getSecret(ctx, activeRootCANamespacedName, activeRootCASecret)
	if err != nil || !exist {
		result.CombineErr(fmt.Errorf("failed to fetch Secret: '%s:%s' during validating ca-store: %w",
			activeRootCANamespacedName.Namespace, activeRootCANamespacedName.Name, err))

		return &result.Result, result.Err
	}

	oldRootCANamespacedName := builders.BuildOldRootCANamespaceName(instance)
	oldRootCASecret := &corev1.Secret{}
	exist, err = r.getSecret(ctx, oldRootCANamespacedName, oldRootCASecret)
	if err != nil || !exist {
		result.CombineErr(fmt.Errorf("failed to fetch Secret: '%s:%s' during validating ca-store: %w",
			oldRootCANamespacedName.Namespace, oldRootCANamespacedName.Name, err))

		return &result.Result, result.Err
	}

	caCert := activeRootCASecret.Data["tls.crt"]
	if !bytes.Equal(oldRootCASecret.Data["tls.crt"], []byte("")) {
		caCert = append(caCert, oldRootCASecret.Data["tls.crt"]...)
	}

	caStoreNamespacedName := builders.BuildCAStoreNamespaceName(instance)
	caStoreSecret := &corev1.Secret{}
	exist, err = r.getSecret(ctx, caStoreNamespacedName, caStoreSecret)
	if err != nil || !exist {
		result.CombineErr(fmt.Errorf("failed to fetch Secret: '%s:%s' during validating ca-store: %w",
			caStoreNamespacedName.Namespace, caStoreNamespacedName.Name, err))

		return &result.Result, result.Err
	}

	// check if 'ca-store' Secret has already been updated
	if bytes.Equal(caCert, caStoreSecret.Data["ca.crt"]) {
		// certificates are equal, do nothing
		return &result.Result, result.Err
	}

	binaryData := make(map[string][]byte)
	binaryData["ca.crt"] = caCert
	caStoreSecret = builders.BuildCAStoreSecret(instance, binaryData)
	if err = r.setOrUpdateControllerReference(instance, caStoreSecret); err != nil {
		return &result.Result, err
	}
	if rs, err := r.resourceReconciler.ReconcileResource(caStoreSecret, reconciler.StatePresent); err != nil {
		result.CombineErr(fmt.Errorf("failed to update Secret: '%s:%s' during validating ca-store: %w",
			caStoreSecret.Namespace, caStoreSecret.Name, err))

		return rs, result.Err
	}

	return &result.Result, result.Err
}

func (r *IngestPkiReconciler) setOrUpdateControllerReference(instance *clv1beta1.Instance,
	secret *corev1.Secret) error {
	updatedOwnerRefs := make([]metav1.OwnerReference, 0)

	for _, ownerRef := range secret.OwnerReferences {
		if ownerRef.Controller != nil && *ownerRef.Controller {
			if ownerRef.UID == instance.GetUID() {
				// If the controller reference already exists as 'Instance', return early
				return nil
			}
		} else {
			updatedOwnerRefs = append(updatedOwnerRefs, ownerRef)
		}
	}

	secret.OwnerReferences = updatedOwnerRefs
	return ctrl.SetControllerReference(instance, secret, r.client.Scheme())
}

func (r *IngestPkiReconciler) getSecret(ctx context.Context,
	secretNamespaceName types.NamespacedName, secret *corev1.Secret) (bool, error) {
	err := r.client.Get(ctx, secretNamespaceName, secret)
	if err != nil {
		if apierrors.IsNotFound(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
