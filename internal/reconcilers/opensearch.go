package reconcilers

import (
	"context"
	"errors"
	"fmt"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers/helpers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/templating"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	semverv3 "github.com/Masterminds/semver/v3"
	"github.com/cisco-open/operator-tools/pkg/reconciler"
	"github.com/go-logr/logr"
	appsv1 "k8s.io/api/apps/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	openSearchReconcilerName = "OpenSearchReconciler"
)

type OpenSearchReconciler struct {
	client                       client.Client
	conditionHelper              conditions.ConditionHelper
	resourceReconciler           reconciler.ResourceReconciler
	topologyHelper               helpers.TopologyHelper
	imageHelper                  helpers.ImageHelper
	passwordStoreReader          services.PasswordStoreService
	openSearchService            services.OpenSearchService
	openSearchTopologyService    services.OpenSearchTopologyService
	featureConfig                services.FeatureConfiguration
	templatedConfiguration       templating.TemplatedConfig
	templatedSecurity            templating.TemplatedConfig
	templatedComposableTemplates templating.TemplatedConfig
	templatedISMPolicies         templating.TemplatedConfig
	logger                       logr.Logger
}

func NewOpenSearchReconciler(client client.Client, conditionHelper conditions.ConditionHelper,
	resourceReconciler reconciler.ResourceReconciler, topologyHelper helpers.TopologyHelper,
	imageHelper helpers.ImageHelper, passwordStoreReader services.PasswordStoreService,
	openSearchService services.OpenSearchService, openSearchTopologyService services.OpenSearchTopologyService,
	featureConfig services.FeatureConfiguration, templatePath string) ComponentReconciler {
	return &OpenSearchReconciler{
		logger:                    ctrl.Log.WithName("opensearch-reconciler"),
		client:                    client,
		conditionHelper:           conditionHelper,
		resourceReconciler:        resourceReconciler,
		topologyHelper:            topologyHelper,
		imageHelper:               imageHelper,
		passwordStoreReader:       passwordStoreReader,
		openSearchService:         openSearchService,
		openSearchTopologyService: openSearchTopologyService,
		featureConfig:             featureConfig,
		templatedConfiguration:    templating.NewTemplatedConfig(templatePath+"/config", "opensearch.yml", "init.sh", "entry.sh", "log4j2.properties"),
		templatedSecurity: templating.NewTemplatedConfig(
			templatePath+"/security",
			"action_groups.yml",
			"audit.yml",
			"config.yml",
			"internal_users.yml",
			"nodes_dn.yml",
			"roles_mapping.yml",
			"roles.yml",
			"tenants.yml",
			"whitelist.yml",
		),
		templatedComposableTemplates: templating.NewTemplatedConfig(templatePath+"/composable-templates", "component_templates.yml", "index_templates.yml"),
		templatedISMPolicies:         templating.NewTemplatedConfig(templatePath+"/ism-policies", "ism_policies.yml"),
	}
}

//nolint:funlen // Just a complex function
func (r *OpenSearchReconciler) Reconcile(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	result := reconciler.CombinedResult{}
	cfg := config.GetInstance()

	// Check if the instance CR is lacking the required configuration.
	if len(instance.Spec.OpenSearch.Cluster.NodePools) == 0 || instance.Spec.OpenSearch.Cluster.Name == "" ||
		instance.Spec.OpenSearch.IndexManagement.RetentionPeriodDays == 0 {
		result.CombineErr(errors.New("opensearch configuration is not available in instance CR"))
		r.conditionHelper.SetReconcileCondition(instance, conditions.OpenSearchReconciled, result)

		// Do not return an error here, as retrying would not help in this case
		return result.Result, nil
	}

	// Determine image channel to be used
	desiredImageChannel := config.StableChannel
	if r.featureConfig.IsFeatureSet(instance, config.FeatureFlagUseOpenSearchDevelopmentVersion) && config.GetInstance().GetImages().OpenSearch.Development != "" {
		desiredImageChannel = config.DevelopmentChannel
	} else if r.featureConfig.IsFeatureSet(instance, config.FeatureFlagUpgradeToOpenSearchV2) && config.GetInstance().GetImages().OpenSearch.Preview != "" {
		desiredImageChannel = config.PreviewChannel
	}

	// Discover current topology
	currentTopology, err := r.openSearchTopologyService.Discover(ctx, instance)
	if err != nil {
		result.CombineErr(err)
		r.conditionHelper.SetReconcileCondition(instance, conditions.OpenSearchReconciled, result)

		return result.Result, result.Err
	}

	// Determine available image channels based on the instance CR and allowed feature flags
	availableImages := r.imageHelper.GetAvailableImages(instance, cfg.GetImages().OpenSearch)

	// Determine the desired topology and patch
	patch, err := r.topologyHelper.Process(ctx, instance, currentTopology, availableImages, desiredImageChannel)
	if err != nil {
		result.CombineErr(err)
		r.conditionHelper.SetReconcileCondition(instance, conditions.OpenSearchReconciled, result)

		return result.Result, result.Err
	}

	// Update the cluster target version in the instance CR
	instance.Status.OpenSearchClusterTargetVersion = patch.TargetImage.Version.String()

	// Determine the cluster support version from the instance CR. This version indicates the lowest
	// version that needs to be supported by dependent components and contents.
	supportVersion := patch.TargetImage.Version
	if instance.Status.OpenSearchClusterSupportVersion != "" {
		version, err2 := semverv3.NewVersion(instance.Status.OpenSearchClusterSupportVersion)
		if err2 == nil {
			supportVersion = version
		} else {
			r.logger.Error(err2, "Failed to parse OpenSearch cluster support version", "version", instance.Status.OpenSearchClusterSupportVersion)
			// Don't return here, just log the error. This is not critical for the reconciliation.
		}
	}

	// Get all OpenSearch credentials object from password-store
	credentials, err := r.passwordStoreReader.GetAllOpenSearchCredentials(ctx, instance)
	if err != nil {
		r.conditionHelper.SetReconcileCondition(instance, conditions.OpenSearchReconciled, result)

		return ctrl.Result{}, err
	}

	// Determine the security admin group based on the authentication method
	securityAdminGroup := ""
	if instance.Spec.OpenSearch.AuthSAML.Enabled {
		securityAdminGroup = instance.Spec.OpenSearch.AuthSAML.AdminGroup
	} else if instance.Spec.OpenSearch.AuthOpenID.Enabled {
		securityAdminGroup = instance.Spec.OpenSearch.AuthOpenID.AdminGroup
	}

	// Determine the number of primary shards and replicas
	// We might want to make this configurable via instance CR
	numberOfDataNodes := int(patch.Topology.GetNumberOfDataNodes())
	minNumberOfDataNodes := int(patch.Topology.GetMinNumberOfDataNodes())
	replicaShards := 1
	if numberOfDataNodes < 2 {
		replicaShards = 0
	}

	// Source object for config templates
	configSource := struct {
		Cluster                     clv1beta1.Cluster
		InitialClusterManagerNodes  []string
		IsSingleNodeCluster         bool
		Credentials                 map[string]models.Credentials
		SecurityAdminGroup          string
		TargetVersion               *semverv3.Version
		SupportVersion              *semverv3.Version
		NumberOfDataNodes           int
		MinNumberOfDataNodes        int
		ReplicaShards               int
		RetentionPeriodDays         int32
		RolloverMinIndexAge         string
		RolloverMinPrimaryShardSize string
	}{
		Cluster:                    instance.Spec.OpenSearch.Cluster,
		InitialClusterManagerNodes: patch.Topology.GetClusterManagerPodNames(),
		IsSingleNodeCluster:        patch.Topology.IsSingleNodeCluster(),
		Credentials:                credentials,
		SecurityAdminGroup:         securityAdminGroup,
		TargetVersion:              patch.TargetImage.Version,
		SupportVersion:             supportVersion,
		NumberOfDataNodes:          numberOfDataNodes,
		MinNumberOfDataNodes:       minNumberOfDataNodes,
		ReplicaShards:              replicaShards,
		RetentionPeriodDays:        instance.Spec.OpenSearch.IndexManagement.RetentionPeriodDays,
		RolloverMinIndexAge:        instance.Spec.OpenSearch.IndexManagement.RolloverMinIndexAge,
		RolloverMinPrimaryShardSize: utils.ConvertQuantityToOpenSearchSizeUnit(
			instance.Spec.OpenSearch.IndexManagement.RolloverMinPrimaryShardSize),
	}

	// Reconcile OpenSearch resources
	serviceAccount := builders.BuildOpenSearchServiceAccount(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, serviceAccount, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(serviceAccount, reconciler.StateCreated))

	clusterRole := builders.BuildOpenSearchClusterRole()
	result.Combine(r.resourceReconciler.ReconcileResource(clusterRole, reconciler.StateCreated))

	clusterRoleBinding := builders.BuildOpenSearchClusterRoleBinding(instance)
	result.Combine(r.resourceReconciler.ReconcileResource(clusterRoleBinding, reconciler.StateCreated))

	configMap := builders.BuildOpenSearchConfigMap(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, configMap, r.client.Scheme()))
	result.CombineErr(r.templatedConfiguration.PopulateConfigMap(templating.PARSE_AS_FILES, configSource, configMap))
	result.Combine(r.resourceReconciler.ReconcileResource(configMap, reconciler.StatePresent))

	securitySecret := builders.BuildOpenSearchSecuritySecret(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, securitySecret, r.client.Scheme()))
	result.CombineErr(r.templatedSecurity.PopulateSecret(templating.PARSE_AS_FILES, configSource, securitySecret))
	result.Combine(r.resourceReconciler.ReconcileResource(securitySecret, reconciler.StatePresent))

	discoveryService := builders.BuildOpenSearchDiscoveryService(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, discoveryService, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(discoveryService, reconciler.StatePresent))

	clientService := builders.BuildOpenSearchClientService(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, clientService, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(clientService, reconciler.StatePresent))

	// TODO: Remove once all dependencies on the legacy client service are removed
	legacyClientService := builders.BuildLegacyOpenSearchClientService(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, legacyClientService, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(legacyClientService, reconciler.StatePresent))

	ingress := builders.BuildOpenSearchIngress(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, ingress, r.client.Scheme()))
	if instance.Spec.OpenSearch.Cluster.ExposeHttpEndpoint {
		result.Combine(r.resourceReconciler.ReconcileResource(ingress, reconciler.StatePresent))
	} else {
		result.Combine(r.resourceReconciler.ReconcileResource(ingress, reconciler.StateAbsent))
	}

	// Reconcile composable-templates config map, which is uploaded by instance-maintainer
	composableTemplatesConfig := builders.BuildOpenSearchComposableTemplatesConfigMap(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, composableTemplatesConfig, r.client.Scheme()))
	result.CombineErr(r.templatedComposableTemplates.PopulateConfigMap(templating.MERGE_ALL, configSource, composableTemplatesConfig))
	result.Combine(r.resourceReconciler.ReconcileResource(composableTemplatesConfig, reconciler.StatePresent))

	// Reconcile ism-policies config map, which is uploaded by instance-maintainer
	ismPoliciesConfig := builders.BuildOpenSearchISMPoliciesConfigMap(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, ismPoliciesConfig, r.client.Scheme()))
	result.CombineErr(r.templatedISMPolicies.PopulateConfigMap(templating.MERGE_ALL, configSource, ismPoliciesConfig))
	result.Combine(r.resourceReconciler.ReconcileResource(ismPoliciesConfig, reconciler.StatePresent))

	// Reconcile node groups that are supposed to be present
	for _, nodeGroup := range patch.Topology.NodeGroups {
		result.CombineErr(ctrl.SetControllerReference(instance, nodeGroup.HeadlessService, r.client.Scheme()))
		result.Combine(r.resourceReconciler.ReconcileResource(nodeGroup.HeadlessService, reconciler.StatePresent))

		result.CombineErr(ctrl.SetControllerReference(instance, nodeGroup.PodDisruptionBudget, r.client.Scheme()))
		result.Combine(r.resourceReconciler.ReconcileResource(nodeGroup.PodDisruptionBudget, reconciler.StatePresent))

		result.CombineErr(ctrl.SetControllerReference(instance, nodeGroup.StatefulSet, r.client.Scheme()))
		result.CombineErr(utils.SetHashFromCM(nodeGroup.StatefulSet.Spec.Template.Annotations, configMap))
		result.Combine(r.resourceReconciler.ReconcileResource(nodeGroup.StatefulSet, reconciler.StatePresent))
	}

	// Reconcile node groups that are supposed to be absent
	// Resources in absent node groups might be incomplete, so we need to do nil checks here.
	// Only for the StatefulSet we can assume that it is present.
	for _, nodeGroup := range patch.AbsentNodeGroups {
		if nodeGroup.HeadlessService != nil {
			result.CombineErr(ctrl.SetControllerReference(instance, nodeGroup.HeadlessService, r.client.Scheme()))
			result.Combine(r.resourceReconciler.ReconcileResource(nodeGroup.HeadlessService, reconciler.StateAbsent))
		}

		if nodeGroup.PodDisruptionBudget != nil {
			result.CombineErr(ctrl.SetControllerReference(instance, nodeGroup.PodDisruptionBudget, r.client.Scheme()))
			result.Combine(r.resourceReconciler.ReconcileResource(nodeGroup.PodDisruptionBudget, reconciler.StateAbsent))
		}

		result.CombineErr(ctrl.SetControllerReference(instance, nodeGroup.StatefulSet, r.client.Scheme()))
		result.Combine(r.resourceReconciler.ReconcileResource(nodeGroup.StatefulSet, reconciler.StateAbsent))
	}

	// Set available condition
	result.CombineErr(r.updateAvailableCondition(ctx, instance, patch.Topology.NodeGroups))

	// Set reconcile condition eventually
	channelLabel := fmt.Sprintf("image channel: %s - %s", patch.TargetImage.Channel, patch.TargetImage.Version)
	r.conditionHelper.SetReconcileCondition(instance, conditions.OpenSearchReconciled, result, channelLabel)

	// Set initial autoscaling condition to avoid unnecessary waiting for the first autoscaling cycle
	r.setInitialAutoscalingCondition(instance)

	return result.Result, result.Err
}

// CleanUp cleans up resources outside the instance CR scope.
func (r *OpenSearchReconciler) CleanUp(_ context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	result := reconciler.CombinedResult{}

	// Cleanup ClusterRoleBinding
	clusterRoleBinding := builders.BuildOpenSearchClusterRoleBinding(instance)
	result.Combine(r.resourceReconciler.ReconcileResource(clusterRoleBinding, reconciler.StateAbsent))

	return result.Result, result.Err
}

// GetValidConditionNames returns a list of condition names related to the component.
// If the component can be disabled AND is disabled, only the '...Reconciled' condition should be returned.
func (r *OpenSearchReconciler) GetValidConditionNames(*clv1beta1.Instance) []string {
	return []string{
		conditions.OpenSearchReconciled,
		conditions.OpenSearchAvailable,
		conditions.OpenSearchHealth,
		conditions.OpenSearchAutoscaling,
	}
}

// UpdateStatusConditions updates the components status condition(s). This method is called frequently and
// should be lightweight. It should only update the local instance, which will be persisted by the watcher
// after all component reconcilers have been called.
func (r *OpenSearchReconciler) UpdateStatusConditions(ctx context.Context, instance *clv1beta1.Instance) bool {
	return r.conditionHelper.SetCondition(instance, r.openSearchService.GetStatusCondition(ctx, instance))
}

// GetName returns the name of the reconciler for monitoring and logging purposes.
func (r *OpenSearchReconciler) GetName() string {
	return openSearchReconcilerName
}

// Determine an overall available status based on the availability of the StatefulSets pods.
func (r *OpenSearchReconciler) updateAvailableCondition(ctx context.Context, instance *clv1beta1.Instance, nodeGroups []models.NodeGroup) error {
	overallStatus := metav1.ConditionTrue
	overallReason := conditions.Available
	statusMsgs := make([]string, 0)

	var cmNodesTotal, cmNodesAvailable, dataNodesTotal, dataNodesAvailable int32

	// Check the availability of the StatefulSets and calculate the number of available and total data and cluster-manager nodes
	for _, nodeGroup := range nodeGroups {
		sts := &appsv1.StatefulSet{}
		err := r.client.Get(ctx, client.ObjectKeyFromObject(nodeGroup.StatefulSet), sts)
		if err != nil {
			if apierrors.IsNotFound(err) {
				statusMsgs = append(statusMsgs, fmt.Sprintf("%s does not exist", sts.Name))
				overallStatus = metav1.ConditionFalse
				overallReason = conditions.NotFound
				continue
			}
			return fmt.Errorf("failed to get StatefulSet %s: %w", nodeGroup.StatefulSet.Name, err)
		}

		if nodeGroup.IsClusterManager() {
			cmNodesTotal += ptr.Deref(sts.Spec.Replicas, 0)
			cmNodesAvailable += sts.Status.ReadyReplicas
		}

		if nodeGroup.IsData() {
			dataNodesTotal += ptr.Deref(sts.Spec.Replicas, 0)
			dataNodesAvailable += sts.Status.ReadyReplicas
		}

		statusMsgs = append(statusMsgs, fmt.Sprintf("%d of %d %s replica(s) are available", sts.Status.ReadyReplicas, ptr.Deref(sts.Spec.Replicas, 0), sts.Name))
	}

	// Check if at least two thirds of cluster-manager nodes are available
	if cmNodesAvailable < (2*cmNodesTotal)/3 || (cmNodesTotal == 1 && cmNodesAvailable == 0) {
		overallStatus = metav1.ConditionFalse
		overallReason = conditions.NotAvailable
	}

	// Check if at least two thirds of data nodes are available
	if dataNodesAvailable < (2*dataNodesTotal)/3 || (dataNodesTotal == 1 && dataNodesAvailable == 0) {
		overallStatus = metav1.ConditionFalse
		overallReason = conditions.NotAvailable
	}

	// Set the available condition
	r.conditionHelper.SetCondition(instance, metav1.Condition{
		Type:    conditions.OpenSearchAvailable,
		Status:  overallStatus,
		Reason:  overallReason,
		Message: utils.Join(statusMsgs, ", "),
	})

	return nil
}

// setInitialAutoscalingCondition sets the initial autoscaling condition if it is not set yet.
func (r *OpenSearchReconciler) setInitialAutoscalingCondition(instance *clv1beta1.Instance) {
	if r.conditionHelper.GetCondition(instance, conditions.OpenSearchAutoscaling) == nil {
		r.conditionHelper.SetCondition(instance, metav1.Condition{
			Type:    conditions.OpenSearchAutoscaling,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.NotEligible,
			Message: "Instance is not yet eligible for autoscaling",
		})
	}
}
