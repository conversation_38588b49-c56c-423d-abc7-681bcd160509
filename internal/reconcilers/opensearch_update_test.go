package reconcilers_test

import (
	"context"
	"errors"
	"fmt"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	clientModels "github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("OpenSearchUpdateReconciler", func() {
	const (
		instanceName           = "cls-test"
		namespace              = "sf-test"
		openSearchTemplatePath = "../../templates/opensearch"
	)

	var (
		reconciler                reconcilers.ComponentReconciler
		instance                  *clv1beta1.Instance
		openSearchTopologyService services.OpenSearchTopologyService
		openSearchServiceMock     *fake.OpenSearchServiceMock
		stsCM                     *appsv1.StatefulSet
		stsData                   *appsv1.StatefulSet
		conditionHelper           = conditions.NewConditionHelper()
		ctx                       = context.Background()
	)

	BeforeEach(func() {
		openSearchServiceMock = fake.NewOpenSearchServiceMock()
		openSearchTopologyService = services.NewOpenSearchTopologyService(k8sClient)

		Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:        instanceName,
				Namespace:   namespace,
				Annotations: map[string]string{},
			},
			Spec: clv1beta1.InstanceSpec{
				OpenSearch: clv1beta1.OpenSearch{
					Cluster: clv1beta1.Cluster{
						Name: "cls-test",
						NodePools: []clv1beta1.NodePool{
							{
								Name: "cluster-manager",
								Autoscaler: clv1beta1.DiskUsageBasedAutoScaler{
									Autoscaler: clv1beta1.Autoscaler{
										MinReplicas: 3,
										MaxReplicas: 3,
									},
								},
								Resources: corev1.ResourceRequirements{},
								Roles:     []clv1beta1.NodeRole{clv1beta1.ClusterManager},
								Jvm:       "jvm-arguments",
								Persistence: clv1beta1.Persistence{
									DiskSize: resource.MustParse("1Gi"),
								},
							},
							{
								Name: "data",
								Autoscaler: clv1beta1.DiskUsageBasedAutoScaler{
									Autoscaler: clv1beta1.Autoscaler{
										MinReplicas: 2,
										MaxReplicas: 10,
									},
									DiskUsageThreshold: 60,
								},
								Resources: corev1.ResourceRequirements{},
								Roles:     []clv1beta1.NodeRole{clv1beta1.Data},
								Jvm:       "jvm-arguments",
								Persistence: clv1beta1.Persistence{
									DiskSize: resource.MustParse("100Gi"),
								},
							},
						},
						ExposeHttpEndpoint: true,
						MaxDataNodes:       4,
						AdditionalConfig: map[string]string{
							"rollover_min_primary_shard_size": "20Gi",
						},
					},
				},
			},
			Status: clv1beta1.InstanceStatus{
				OpenSearchClusterSupportVersion: "1.0.0",
				OpenSearchClusterTargetVersion:  "1.0.0",
			},
		}

		// Create the OpenSearch namespace
		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace,
			},
		}
		err := k8sClient.Get(ctx, types.NamespacedName{Name: namespace}, ns)
		if err != nil && apierrors.IsNotFound(err) {
			Expect(k8sClient.Create(ctx, ns)).To(Succeed())
		}

		// Create headless services for stsCM and stsData stateful sets
		hsCM := builders.BuildOpenSearchHeadlessService(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0])
		hsData := builders.BuildOpenSearchHeadlessService(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1])
		Expect(k8sClient.Create(ctx, hsCM)).To(Succeed())
		Expect(k8sClient.Create(ctx, hsData)).To(Succeed())

		// Create StatefulSets for cluster-manager and data node pools
		stsCM = builders.BuildOpenSearchStatefulSet(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0], "opensearch:1.0.0")
		stsData = builders.BuildOpenSearchStatefulSet(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1], "opensearch:1.0.0")
		stsCM.Spec.Replicas = ptr.To(int32(3))
		stsData.Spec.Replicas = ptr.To(int32(4))
		Expect(k8sClient.Create(ctx, stsCM)).To(Succeed())
		Expect(k8sClient.Create(ctx, stsData)).To(Succeed())

		// Create Pods for stsCM to prepare for testing pod updates
		for i := range 3 {
			pod := corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:      fmt.Sprintf("%s-%d", stsCM.Name, i),
					Namespace: namespace,
					Labels:    stsCM.Labels,
				},
				Spec: stsCM.Spec.Template.Spec,
			}
			pod.Spec.Volumes = append(pod.Spec.Volumes, corev1.Volume{
				Name: "data",
				VolumeSource: corev1.VolumeSource{
					PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
						ClaimName: fmt.Sprintf("%s-%d", stsCM.Name, i),
					},
				},
			})
			Expect(k8sClient.Create(ctx, &pod)).To(Succeed())
		}

		// Create Pods for stsData to prepare for testing pod updates
		for i := range 4 {
			pod := corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:      fmt.Sprintf("%s-%d", stsData.Name, i),
					Namespace: namespace,
					Labels:    stsData.Labels,
				},
				Spec: stsData.Spec.Template.Spec,
			}
			pod.Spec.Volumes = append(pod.Spec.Volumes, corev1.Volume{
				Name: "data",
				VolumeSource: corev1.VolumeSource{
					PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
						ClaimName: fmt.Sprintf("%s-%d", stsData.Name, i),
					},
				},
			})
			Expect(k8sClient.Create(ctx, &pod)).To(Succeed())
		}

		openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
			Status: clientModels.HealthStatusGreen,
		}, nil)

		reconciler = reconcilers.NewOpenSearchUpdateReconciler(k8sClient, conditionHelper, openSearchTopologyService, openSearchServiceMock)
	})

	AfterEach(func() {
		Expect(k8sClient.DeleteAllOf(ctx, &clv1beta1.Instance{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &appsv1.StatefulSet{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Service{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Pod{}, client.InNamespace(namespace))).To(Succeed())
	})

	Context("Reconcile", func() {
		It("should return without error", func() {
			result, err := reconciler.Reconcile(ctx, instance)
			Expect(err).ToNot(HaveOccurred())
			Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
		})

		When("OpenSearch is reconciled", func() {
			BeforeEach(func() {
				conditionHelper.SetCondition(instance, metav1.Condition{
					Type:   conditions.OpenSearchReconciled,
					Status: metav1.ConditionTrue,
					Reason: conditions.Succeeded,
				})
			})

			When("there are no pods to be updated", func() {
				BeforeEach(func() {
					stsData.Status.UpdateRevision = "1"
					stsData.Status.CurrentRevision = "1"
					stsData.Status.UpdatedReplicas = 4
					stsData.Status.AvailableReplicas = 4
					stsData.Status.ReadyReplicas = 4
					stsData.Status.Replicas = 4
					Expect(k8sClient.Status().Update(ctx, stsData)).To(Succeed())
				})

				It("should return without error", func() {
					result, err := reconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
				})

				It("should not restart (delete) any pods", func() {
					_, err := reconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
					Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
					Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())

					Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
					Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
					Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
					Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-3", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
				})

				It("should set the updated condition to True", func() {
					_, err := reconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpdated)).To(Equal(metav1.ConditionTrue))
				})

				It("should set the upgraded condition to True", func() {
					_, err := reconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpgraded)).To(Equal(metav1.ConditionTrue))
				})

				It("should set the cluster support version according to the target version", func() {
					instance.Status.OpenSearchClusterSupportVersion = ""

					_, err := reconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(instance.Status.OpenSearchClusterSupportVersion).To(Equal("1.0.0"))
				})

				When("an upgrade was completed previously", func() {
					BeforeEach(func() {
						conditionHelper.SetCondition(instance, metav1.Condition{
							Type:    conditions.OpenSearchUpgraded,
							Status:  metav1.ConditionFalse,
							Reason:  conditions.InProgress,
							Message: "Upgrading pods to latest image version",
						})
					})

					It("should set shard allocation to 'all'", func() {
						_, err := reconciler.Reconcile(ctx, instance)
						Expect(err).ToNot(HaveOccurred())
						Expect(openSearchServiceMock.IsSetShardAllocationCalled).To(BeTrue())
						Expect(openSearchServiceMock.SetShardAllocationSetting).To(Equal(clientModels.ShardAllocationAll))
					})

					When("setting the shard allocation fails", func() {
						BeforeEach(func() {
							openSearchServiceMock.PrepareSetShardAllocation(errors.New("failed to set shard allocation"))
						})

						It("should not return an error, but requeue", func() {
							result, err := reconciler.Reconcile(ctx, instance)
							Expect(err).ToNot(HaveOccurred())
							Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
						})

						It("should not set the upgraded condition to True", func() {
							_, err := reconciler.Reconcile(ctx, instance)
							Expect(err).ToNot(HaveOccurred())
							Expect(conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpgraded)).To(Equal(metav1.ConditionFalse))
						})
					})
				})
			})

			When("there are pods to be updated", func() {
				BeforeEach(func() {
					stsCM.Status.UpdateRevision = "2"
					stsCM.Status.CurrentRevision = "1"
					stsCM.Status.UpdatedReplicas = 0
					stsCM.Status.AvailableReplicas = 3
					stsCM.Status.ReadyReplicas = 3
					stsCM.Status.Replicas = 3
					Expect(k8sClient.Status().Update(ctx, stsCM)).To(Succeed())

					stsData.Status.UpdateRevision = "2"
					stsData.Status.CurrentRevision = "1"
					stsData.Status.UpdatedReplicas = 0
					stsData.Status.AvailableReplicas = 4
					stsData.Status.ReadyReplicas = 4
					stsData.Status.Replicas = 4
					Expect(k8sClient.Status().Update(ctx, stsData)).To(Succeed())
				})

				When("the updated condition is not set to False yet", func() {
					It("should return without error, but requeue", func() {
						result, err := reconciler.Reconcile(ctx, instance)
						Expect(err).ToNot(HaveOccurred())
						Expect(result).To(Equal(reconcile.Result{RequeueAfter: 15 * time.Second}))
					})

					It("should not restart (delete) any pods yet", func() {
						_, err := reconciler.Reconcile(ctx, instance)
						Expect(err).ToNot(HaveOccurred())
						Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
						Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
						Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())

						Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
						Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
						Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
						Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-3", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
					})

					It("should set the updated condition to False", func() {
						_, err := reconciler.Reconcile(ctx, instance)
						Expect(err).ToNot(HaveOccurred())
						Expect(conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpdated)).To(Equal(metav1.ConditionFalse))
					})
				})

				When("the image version is not updated (normal update procedure)", func() {
					It("does not set the upgraded condition", func() {
						_, err := reconciler.Reconcile(ctx, instance)
						Expect(err).ToNot(HaveOccurred())
						Expect(conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpgraded)).To(Equal(metav1.ConditionUnknown))
					})

					When("the updated condition is set to False", func() {
						BeforeEach(func() {
							conditionHelper.SetCondition(instance, metav1.Condition{
								Type:    conditions.OpenSearchUpdated,
								Status:  metav1.ConditionFalse,
								Reason:  conditions.InProgress,
								Message: "Updating 'elastic-cluster-manager' pods to latest revision.",
							})
						})

						When("the OpenSearch status is yellow due to initializing shards (requeue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:             clientModels.HealthStatusYellow,
									InitializingShards: 2,
								}, nil)
							})

							It("should return without error, but requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
							})
						})

						When("the OpenSearch status is yellow due to relocating shards (requeue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:           clientModels.HealthStatusYellow,
									RelocatingShards: 2,
								}, nil)
							})

							It("should return without error, but requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
							})
						})

						When("the OpenSearch status is yellow due to delayed unassigned shards (requeue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:                  clientModels.HealthStatusYellow,
									DelayedUnassignedShards: 2,
								}, nil)
							})

							It("should return without error, but requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
							})
						})

						When("the OpenSearch status is yellow due to unassigned shards (continue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:           clientModels.HealthStatusYellow,
									UnassignedShards: 2,
								}, nil)
							})

							It("should return without error or requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
							})

							When("no pods have been restarted yet", func() {
								It("should restart (delete) the first pod", func() {
									_, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, &corev1.Pod{})).To(HaveOccurred())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())

									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-3", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
								})
							})
						})

						When("the OpenSearch status is red (continue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:           clientModels.HealthStatusYellow,
									UnassignedShards: 4,
								}, nil)
							})

							It("should return without error or requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
							})

							When("no pods have been restarted yet", func() {
								It("should restart (delete) the first pod", func() {
									_, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, &corev1.Pod{})).To(HaveOccurred())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())

									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-3", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
								})
							})
						})

						When("the OpenSearch status is green (continue)", func() {
							It("should return without error or requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
							})

							When("no pods have been restarted yet", func() {
								It("should restart (delete) the first cluster_manager pod", func() {
									_, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())

									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, &corev1.Pod{})).To(HaveOccurred())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())

									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-3", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
								})
							})

							When("the first pod has been restarted", func() {
								BeforeEach(func() {
									pod := &corev1.Pod{}
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, pod)).To(Succeed())
									pod.Labels["controller-revision-hash"] = "2" // Update revision of pod to match StatefulSet update revision
									Expect(k8sClient.Update(ctx, pod)).To(Succeed())
								})

								It("should restart (delete) the second cluster_manager pod", func() {
									_, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())

									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-1", Namespace: namespace}, &corev1.Pod{})).To(HaveOccurred())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())

									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-3", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
								})
							})
						})

						When("the OpenSearch status cannot be retrieved (requeue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{}, errors.New("error"))
							})

							It("should return without error, but requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
							})
						})
					})
				})

				When("the image version is updated (upgrade procedure)", func() {
					BeforeEach(func() {
						instance.Status.OpenSearchClusterTargetVersion = "2.0.0"
						instance.Status.OpenSearchClusterSupportVersion = "1.0.0"

						stsCM.Spec.Template.Spec.Containers[0].Image = "opensearch:2.0.0"
						Expect(k8sClient.Update(ctx, stsCM)).To(Succeed())

						stsData.Spec.Template.Spec.Containers[0].Image = "opensearch:2.0.0"
						Expect(k8sClient.Update(ctx, stsData)).To(Succeed())
					})

					It("sets the upgraded condition to False", func() {
						_, err := reconciler.Reconcile(ctx, instance)
						Expect(err).ToNot(HaveOccurred())
						Expect(conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpgraded)).To(Equal(metav1.ConditionFalse))
					})

					It("should not update the support version yet", func() {
						_, err := reconciler.Reconcile(ctx, instance)
						Expect(err).ToNot(HaveOccurred())
						Expect(instance.Status.OpenSearchClusterSupportVersion).To(Equal("1.0.0"))
					})

					When("the updated condition is set to False", func() {
						BeforeEach(func() {
							conditionHelper.SetCondition(instance, metav1.Condition{
								Type:    conditions.OpenSearchUpdated,
								Status:  metav1.ConditionFalse,
								Reason:  conditions.InProgress,
								Message: "Updating 'elastic-data' pods to latest revision.",
							})
						})

						When("the OpenSearch status is yellow due to initializing shards (requeue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:             clientModels.HealthStatusYellow,
									InitializingShards: 2,
								}, nil)
							})

							It("Sets shard allocation to 'all' to allow recover", func() {
								_, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(openSearchServiceMock.IsSetShardAllocationCalled).To(BeTrue())
								Expect(openSearchServiceMock.SetShardAllocationSetting).To(Equal(clientModels.ShardAllocationAll))
							})

							It("should return without error, but requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
							})
						})

						When("the OpenSearch status is yellow due to relocating shards (requeue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:           clientModels.HealthStatusYellow,
									RelocatingShards: 2,
								}, nil)
							})

							It("Sets shard allocation to 'all' to allow recover", func() {
								_, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(openSearchServiceMock.IsSetShardAllocationCalled).To(BeTrue())
								Expect(openSearchServiceMock.SetShardAllocationSetting).To(Equal(clientModels.ShardAllocationAll))
							})

							It("should return without error, but requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
							})
						})

						When("the OpenSearch status is yellow due to delayed unassigned shards (requeue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:                  clientModels.HealthStatusYellow,
									DelayedUnassignedShards: 2,
								}, nil)
							})

							It("Sets shard allocation to 'all' to allow recover", func() {
								_, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(openSearchServiceMock.IsSetShardAllocationCalled).To(BeTrue())
								Expect(openSearchServiceMock.SetShardAllocationSetting).To(Equal(clientModels.ShardAllocationAll))
							})

							It("should return without error, but requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
							})
						})

						When("the OpenSearch status is yellow due to unassigned shards (conditionally continue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:           clientModels.HealthStatusYellow,
									UnassignedShards: 2,
								}, nil)
							})

							When("node restart is allowed (continue)", func() {
								BeforeEach(func() {
									openSearchServiceMock.PrepareIsNodeRestartAllowed(true, "", nil)
								})

								It("should return without error or requeue", func() {
									result, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())
									Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
								})

								When("no pods have been restarted yet", func() {
									It("should restart (delete) the first data pod", func() {
										_, err := reconciler.Reconcile(ctx, instance)
										Expect(err).ToNot(HaveOccurred())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())

										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, &corev1.Pod{})).To(HaveOccurred())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-3", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									})
								})
							})

							When("node restart is not allowed (requeue)", func() {
								BeforeEach(func() {
									openSearchServiceMock.PrepareIsNodeRestartAllowed(false, "Shard 0 of index 'foo' has no stores", nil)
								})

								It("Sets shard allocation to 'all' to allow recover", func() {
									_, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())
									Expect(openSearchServiceMock.IsSetShardAllocationCalled).To(BeTrue())
									Expect(openSearchServiceMock.SetShardAllocationSetting).To(Equal(clientModels.ShardAllocationAll))
								})

								It("should return without error, but requeue", func() {
									result, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())
									Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
								})
							})

							When("an error occurs while checking if node restart is allowed (requeue)", func() {
								BeforeEach(func() {
									openSearchServiceMock.PrepareIsNodeRestartAllowed(false, "", errors.New("error"))
								})

								It("Sets shard allocation to 'all' to allow recover", func() {
									_, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())
									Expect(openSearchServiceMock.IsSetShardAllocationCalled).To(BeTrue())
									Expect(openSearchServiceMock.SetShardAllocationSetting).To(Equal(clientModels.ShardAllocationAll))
								})

								It("should return without error, but requeue", func() {
									result, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())
									Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
								})
							})
						})

						When("the OpenSearch status is red (requeue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{
									Status:           clientModels.HealthStatusYellow,
									UnassignedShards: 4,
								}, nil)
							})

							It("Sets shard allocation to 'all' to allow recover", func() {
								_, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(openSearchServiceMock.IsSetShardAllocationCalled).To(BeTrue())
								Expect(openSearchServiceMock.SetShardAllocationSetting).To(Equal(clientModels.ShardAllocationAll))
							})

							It("should return without error, but requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
							})
						})

						When("the OpenSearch status is green (continue)", func() {
							It("should return without error or requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
							})

							When("no pods have been restarted yet", func() {
								It("Sets shard allocation to 'primaries' only", func() {
									_, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())
									Expect(openSearchServiceMock.IsSetShardAllocationCalled).To(BeTrue())
									Expect(openSearchServiceMock.SetShardAllocationSetting).To(Equal(clientModels.ShardAllocationPrimaries))
								})

								It("should restart (delete) the first data pod", func() {
									_, err := reconciler.Reconcile(ctx, instance)
									Expect(err).ToNot(HaveOccurred())

									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())

									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, &corev1.Pod{})).To(HaveOccurred())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-3", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
								})
							})

							When("the first pod has been restarted", func() {
								BeforeEach(func() {
									pod := &corev1.Pod{}
									Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, pod)).To(Succeed())
									pod.Labels["controller-revision-hash"] = "2" // Update revision of pod to match StatefulSet update revision
									Expect(k8sClient.Update(ctx, pod)).To(Succeed())

									stsData.Status.UpdatedReplicas = 1
									Expect(k8sClient.Status().Update(ctx, stsData)).To(Succeed())
								})

								When("the restarted node joined the cluster (continue)", func() {
									BeforeEach(func() {
										openSearchServiceMock.PrepareGetNumberOfNodesMatchingVersion(1, nil)
									})

									It("checks the updated node joined the cluster", func() {
										_, err := reconciler.Reconcile(ctx, instance)
										Expect(err).ToNot(HaveOccurred())
										Expect(openSearchServiceMock.IsGetNumberOfNodesMatchingVersionCalled).To(BeTrue())
										Expect(openSearchServiceMock.GetNumberOfNodesMatchingVersionVersion.String()).To(Equal("2.0.0"))
										Expect(openSearchServiceMock.GetNumberOfNodesMatchingVersionFilter).To(Equal("elastic-data-*"))
									})

									It("should restart (delete) the second data pod", func() {
										_, err := reconciler.Reconcile(ctx, instance)
										Expect(err).ToNot(HaveOccurred())

										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-1", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-cluster-manager-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())

										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-0", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-1", Namespace: namespace}, &corev1.Pod{})).To(HaveOccurred())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-2", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
										Expect(k8sClient.Get(ctx, types.NamespacedName{Name: "elastic-data-3", Namespace: namespace}, &corev1.Pod{})).To(Succeed())
									})
								})

								When("the restarted node did not join the cluster yet (requeue)", func() {
									BeforeEach(func() {
										openSearchServiceMock.PrepareGetNumberOfNodesMatchingVersion(0, nil)
									})

									It("should return without error, but requeue", func() {
										result, err := reconciler.Reconcile(ctx, instance)
										Expect(err).ToNot(HaveOccurred())
										Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
									})
								})

								When("an error occurs while checking if the updated node joined the cluster (requeue)", func() {
									BeforeEach(func() {
										openSearchServiceMock.PrepareGetNumberOfNodesMatchingVersion(0, errors.New("error"))
									})

									It("should return without error, but requeue", func() {
										result, err := reconciler.Reconcile(ctx, instance)
										Expect(err).ToNot(HaveOccurred())
										Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
									})
								})
							})
						})

						When("the OpenSearch status cannot be retrieved (requeue)", func() {
							BeforeEach(func() {
								openSearchServiceMock.PrepareGetHealth(clientModels.OSHealthResponse{}, errors.New("error"))
							})

							It("should return without error, but requeue", func() {
								result, err := reconciler.Reconcile(ctx, instance)
								Expect(err).ToNot(HaveOccurred())
								Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 15 * time.Second}))
							})
						})
					})
				})
			})
		})

		When("OpenSearch is not reconciled", func() {
			BeforeEach(func() {
				conditionHelper.SetCondition(instance, metav1.Condition{
					Type:   conditions.OpenSearchReconciled,
					Status: metav1.ConditionFalse,
					Reason: conditions.InProgress,
				})
			})

			When("there are pods to be updated", func() {
				BeforeEach(func() {
					stsData.Status.UpdateRevision = "2"
					stsData.Status.CurrentRevision = "1"
					stsData.Status.UpdatedReplicas = 0
					stsData.Status.AvailableReplicas = 4
					stsData.Status.ReadyReplicas = 4
					stsData.Status.Replicas = 4
					Expect(k8sClient.Status().Update(ctx, stsData)).To(Succeed())
				})

				It("should return without error", func() {
					result, err := reconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
				})

				It("should not set the updated condition to False", func() {
					_, err := reconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(conditionHelper.GetConditionStatus(instance, conditions.OpenSearchUpdated)).NotTo(Equal(metav1.ConditionFalse))
				})
			})
		})
	})

	Context("GetValidConditionNames", func() {
		When("the updated condition is set to True", func() {
			BeforeEach(func() {
				conditionHelper.SetCondition(instance, metav1.Condition{
					Type:   conditions.OpenSearchUpdated,
					Status: metav1.ConditionTrue,
					Reason: conditions.Succeeded,
				})
			})

			It("returns no condition names", func() {
				Expect(reconciler.GetValidConditionNames(instance)).To(BeEmpty())
			})
		})

		When("the updated condition is set to False and the timeout is not breached", func() {
			BeforeEach(func() {
				conditionHelper.SetCondition(instance, metav1.Condition{
					Type:   conditions.OpenSearchUpdated,
					Status: metav1.ConditionFalse,
					Reason: conditions.InProgress,
					LastTransitionTime: metav1.Time{
						Time: time.Now().Add(-50 * time.Minute),
					},
				})
			})

			It("returns no condition names", func() {
				Expect(reconciler.GetValidConditionNames(instance)).To(BeEmpty())
			})
		})

		When("the updated condition is set to False and the timeout is breached", func() {
			BeforeEach(func() {
				conditionHelper.SetCondition(instance, metav1.Condition{
					Type:   conditions.OpenSearchUpdated,
					Status: metav1.ConditionFalse,
					Reason: conditions.InProgress,
					LastTransitionTime: metav1.Time{
						Time: time.Now().Add(-70 * time.Hour),
					},
				})
			})

			It("should return the updated condition name", func() {
				Expect(reconciler.GetValidConditionNames(instance)).To(ConsistOf(conditions.OpenSearchUpdated))
			})
		})
	})

	Context("GetName", func() {
		It("should return the name of the reconciler", func() {
			Expect(reconciler.GetName()).To(Equal("OpenSearchUpdateReconciler"))
		})
	})
})
