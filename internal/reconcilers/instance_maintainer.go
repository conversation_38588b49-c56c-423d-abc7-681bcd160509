package reconcilers

import (
	"context"
	"fmt"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/templating"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	InstanceMaintainerReconcilerName = "InstanceMaintainerReconciler"
)

type InstanceMaintainerReconciler struct {
	client                    client.Client
	conditionHelper           conditions.ConditionHelper
	resourceReconciler        reconciler.ResourceReconciler
	templatedConfig           templating.TemplatedConfig
	instanceMaintainerService services.InstanceMaintainerService
	featureConfig             services.FeatureConfiguration
}

func NewInstanceMaintainerReconciler(client client.Client, conditionHelper conditions.ConditionHelper,
	resourceReconciler reconciler.ResourceReconciler, instanceMaintainerService services.InstanceMaintainerService,
	featureConfig services.FeatureConfiguration, templatePath string) ComponentReconciler {
	return &InstanceMaintainerReconciler{
		client:                    client,
		conditionHelper:           conditionHelper,
		resourceReconciler:        resourceReconciler,
		instanceMaintainerService: instanceMaintainerService,
		featureConfig:             featureConfig,
		templatedConfig:           templating.NewTemplatedConfig(templatePath, "config.yaml"),
	}
}

func (r *InstanceMaintainerReconciler) Reconcile(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	result := reconciler.CombinedResult{}

	// Source object for config templates and builder
	configSource := struct {
		InstanceMaintainer  clv1beta1.InstanceMaintainer
		MinPrimaryShardSize string
		SkipReconciliation  interface{}
	}{
		InstanceMaintainer:  instance.Spec.InstanceMaintainer,
		MinPrimaryShardSize: instance.Spec.OpenSearch.Cluster.AdditionalConfig["rollover_min_primary_shard_size"],
		SkipReconciliation: struct {
			Content             bool
			AccessControl       bool
			ComposableTemplates bool
			ISMPolicies         bool
			SavedObjects        bool
		}{
			Content:             r.featureConfig.IsFeatureSet(instance, config.FeatureFlagSkipReconcileContent),
			AccessControl:       r.featureConfig.IsFeatureSet(instance, config.FeatureFlagSkipReconcileAccessControl),
			ComposableTemplates: r.featureConfig.IsFeatureSet(instance, config.FeatureFlagSkipReconcileComposableTemplate),
			ISMPolicies:         r.featureConfig.IsFeatureSet(instance, config.FeatureFlagSkipReconcileISMPolicy),
			SavedObjects:        r.featureConfig.IsFeatureSet(instance, config.FeatureFlagSkipReconcileSavedObjects),
		},
	}

	// Reconcile instance-maintainer related Kubernetes resources
	serviceAccount := builders.BuildInstanceMaintainerServiceAccount(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, serviceAccount, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(serviceAccount, reconciler.StatePresent))

	role := builders.BuildInstanceMaintainerRole(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, role, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(role, reconciler.StatePresent))

	roleBinding := builders.BuildInstanceMaintainerRoleBinding(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, roleBinding, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(roleBinding, reconciler.StatePresent))

	service := builders.BuildInstanceMaintainerService(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, service, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(service, reconciler.StatePresent))

	// create configmap and populate with instance-specific templated data
	configMap := builders.BuildInstanceMaintainerConfigMap(instance)
	result.CombineErr(r.templatedConfig.PopulateConfigMap(templating.PARSE_AS_FILES, configSource, configMap))
	result.CombineErr(ctrl.SetControllerReference(instance, configMap, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(configMap, reconciler.StatePresent))

	deployment := builders.BuildInstanceMaintainerDeployment(instance)
	result.CombineErr(utils.SetHashFromCM(deployment.Spec.Template.Annotations, configMap))
	result.CombineErr(ctrl.SetControllerReference(instance, deployment, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(deployment, reconciler.StatePresent))

	// Set available condition
	result.CombineErr(r.updateAvailableCondition(ctx, instance, deployment))

	// Set reconcile condition eventually
	r.conditionHelper.SetReconcileCondition(instance, conditions.InstanceMaintainerReconciled, result)
	return result.Result, result.Err
}

func (r *InstanceMaintainerReconciler) CleanUp(context.Context, *clv1beta1.Instance) (ctrl.Result, error) {
	// Nothing to do here, as the instance-maintainer resources are owned by the instance
	return ctrl.Result{}, nil
}

// GetValidConditionNames returns a list of condition names related to the component.
// If the component can be disabled AND is disabled, only the '...Reconciled' condition should be returned.
func (r *InstanceMaintainerReconciler) GetValidConditionNames(*clv1beta1.Instance) []string {
	validConditions := []string{
		conditions.InstanceMaintainerReconciled,
		conditions.InstanceMaintainerAvailable,
		conditions.InstanceMaintainerStatus,
	}
	return validConditions
}

// UpdateStatusConditions updates the components status condition(s). This method is called frequently and
// should be lightweight. It should only update the local instance, which will be persisted by the watcher
// after all component reconcilers have been called.
func (r *InstanceMaintainerReconciler) UpdateStatusConditions(ctx context.Context, instance *clv1beta1.Instance) bool {
	return r.conditionHelper.SetCondition(instance, r.instanceMaintainerService.GetStatusCondition(ctx, instance))
}

// GetName returns the name of the reconciler for monitoring and logging purposes.
func (r *InstanceMaintainerReconciler) GetName() string {
	return InstanceMaintainerReconcilerName
}

//nolint:dupl // This availability condition differs from component to component
func (r *InstanceMaintainerReconciler) updateAvailableCondition(ctx context.Context, instance *clv1beta1.Instance, deployment *appsv1.Deployment) error {
	// Determine ready status based on replica availability
	err := r.client.Get(ctx, client.ObjectKeyFromObject(deployment), deployment)
	if err != nil {
		if errors.IsNotFound(err) {
			r.conditionHelper.SetCondition(instance, metav1.Condition{
				Type:    conditions.InstanceMaintainerAvailable,
				Status:  metav1.ConditionFalse,
				Reason:  conditions.NotFound,
				Message: "Deployment does not exist (yet)",
			})

			// Do not return an error in this case as rescheduling would not help
			// Immediately after the deployment is created we cannot expect it to exist already (async processing)
			return nil
		}
		return err
	}

	if deployment.Status.AvailableReplicas >= 1 {
		r.conditionHelper.SetCondition(instance, metav1.Condition{
			Type:    conditions.InstanceMaintainerAvailable,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.Available,
			Message: fmt.Sprintf("%d of %d replica(s) are available", deployment.Status.AvailableReplicas, deployment.Status.Replicas),
		})
		return nil
	}
	r.conditionHelper.SetCondition(instance, metav1.Condition{
		Type:    conditions.InstanceMaintainerAvailable,
		Status:  metav1.ConditionFalse,
		Reason:  conditions.NotAvailable,
		Message: "No replicas are available",
	})
	return nil
}
