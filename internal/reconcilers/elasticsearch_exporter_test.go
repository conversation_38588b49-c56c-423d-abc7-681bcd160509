package reconcilers_test

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("ElasticsearchExporterReconciler", func() {
	const (
		instanceName                      = "cls-test"
		namespace                         = "sf-test"
		elasticsearchExporterTemplatePath = "../../templates/elasticsearch_exporter"
	)

	var (
		instance                        *clv1beta1.Instance
		elasticsearchExporterReconciler reconcilers.ComponentReconciler
		passwordStoreServiceMock        *fake.PasswordStoreServiceMock
		resourceReconciler              reconciler.ResourceReconciler
		conditionHelper                 = conditions.NewConditionHelper()
		ctx                             = context.Background()
		instanceNamespacedName          = types.NamespacedName{
			Name:      instanceName,
			Namespace: namespace,
		}
	)

	BeforeEach(func() {
		resourceReconciler = reconciler.NewReconcilerWith(k8sClient)
		passwordStoreServiceMock = fake.NewPasswordStoreServiceMock()

		Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      instanceName,
				Namespace: namespace,
			},
			Spec: clv1beta1.InstanceSpec{
				InstanceMaintainer: clv1beta1.InstanceMaintainer{
					Resources: corev1.ResourceRequirements{},
				},
				OpenSearch: clv1beta1.OpenSearch{
					Cluster: clv1beta1.Cluster{
						NodePools: []clv1beta1.NodePool{},
					},
				},
			},
		}

		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace,
			},
		}

		err := k8sClient.Get(ctx, types.NamespacedName{Name: namespace}, ns)
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the namespace")
			Expect(k8sClient.Create(ctx, ns)).To(Succeed())
		}

		err = k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{})
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the CustomResource for the CLOE instance")
			Expect(k8sClient.Create(ctx, instance)).To(Succeed())
		}

		passwordStoreServiceMock.PrepareOpenSearchCredentials(models.Credentials{
			Username: "esexporter",
			Password: "password",
		})

		elasticsearchExporterReconciler = reconcilers.NewElasticsearchExporterReconciler(
			k8sClient, conditionHelper, resourceReconciler, passwordStoreServiceMock,
			elasticsearchExporterTemplatePath)

	})

	AfterEach(func() {
		err := k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{})
		Expect(err).NotTo(HaveOccurred())

		By("deleting the CustomResource for the CLOE instance")
		Expect(k8sClient.Delete(ctx, instance)).To(Succeed())
	})

	Context("Reconcile()", func() {
		When("Reconcile() is called", func() {
			BeforeEach(func() {
				result, err := elasticsearchExporterReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
			})

			It("should reconcile all elasticsearch-exporter resources", func() {
				deployment := &appsv1.Deployment{}
				err := k8sClient.Get(ctx, types.NamespacedName{Name: "elasticsearch-exporter-deployment", Namespace: namespace}, deployment)
				Expect(err).NotTo(HaveOccurred())
				Expect(deployment.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(deployment.OwnerReferences[0].Kind).To(Equal("Instance"))

				secret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "elasticsearch-exporter-secret", Namespace: namespace}, secret)
				Expect(err).NotTo(HaveOccurred())
				Expect(secret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(string(secret.Data["credentials.txt"])).To(Equal("esexporter:password"))

				service := &corev1.Service{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "elasticsearch-exporter-service", Namespace: namespace}, service)
				Expect(err).NotTo(HaveOccurred())
				Expect(service.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(service.Spec.Ports[0].Name).To(Equal("http"))
				Expect(service.Spec.Ports[0].Port).To(Equal(int32(9108)))

				networkPolicy := &networkingv1.NetworkPolicy{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "network-allow-monitoring", Namespace: namespace}, networkPolicy)
				Expect(err).NotTo(HaveOccurred())
				Expect(networkPolicy.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(networkPolicy.Spec.PolicyTypes[0]).To(Equal(networkingv1.PolicyTypeIngress))
				Expect(networkPolicy.Spec.Ingress[0].From[0].PodSelector.MatchLabels["role"]).To(Equal("prometheus"))
				Expect(networkPolicy.Spec.Ingress[0].From[1].NamespaceSelector.MatchLabels["role"]).To(Equal("monitor-operator"))
			})

			It("should set the reconciled condition", func() {
				condition := conditionHelper.GetCondition(instance, conditions.ElasticsearchExporterReconciled)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Succeeded))
				Expect(condition.Message).To(Equal("Successfully reconciled resources"))
			})
		})

		When("elasticsearch-exporter deployment has less than 1 available replicas", func() {
			BeforeEach(func() {
				deployment := builders.BuildElasticsearchExporterDeployment(instance)
				deployment.Status = appsv1.DeploymentStatus{
					ReadyReplicas:     0,
					Replicas:          0,
					AvailableReplicas: 0,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to not available (false) state", func() {
				result, err := elasticsearchExporterReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				condition := conditionHelper.GetCondition(instance, conditions.ElasticsearchExporterAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionFalse))
				Expect(condition.Reason).To(Equal(conditions.NotAvailable))
				Expect(condition.Message).To(Equal("No replicas are available"))
			})
		})

		When("elasticsearch-exporter deployment has 1 or more available replicas", func() {
			BeforeEach(func() {
				deployment := builders.BuildElasticsearchExporterDeployment(instance)
				deployment.Status = appsv1.DeploymentStatus{
					ReadyReplicas:     1,
					Replicas:          1,
					AvailableReplicas: 1,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to available (true) state", func() {
				result, err := elasticsearchExporterReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				condition := conditionHelper.GetCondition(instance, conditions.ElasticsearchExporterAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Available))
				Expect(condition.Message).To(Equal("1 of 1 replica(s) are available"))
			})
		})
	})

	Context("CleanUp()", func() {
		It("should return no error and schedule no requeue", func() {
			result, err := elasticsearchExporterReconciler.CleanUp(ctx, instance)
			Expect(err).NotTo(HaveOccurred())
			Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
		})
	})

	Context("GetValidConditionNames()", func() {
		It("should return a list of valid condition names", func() {
			Expect(elasticsearchExporterReconciler.GetValidConditionNames(instance)).To(BeEmpty())
		})
	})

	Context("UpdateStatusConditions()", func() {
		It("returns false", func() {
			Expect(elasticsearchExporterReconciler.UpdateStatusConditions(ctx, instance)).To(BeFalse())
		})
	})

	Context("GetName()", func() {
		It("should return the name of the reconciler", func() {
			Expect(elasticsearchExporterReconciler.GetName()).To(Equal("ElasticsearchExporterReconciler"))
		})
	})

})
