package helpers_test

import (
	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers/helpers"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test Image Helper", func() {
	var (
		imageHelper              helpers.ImageHelper
		featureConfigurationMock *fake.FeatureConfigurationMock
		instance                 *clv1beta1.Instance
	)

	BeforeEach(func() {
		featureConfigurationMock = fake.NewFeatureConfigurationMock()
		imageHelper = helpers.NewImageHelper(featureConfigurationMock)

		featureConfigurationMock.PrepareSupportedFeatureFlags([]string{
			config.FeatureFlagUpgradeToOpenSearchV2,
			config.FeatureFlagUseOpenSearchDevelopmentVersion,
		})

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test_instance",
				Namespace: "default",
			},
			Spec: clv1beta1.InstanceSpec{},
		}
	})

	Context("GetAvailableImages", func() {
		When("non of the related feature-flags are allowed (production cluster)", func() {
			It("should only return the stable image", func() {
				images := imageHelper.GetAvailableImages(instance, config.ImageChannels{
					Stable:      "opensearch:1.0.0",
					Preview:     "opensearch:2.0.0",
					Development: "opensearch:2.1.0",
				})

				Expect(images).To(HaveLen(1))
				Expect(images[config.StableChannel]).To(Equal("opensearch:1.0.0"))
			})
		})

		When("the 'upgradeToOpenSearchV2' feature flag is allowed (production cluster)", func() {
			BeforeEach(func() {
				featureConfigurationMock.PrepareAllowedFeatureFlags([]string{
					config.FeatureFlagUpgradeToOpenSearchV2,
				})
			})

			It("should only return the stable and preview images", func() {
				images := imageHelper.GetAvailableImages(instance, config.ImageChannels{
					Stable:      "opensearch:1.0.0",
					Preview:     "opensearch:2.0.0",
					Development: "opensearch:2.1.0",
				})

				Expect(images).To(HaveLen(2))
				Expect(images[config.StableChannel]).To(Equal("opensearch:1.0.0"))
				Expect(images[config.PreviewChannel]).To(Equal("opensearch:2.0.0"))
			})

			When("the preview image is not configured", func() {
				It("should return the stable only", func() {
					images := imageHelper.GetAvailableImages(instance, config.ImageChannels{
						Stable:      "opensearch:1.0.0",
						Development: "opensearch:2.1.0",
					})

					Expect(images).To(HaveLen(1))
					Expect(images[config.StableChannel]).To(Equal("opensearch:1.0.0"))
				})
			})
		})

		When("all related feature-flags are allowed (development cluster)", func() {
			BeforeEach(func() {
				featureConfigurationMock.PrepareAllowedFeatureFlags([]string{
					config.FeatureFlagUpgradeToOpenSearchV2,
					config.FeatureFlagUseOpenSearchDevelopmentVersion,
				})
			})

			It("should return all images", func() {
				images := imageHelper.GetAvailableImages(instance, config.ImageChannels{
					Stable:      "opensearch:1.0.0",
					Preview:     "opensearch:2.0.0",
					Development: "opensearch:2.1.0",
				})

				Expect(images).To(HaveLen(3))
				Expect(images[config.StableChannel]).To(Equal("opensearch:1.0.0"))
				Expect(images[config.PreviewChannel]).To(Equal("opensearch:2.0.0"))
				Expect(images[config.DevelopmentChannel]).To(Equal("opensearch:2.1.0"))
			})

			When("the development image is not configured", func() {
				It("should return the stable and preview images only", func() {
					images := imageHelper.GetAvailableImages(instance, config.ImageChannels{
						Stable:  "opensearch:1.0.0",
						Preview: "opensearch:2.0.0",
					})

					Expect(images).To(HaveLen(2))
					Expect(images[config.StableChannel]).To(Equal("opensearch:1.0.0"))
					Expect(images[config.PreviewChannel]).To(Equal("opensearch:2.0.0"))
				})
			})

			When("the preview image is not configured", func() {
				It("should return the stable and development images only", func() {
					images := imageHelper.GetAvailableImages(instance, config.ImageChannels{
						Stable:      "opensearch:1.0.0",
						Development: "opensearch:2.1.0",
					})

					Expect(images).To(HaveLen(2))
					Expect(images[config.StableChannel]).To(Equal("opensearch:1.0.0"))
					Expect(images[config.DevelopmentChannel]).To(Equal("opensearch:2.1.0"))
				})
			})
		})
	})
})
