package helpers

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
)

// TopologyHelper is an interface for the topology helper.
// It uses the current topology and an instance CR to calculate the desired topology for OpenSearch
// and the patch to achieve it.
type TopologyHelper interface {
	// Process calculates the patch to achieve the desired topology based on the current topology and the desired node pools.
	Process(ctx context.Context, instance *clv1beta1.Instance, currentTopology *models.Topology, availableImageChannels map[string]string, desiredImageChannel string) (models.TopologyPatch, error)
}

type ImageHelper interface {
	// GetAvailableImages filters the given image channels based on the allowed feature flags.
	// It returns a map of image channels to their corresponding image names.
	GetAvailableImages(instance *clv1beta1.Instance, imageChannels config.ImageChannels) map[string]string
}
