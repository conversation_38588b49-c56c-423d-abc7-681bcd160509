package helpers

import (
	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
)

type imageHelper struct {
	featureConfiguration services.FeatureConfiguration
}

func NewImageHelper(featureConfiguration services.FeatureConfiguration) ImageHelper {
	return &imageHelper{
		featureConfiguration: featureConfiguration,
	}
}

// GetAvailableImages filters the given image channels based on the allowed feature flags.
// It returns a map of image channels to their corresponding image names.
func (h *imageHelper) GetAvailableImages(instance *clv1beta1.Instance, imagesChannels config.ImageChannels) map[string]string {
	availableImages := map[string]string{
		config.StableChannel: imagesChannels.Stable,
	}

	// only add preview image if the production use of it is allowed
	allowed, _ := h.featureConfiguration.IsFeatureAllowed(instance, config.FeatureFlagUpgradeToOpenSearchV2)
	if allowed && imagesChannels.Preview != "" {
		availableImages[config.PreviewChannel] = imagesChannels.Preview
	}

	// only add development image if the shoot name can be associated to development purpose
	allowed, _ = h.featureConfiguration.IsFeatureAllowed(instance, config.FeatureFlagUseOpenSearchDevelopmentVersion)
	if allowed && imagesChannels.Development != "" {
		availableImages[config.DevelopmentChannel] = imagesChannels.Development
	}

	return availableImages
}
