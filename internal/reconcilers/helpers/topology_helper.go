package helpers

import (
	"context"
	"fmt"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/utils/ptr"
)

const securityInitNodeEnvVarName = "SECURITY_INIT_NODE_NAME"

type topologyHelper struct {
	openSearchTopologyService services.OpenSearchTopologyService
}

func NewTopologyHelper(openSearchTopologyService services.OpenSearchTopologyService) TopologyHelper {
	return &topologyHelper{
		openSearchTopologyService: openSearchTopologyService,
	}
}

// Process calculates the desired topology based on the given instance and the current topology.
func (t *topologyHelper) Process(ctx context.Context, instance *clv1beta1.Instance, currentTopology *models.Topology,
	availableImageChannels map[string]string, desiredImageChannel string) (models.TopologyPatch, error) {
	// determine target image (version)
	targetImage, err := utils.GetTargetImage(availableImageChannels, currentTopology.GetOpenSearchImages(), desiredImageChannel)
	if err != nil {
		return models.TopologyPatch{}, fmt.Errorf("failed to determine the OS target image: %w", err)
	}

	// Calculate the desired topology
	desiredTopology, err := t.calculateDesiredTopology(ctx, currentTopology, instance, targetImage)
	if err != nil {
		return models.TopologyPatch{}, fmt.Errorf("failed to calculate the desired topology: %w", err)
	}

	// Set node for OpenSearch security initialization
	t.setSecurityInitNodeEnvVar(&desiredTopology)

	// Calculate the patch to achieve the desired topology
	patch := t.calculatePatch(currentTopology, &desiredTopology)
	patch.TargetImage = targetImage

	return patch, nil
}

func (t *topologyHelper) calculateDesiredTopology(ctx context.Context, currentTopology *models.Topology, instance *clv1beta1.Instance, targetImage utils.VersionTuple) (models.Topology, error) {
	topology := models.Topology{}

	for _, nodePool := range instance.Spec.OpenSearch.Cluster.NodePools {
		var err error
		var replicas int32
		existingNodeGroup := currentTopology.FindNodeGroupByStsName(fmt.Sprintf("elastic-%s", nodePool.Name))

		// Build the desired resources and compose a NodeGroup
		nodeGroup := models.NodeGroup{
			NodePool:            &nodePool,
			StatefulSet:         builders.BuildOpenSearchStatefulSet(instance, &nodePool, targetImage.Image),
			HeadlessService:     builders.BuildOpenSearchHeadlessService(instance, &nodePool),
			PodDisruptionBudget: builders.BuildOpenSearchPodDisruptionBudget(instance, &nodePool),
		}

		// Retain the existing replicas count if the NodeGroup already exists in the current topology.
		// Otherwise, check the number of PVCs to determine the replicas count.
		// This is essential to restore the correct number of replicas after a StatefulSet deletion
		// (e.g., during CLOE OS migration or accidental deletion).
		if existingNodeGroup != nil && existingNodeGroup.StatefulSet != nil {
			replicas = ptr.Deref(existingNodeGroup.StatefulSet.Spec.Replicas, replicas)
		} else {
			if len(nodeGroup.StatefulSet.Spec.VolumeClaimTemplates) >= 1 {
				// Get the number of PVCs to determine the replicas count.
				pvcTemplateName := nodeGroup.StatefulSet.Spec.VolumeClaimTemplates[0].Name
				replicas, err = t.openSearchTopologyService.GetNumberOfPVCs(ctx, instance, fmt.Sprintf("%s-%s",
					pvcTemplateName, nodeGroup.StatefulSet.Name))
				if err != nil {
					return models.Topology{}, fmt.Errorf("failed to get number of PVCs: %w", err)
				}
			} else {
				// If there are no PVCs, set the replicas count to the min replicas.
				replicas = nodePool.Autoscaler.MinReplicas
			}
		}

		// Constrain the replicas count within the min and max range.
		// We do NOT use customer given maxDataNodeCount here. This needs to be handled by the autoscaler.
		replicas = utils.Constrain(replicas, nodePool.Autoscaler.MinReplicas, nodePool.Autoscaler.MaxReplicas)

		// Set the replicas count for the StatefulSet.
		nodeGroup.StatefulSet.Spec.Replicas = ptr.To(replicas)

		topology.NodeGroups = append(topology.NodeGroups, nodeGroup)
	}

	// Sort the topology for normal reconciliation for now (no OpenSearch upgrade).
	topology.Sort(models.SortOrderNormal)

	return topology, nil
}

// calculatePatch composes a patch object, which can be used by the reconciler to remove any
// obsolete resource and create/update all desired resources.
func (t *topologyHelper) calculatePatch(currentTopology *models.Topology, desiredTopology *models.Topology) models.TopologyPatch {
	absentNodeGroups := []models.NodeGroup{}
	for _, currentNodeGroup := range currentTopology.NodeGroups {
		if desiredTopology.FindNodeGroupByStsName(currentNodeGroup.StatefulSet.Name) == nil {
			absentNodeGroups = append(absentNodeGroups, currentNodeGroup)
		}
	}

	return models.TopologyPatch{
		Topology:         desiredTopology,
		AbsentNodeGroups: absentNodeGroups,
	}
}

// setSecurityInitNodeEnvVar determines the node that will run the security initialization script.
// It adds the node name to the environment variable of the OpenSearch container in the first StatefulSet
// with the ClusterManager role.
// The init.sh script uses this environment variable to determine whether to run securityadmin.sh.
func (t *topologyHelper) setSecurityInitNodeEnvVar(topology *models.Topology) {
	for _, nodeGroup := range topology.NodeGroups {
		if utils.Contains(nodeGroup.NodePool.Roles, clv1beta1.ClusterManager) {
			// The code below works under the assumption, that we only have one cluster manager node pool without autoscaling,
			// which is not ideal. We should use the first pod of the first statefulset with the cluster manager role eventually:
			// TODO: AFTER OpenSearch migration to CLOE use pod 0 for security init instead. The new init.sh script
			// supports this change.
			utils.SetContainerEnvVar(&nodeGroup.StatefulSet.Spec.Template.Spec.Containers[0], corev1.EnvVar{
				Name:  securityInitNodeEnvVarName,
				Value: fmt.Sprintf("%s-%d", nodeGroup.StatefulSet.Name, nodeGroup.NodePool.Autoscaler.MaxReplicas-1),
			})

			return
		}
	}
}
