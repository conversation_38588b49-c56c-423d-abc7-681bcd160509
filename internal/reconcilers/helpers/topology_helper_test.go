package helpers_test

import (
	"context"
	"errors"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers/helpers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	policyv1 "k8s.io/api/policy/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test TopologyHelper", func() {
	const (
		instanceName     = "cls-test"
		namespace        = "sf-test"
		nodePoolNameData = "data"
	)

	var (
		topologyHelper                helpers.TopologyHelper
		openSearchTopologyServiceMock *fake.OpenSearchTopologyServiceMock
		currentTopology               *models.Topology
		instance                      *clv1beta1.Instance
		sts1, sts2                    *appsv1.StatefulSet
		hs1, hs2                      *corev1.Service
		pdb1, pdb2                    *policyv1.PodDisruptionBudget
		availableImageChannels        map[string]string
		ctx                           = context.Background()
	)

	BeforeEach(func() {
		openSearchTopologyServiceMock = fake.NewOpenSearchTopologyServiceMock()
		topologyHelper = helpers.NewTopologyHelper(openSearchTopologyServiceMock)

		Expect(config.GetInstance().Read("../../config/test_config/config.yaml", false)).To(Succeed())

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:        instanceName,
				Namespace:   namespace,
				Annotations: map[string]string{},
			},
			Spec: clv1beta1.InstanceSpec{
				OpenSearch: clv1beta1.OpenSearch{
					Cluster: clv1beta1.Cluster{
						Name: "cls-test",
						NodePools: []clv1beta1.NodePool{
							{
								Name: "group-1",
								Autoscaler: clv1beta1.DiskUsageBasedAutoScaler{
									Autoscaler: clv1beta1.Autoscaler{
										MinReplicas: 3,
										MaxReplicas: 3,
									},
								},
								Resources: corev1.ResourceRequirements{},
								Roles:     []clv1beta1.NodeRole{clv1beta1.ClusterManager},
								Jvm:       "jvm-arguments",
								Persistence: clv1beta1.Persistence{
									DiskSize: resource.MustParse("1Gi"),
								},
							},
							{
								Name: "group-2",
								Autoscaler: clv1beta1.DiskUsageBasedAutoScaler{
									Autoscaler: clv1beta1.Autoscaler{
										MinReplicas: 2,
										MaxReplicas: 10,
									},
									DiskUsageThreshold: 60,
								},
								Resources: corev1.ResourceRequirements{},
								Roles:     []clv1beta1.NodeRole{clv1beta1.Data},
								Jvm:       "jvm-arguments",
								Persistence: clv1beta1.Persistence{
									DiskSize: resource.MustParse("100Gi"),
								},
							},
						},
						ExposeHttpEndpoint: true,
						MaxDataNodes:       4,
						AdditionalConfig: map[string]string{
							"rollover_min_primary_shard_size": "20Gi",
						},
					},
				},
			},
		}

		sts1 = builders.BuildOpenSearchStatefulSet(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0], "opensearch:1.0.0")
		sts2 = builders.BuildOpenSearchStatefulSet(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1], "opensearch:1.0.0")
		hs1 = builders.BuildOpenSearchHeadlessService(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0])
		hs2 = builders.BuildOpenSearchHeadlessService(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1])
		pdb1 = builders.BuildOpenSearchPodDisruptionBudget(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0])
		pdb2 = builders.BuildOpenSearchPodDisruptionBudget(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1])

		sts1.Spec.Replicas = ptr.To(int32(3))
		sts2.Spec.Replicas = ptr.To(int32(2))

		availableImageChannels = map[string]string{
			config.StableChannel:      "opensearch:1.0.0",
			config.PreviewChannel:     "opensearch:2.0.0",
			config.DevelopmentChannel: "opensearch:2.1.0",
		}
	})

	Context("Process", func() {
		When("the existing topology is unchanged", func() {
			BeforeEach(func() {
				// Set replicas to properly test replicas retainment
				sts2.Spec.Replicas = ptr.To(int32(4))

				openSearchTopologyServiceMock.PrepareDiscover(&models.Topology{
					NodeGroups: []models.NodeGroup{
						{
							StatefulSet:         sts1,
							HeadlessService:     hs1,
							PodDisruptionBudget: pdb1,
						},
						{
							StatefulSet:         sts2,
							HeadlessService:     hs2,
							PodDisruptionBudget: pdb2,
						},
					},
				}, nil)
				currentTopology = &models.Topology{
					NodeGroups: []models.NodeGroup{
						{
							StatefulSet:         sts1,
							HeadlessService:     hs1,
							PodDisruptionBudget: pdb1,
						},
						{
							StatefulSet:         sts2,
							HeadlessService:     hs2,
							PodDisruptionBudget: pdb2,
						},
					},
				}
			})

			It("should return the correct topology patch", func() {
				patch, err := topologyHelper.Process(ctx, instance, currentTopology, availableImageChannels, "stable")
				Expect(err).ToNot(HaveOccurred())

				Expect(patch).ToNot(BeNil())
				Expect(patch.AbsentNodeGroups).To(HaveLen(0))
				Expect(patch.Topology.NodeGroups).To(HaveLen(2))

				Expect(patch.Topology.NodeGroups[0].StatefulSet).NotTo(BeNil())
				Expect(patch.Topology.NodeGroups[0].StatefulSet.Name).To(Equal("elastic-group-1"))
				Expect(patch.Topology.NodeGroups[1].StatefulSet).NotTo(BeNil())
				Expect(patch.Topology.NodeGroups[1].StatefulSet.Name).To(Equal("elastic-group-2"))
				Expect(*patch.Topology.NodeGroups[1].StatefulSet.Spec.Replicas).To(Equal(int32(4)))

				Expect(patch.Topology.NodeGroups[0].HeadlessService).NotTo(BeNil())
				Expect(patch.Topology.NodeGroups[0].HeadlessService.Name).To(Equal("opensearch-group-1-headless"))
				Expect(patch.Topology.NodeGroups[1].HeadlessService).NotTo(BeNil())
				Expect(patch.Topology.NodeGroups[1].HeadlessService.Name).To(Equal("opensearch-group-2-headless"))

				Expect(patch.Topology.NodeGroups[0].PodDisruptionBudget).NotTo(BeNil())
				Expect(patch.Topology.NodeGroups[0].PodDisruptionBudget.Name).To(Equal("opensearch-group-1-pdb"))
				Expect(patch.Topology.NodeGroups[1].PodDisruptionBudget).NotTo(BeNil())
				Expect(patch.Topology.NodeGroups[1].PodDisruptionBudget.Name).To(Equal("opensearch-group-2-pdb"))

				Expect(patch.AbsentNodeGroups).To(HaveLen(0))

				Expect(patch.TargetImage.Image).To(Equal("opensearch:1.0.0"))
				Expect(patch.TargetImage.Version.Original()).To(Equal("1.0.0"))
			})
		})

		When("the existing topology contains an obsolete node group", func() {
			BeforeEach(func() {
				currentTopology = &models.Topology{
					NodeGroups: []models.NodeGroup{
						{
							StatefulSet:         sts1,
							HeadlessService:     hs1,
							PodDisruptionBudget: pdb1,
						},
						{
							StatefulSet:         sts2,
							HeadlessService:     hs2,
							PodDisruptionBudget: pdb2,
						},
					},
				}

				// Only keep the first node pool
				instance.Spec.OpenSearch.Cluster.NodePools = instance.Spec.OpenSearch.Cluster.NodePools[:1]
			})

			It("should return the desired topology and absent node groups", func() {
				patch, err := topologyHelper.Process(ctx, instance, currentTopology, availableImageChannels, "stable")
				Expect(err).ToNot(HaveOccurred())

				Expect(patch).ToNot(BeNil())
				Expect(patch.AbsentNodeGroups).To(HaveLen(1))
				Expect(patch.AbsentNodeGroups[0].StatefulSet).NotTo(BeNil())
				Expect(patch.AbsentNodeGroups[0].StatefulSet.Name).To(Equal("elastic-group-2"))
			})
		})

		When("the existing topology is empty", func() {
			BeforeEach(func() {
				currentTopology = &models.Topology{}
			})

			It("should check for existing PVCs", func() {
				_, err := topologyHelper.Process(ctx, instance, currentTopology, availableImageChannels, "stable")
				Expect(err).ToNot(HaveOccurred())

				Expect(openSearchTopologyServiceMock.IsGetNumberOfPVCsCalled).To(BeTrue())
				Expect(openSearchTopologyServiceMock.PVCPrefix).To(Equal("data-elastic-group-2"))
			})

			When("there are no existing PVCs (newly created instance)", func() {
				BeforeEach(func() {
					openSearchTopologyServiceMock.PrepareGetNumberOfPVCs(0, nil)
					currentTopology = &models.Topology{}
				})

				It("should return the desired topology", func() {
					patch, err := topologyHelper.Process(ctx, instance, currentTopology, availableImageChannels, "stable")
					Expect(err).ToNot(HaveOccurred())

					Expect(patch).ToNot(BeNil())
					Expect(patch.AbsentNodeGroups).To(HaveLen(0))
					Expect(patch.Topology.NodeGroups).To(HaveLen(2))

					Expect(patch.Topology.NodeGroups[0].StatefulSet).NotTo(BeNil())
					Expect(patch.Topology.NodeGroups[0].StatefulSet.Name).To(Equal("elastic-group-1"))

					securityInitNode, ok := utils.GetContainerEnvVar(&patch.Topology.NodeGroups[0].StatefulSet.Spec.Template.Spec.Containers[0], "SECURITY_INIT_NODE_NAME")
					Expect(ok).To(BeTrue())
					Expect(securityInitNode.Value).To(Equal("elastic-group-1-2"))

					Expect(patch.Topology.NodeGroups[1].StatefulSet).NotTo(BeNil())
					Expect(patch.Topology.NodeGroups[1].StatefulSet.Name).To(Equal("elastic-group-2"))
					Expect(*patch.Topology.NodeGroups[1].StatefulSet.Spec.Replicas).To(Equal(int32(2))) // <-- minimum from autoscaler config

					Expect(patch.Topology.NodeGroups[0].HeadlessService).NotTo(BeNil())
					Expect(patch.Topology.NodeGroups[0].HeadlessService.Name).To(Equal("opensearch-group-1-headless"))
					Expect(patch.Topology.NodeGroups[1].HeadlessService).NotTo(BeNil())
					Expect(patch.Topology.NodeGroups[1].HeadlessService.Name).To(Equal("opensearch-group-2-headless"))

					Expect(patch.Topology.NodeGroups[0].PodDisruptionBudget).NotTo(BeNil())
					Expect(patch.Topology.NodeGroups[0].PodDisruptionBudget.Name).To(Equal("opensearch-group-1-pdb"))
					Expect(patch.Topology.NodeGroups[1].PodDisruptionBudget).NotTo(BeNil())
					Expect(patch.Topology.NodeGroups[1].PodDisruptionBudget.Name).To(Equal("opensearch-group-2-pdb"))
				})
			})

			When("there are existing PVCs (after StatefulSet deletion)", func() {
				BeforeEach(func() {
					openSearchTopologyServiceMock.PrepareGetNumberOfPVCs(5, nil)
					currentTopology = &models.Topology{}
				})

				It("should return the desired topology", func() {
					patch, err := topologyHelper.Process(ctx, instance, currentTopology, availableImageChannels, "stable")
					Expect(err).ToNot(HaveOccurred())

					Expect(patch).ToNot(BeNil())
					Expect(patch.AbsentNodeGroups).To(HaveLen(0))
					Expect(patch.Topology.NodeGroups).To(HaveLen(2))

					Expect(patch.Topology.NodeGroups[1].StatefulSet).NotTo(BeNil())
					Expect(patch.Topology.NodeGroups[1].StatefulSet.Name).To(Equal("elastic-group-2"))
					Expect(*patch.Topology.NodeGroups[1].StatefulSet.Spec.Replicas).To(Equal(int32(5))) // <-- from existing PVCs
				})
			})

			When("the number of existing PVCs cannot be determined due to an error", func() {
				BeforeEach(func() {
					openSearchTopologyServiceMock.PrepareGetNumberOfPVCs(0, errors.New("fake error"))
					currentTopology = &models.Topology{}
				})

				It("should return an error", func() {
					_, err := topologyHelper.Process(ctx, instance, currentTopology, availableImageChannels, "stable")
					Expect(err).To(HaveOccurred())
				})
			})
		})
	})
})
