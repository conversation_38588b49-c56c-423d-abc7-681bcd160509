package reconcilers_test

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	policyv1 "k8s.io/api/policy/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("DataPrepperReconciler", func() {
	const (
		instanceName            = "cls-test"
		namespace               = "sf-test"
		dataPrepperTemplatePath = "../../templates/dataprepper"
	)

	var (
		instance                 *clv1beta1.Instance
		dataPrepperReconciler    reconcilers.ComponentReconciler
		resourceReconciler       reconciler.ResourceReconciler
		conditionHelper          = conditions.NewConditionHelper()
		passwordStoreServiceMock = fake.NewPasswordStoreServiceMock()
		ctx                      = context.Background()
		instanceNamespacedName   = types.NamespacedName{
			Name:      instanceName,
			Namespace: namespace,
		}
	)

	BeforeEach(func() {
		resourceReconciler = reconciler.NewReconcilerWith(k8sClient)

		Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      instanceName,
				Namespace: namespace,
			},
			Spec: clv1beta1.InstanceSpec{
				DataPrepper: clv1beta1.DataPrepper{
					Enabled: true,
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    *resource.NewQuantity(1, resource.DecimalSI),
							corev1.ResourceMemory: *resource.NewQuantity(100, resource.DecimalSI),
						},
					},
					Replicas: 2,
					AdditionalConfig: map[string]string{
						"log_pipeline_batch_size":      "200",
						"log_pipeline_buffer_size":     "12800",
						"log_pipeline_workers":         "4",
						"max_heap_percentage":          "70",
						"metric_pipeline_batch_size":   "200",
						"metric_pipeline_buffer_size":  "12800",
						"metric_pipeline_workers":      "4",
						"raw_span_pipeline_batch_size": "3200",
						"trace_pipeline_batch_size":    "400",
						"trace_pipeline_buffer_size":   "25600",
						"trace_pipeline_workers":       "8",
					},
				},
			},
		}

		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace,
			},
		}

		err := k8sClient.Get(ctx, types.NamespacedName{Name: namespace}, ns)
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the namespace")
			Expect(k8sClient.Create(ctx, ns)).To(Succeed())
		}

		err = k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{})
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the CustomResource for the CLOE instance")
			Expect(k8sClient.Create(ctx, instance)).To(Succeed())
		}

		dataPrepperReconciler = reconcilers.NewDataPrepperReconciler(k8sClient, conditionHelper,
			resourceReconciler, passwordStoreServiceMock, dataPrepperTemplatePath)
	})

	AfterEach(func() {
		Expect(k8sClient.DeleteAllOf(ctx, &clv1beta1.Instance{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &appsv1.Deployment{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &policyv1.PodDisruptionBudget{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Secret{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.ConfigMap{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Service{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &networkingv1.Ingress{}, client.InNamespace(namespace))).To(Succeed())
	})

	Context("Reconcile()", func() {
		When("Reconcile() is called", func() {
			When("Data Prepper is not yet deployed", func() {
				It("should reconcile all Data Prepper resources", func() {
					result, err := dataPrepperReconciler.Reconcile(ctx, instance)
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					dataPrepperDeployment := &appsv1.Deployment{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper-deployment", Namespace: namespace}, dataPrepperDeployment)
					Expect(err).NotTo(HaveOccurred())
					Expect(dataPrepperDeployment.OwnerReferences[0].Kind).To(Equal("Instance"))
					Expect(dataPrepperDeployment.OwnerReferences[0].Name).To(Equal("cls-test"))

					dataPrepperPDB := &policyv1.PodDisruptionBudget{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper-pdb", Namespace: namespace}, dataPrepperPDB)
					Expect(err).NotTo(HaveOccurred())
					Expect(dataPrepperPDB.OwnerReferences[0].Kind).To(Equal("Instance"))
					Expect(dataPrepperPDB.OwnerReferences[0].Name).To(Equal("cls-test"))
					Expect(dataPrepperPDB.Spec.MaxUnavailable.IntVal).To(Equal(int32(1)))

					dataPrepperSecret := &corev1.Secret{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper-secret", Namespace: namespace}, dataPrepperSecret)
					Expect(err).NotTo(HaveOccurred())
					Expect(dataPrepperSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
					Expect(dataPrepperSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
					Expect(dataPrepperSecret.Data["data-prepper-wait-for-os-and-start.sh"]).ToNot(BeEmpty())
					Expect(dataPrepperSecret.Data["trace_analytics_no_ssl.yaml"]).ToNot(BeEmpty())

					dataPrepperConfigMap := &corev1.ConfigMap{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper-config", Namespace: namespace}, dataPrepperConfigMap)
					Expect(err).NotTo(HaveOccurred())
					Expect(dataPrepperConfigMap.OwnerReferences[0].Kind).To(Equal("Instance"))
					Expect(dataPrepperConfigMap.OwnerReferences[0].Name).To(Equal("cls-test"))
					Expect(dataPrepperConfigMap.Data["data-prepper-config.yaml"]).ToNot(BeEmpty())
					Expect(dataPrepperConfigMap.Data["log4j2-rolling.properties"]).ToNot(BeEmpty())
					Expect(dataPrepperConfigMap.Data["probes.sh"]).ToNot(BeEmpty())

					dataPrepperService := &corev1.Service{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper", Namespace: namespace}, dataPrepperService)
					Expect(err).NotTo(HaveOccurred())
					Expect(dataPrepperService.OwnerReferences[0].Kind).To(Equal("Instance"))
					Expect(dataPrepperService.OwnerReferences[0].Name).To(Equal("cls-test"))
					Expect(dataPrepperService.Spec.Ports).To(HaveLen(3))
					Expect(dataPrepperService.Spec.Ports[0].Name).To(Equal("otel-traces"))
					Expect(dataPrepperService.Spec.Ports[0].Port).To(Equal(int32(21890)))
					Expect(dataPrepperService.Spec.Ports[1].Name).To(Equal("otel-metrics"))
					Expect(dataPrepperService.Spec.Ports[1].Port).To(Equal(int32(21891)))
					Expect(dataPrepperService.Spec.Ports[2].Name).To(Equal("otel-logs"))
					Expect(dataPrepperService.Spec.Ports[2].Port).To(Equal(int32(21892)))

					dataPrepperServiceHeadless := &corev1.Service{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper-headless", Namespace: namespace}, dataPrepperServiceHeadless)
					Expect(err).NotTo(HaveOccurred())
					Expect(dataPrepperServiceHeadless.OwnerReferences[0].Kind).To(Equal("Instance"))
					Expect(dataPrepperServiceHeadless.OwnerReferences[0].Name).To(Equal("cls-test"))
					Expect(dataPrepperServiceHeadless.Spec.Ports).To(HaveLen(1))
					Expect(dataPrepperServiceHeadless.Spec.Ports[0].Name).To(Equal("otel"))
					Expect(dataPrepperServiceHeadless.Spec.Ports[0].Port).To(Equal(int32(21890)))

					dataPrepperIngress := &networkingv1.Ingress{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper-ingress", Namespace: namespace}, dataPrepperIngress)
					Expect(err).NotTo(HaveOccurred())
					Expect(dataPrepperIngress.OwnerReferences[0].Kind).To(Equal("Instance"))
					Expect(dataPrepperIngress.OwnerReferences[0].Name).To(Equal("cls-test"))
				})

				It("should set the reconciled condition", func() {
					result, err := dataPrepperReconciler.Reconcile(ctx, instance)
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					condition := conditionHelper.GetCondition(instance, conditions.DataPrepperReconciled)
					Expect(condition.Status).To(Equal(metav1.ConditionTrue))
					Expect(condition.Reason).To(Equal(conditions.Succeeded))
					Expect(condition.Message).To(Equal("Successfully reconciled resources (present)"))
				})
			})

			When("it is called with an instance that is missing Data Prepper configuration", func() {
				BeforeEach(func() {
					instance.Spec.DataPrepper.Resources.Requests = corev1.ResourceList{}
				})

				It("should not requeue and not reconcile resources", func() {
					result, err := dataPrepperReconciler.Reconcile(ctx, instance)
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					dataPrepperDeployment := &appsv1.Deployment{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper-deployment", Namespace: namespace}, dataPrepperDeployment)
					Expect(err).To(HaveOccurred())
				})
			})

			When("it is called with an instance that is missing Data Prepper additional config field", func() {
				BeforeEach(func() {
					instance.Spec.DataPrepper.AdditionalConfig = map[string]string{
						"log_pipeline_batch_size":    "200",
						"trace_pipeline_buffer_size": "25600",
						"trace_pipeline_workers":     "8",
					}
				})

				It("should return an error and not reconcile resources", func() {
					result, err := dataPrepperReconciler.Reconcile(ctx, instance)
					Expect(err).To(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					dataPrepperDeployment := &appsv1.Deployment{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper-deployment", Namespace: namespace}, dataPrepperDeployment)
					Expect(err).To(HaveOccurred())
				})
			})

			When("Data Prepper is not in a HA setup (dev plan)", func() {
				BeforeEach(func() {
					instance.Spec.DataPrepper.Replicas = 1
				})

				It("should not deploy a PDB", func() {
					result, err := dataPrepperReconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					dataPrepperPDB := &policyv1.PodDisruptionBudget{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "data-prepper-pdb", Namespace: namespace}, dataPrepperPDB)
					Expect(err).To(HaveOccurred())
					Expect(apierrors.IsNotFound(err)).To(BeTrue())
				})
			})
		})

		When("the Data Prepper deployment has less than 1 available replica", func() {
			BeforeEach(func() {
				// 1. deploy Data Prepper
				result, err := dataPrepperReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 2. update the status of Data Prepper deployment
				deployment := builders.BuildDataPrepperDeployment(instance, "95")
				deployment.Status = appsv1.DeploymentStatus{
					Replicas:          1,
					ReadyReplicas:     0,
					AvailableReplicas: 0,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to not available (false) state", func() {
				// 3. run reconcile again
				result, err := dataPrepperReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 4. check condition
				condition := conditionHelper.GetCondition(instance, conditions.DataPrepperAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionFalse))
				Expect(condition.Reason).To(Equal(conditions.NotAvailable))
				Expect(condition.Message).To(Equal("No replicas are available"))
			})
		})

		When("Data Prepper deployment has 1 or more available replica", func() {
			BeforeEach(func() {
				// 1. deploy Data Prepper
				result, err := dataPrepperReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 2. update status of Data Prepper deployment
				deployment := builders.BuildDataPrepperDeployment(instance, "95")
				deployment.Status = appsv1.DeploymentStatus{
					Replicas:          1,
					ReadyReplicas:     1,
					AvailableReplicas: 1,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to available (true) state", func() {
				// 3. run reconcile again
				result, err := dataPrepperReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 4. check condition
				condition := conditionHelper.GetCondition(instance, conditions.DataPrepperAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Available))
				Expect(condition.Message).To(Equal("1 of 1 replica(s) are available"))
			})
		})
	})

	Context("CleanUp()", func() {
		It("should return no error and schedule no requeue", func() {
			result, err := dataPrepperReconciler.CleanUp(ctx, instance)
			Expect(err).NotTo(HaveOccurred())
			Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
		})
	})

	Context("UpdateStatusConditions()", func() {
		It("returns false", func() {
			Expect(dataPrepperReconciler.UpdateStatusConditions(ctx, instance)).To(BeFalse())
		})
	})

	Context("GetValidConditionNames()", func() {
		It("should return a list of valid condition names", func() {
			Expect(dataPrepperReconciler.GetValidConditionNames(instance)).To(Equal([]string{
				conditions.DataPrepperReconciled,
				conditions.DataPrepperAvailable,
			}))
		})
	})

	Context("GetName()", func() {
		It("should return the name of the reconciler", func() {
			Expect(dataPrepperReconciler.GetName()).To(Equal("DataPrepperReconciler"))
		})
	})
})
