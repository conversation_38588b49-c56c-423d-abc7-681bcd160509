package reconcilers_test

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	appsv1 "k8s.io/api/apps/v1"
	autoscalingv2 "k8s.io/api/autoscaling/v2"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	policyv1 "k8s.io/api/policy/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("FluentdReconciler", func() {
	const (
		instanceName        = "cls-test"
		namespace           = "sf-test"
		fluentdTemplatePath = "../../templates/fluentd"
	)

	var (
		instance                 *clv1beta1.Instance
		fluentdReconciler        reconcilers.ComponentReconciler
		resourceReconciler       reconciler.ResourceReconciler
		conditionHelper          = conditions.NewConditionHelper()
		passwordStoreServiceMock = fake.NewPasswordStoreServiceMock()
		ctx                      = context.Background()
		instanceNamespacedName   = types.NamespacedName{
			Name:      instanceName,
			Namespace: namespace,
		}
	)

	BeforeEach(func() {
		resourceReconciler = reconciler.NewReconcilerWith(k8sClient)

		Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      instanceName,
				Namespace: namespace,
			},
			Spec: clv1beta1.InstanceSpec{
				Fluentd: clv1beta1.Fluentd{
					Enabled:            true,
					MaxIngestInstances: 5,
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    *resource.NewQuantity(1, resource.DecimalSI),
							corev1.ResourceMemory: *resource.NewQuantity(100, resource.DecimalSI),
						},
					},
					Autoscaler: clv1beta1.Autoscaler{
						MinReplicas: 2,
						MaxReplicas: 10,
					},
					AdditionalConfig: map[string]string{
						"output_chunk_limit_size": "10M",
						"output_total_limit_size": "289M",
					},
				},
			},
		}

		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace,
			},
		}

		err := k8sClient.Get(ctx, types.NamespacedName{Name: namespace}, ns)
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the namespace")
			Expect(k8sClient.Create(ctx, ns)).To(Succeed())
		}

		err = k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{})
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the CustomResource for the CLOE instance")
			Expect(k8sClient.Create(ctx, instance)).To(Succeed())
		}

		fluentdReconciler = reconcilers.NewFluentdReconciler(k8sClient, conditionHelper,
			resourceReconciler, passwordStoreServiceMock, fluentdTemplatePath)
	})

	AfterEach(func() {
		Expect(k8sClient.DeleteAllOf(ctx, &clv1beta1.Instance{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &appsv1.Deployment{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Secret{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Service{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &networkingv1.Ingress{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &policyv1.PodDisruptionBudget{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &autoscalingv2.HorizontalPodAutoscaler{}, client.InNamespace(namespace))).To(Succeed())
	})

	Context("Reconcile()", func() {
		When("fluentd is not yet deployed", func() {
			It("should reconcile all fluentd resources", func() {
				result, err := fluentdReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				fluentdDeployment := &appsv1.Deployment{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-deployment", Namespace: namespace}, fluentdDeployment)
				Expect(err).NotTo(HaveOccurred())
				Expect(fluentdDeployment.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(fluentdDeployment.OwnerReferences[0].Name).To(Equal("cls-test"))

				fluentdAuthSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-auth", Namespace: namespace}, fluentdAuthSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(fluentdAuthSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(fluentdAuthSecret.OwnerReferences[0].Name).To(Equal("cls-test"))

				fluentdConfigSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-config", Namespace: namespace}, fluentdConfigSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(fluentdConfigSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(fluentdConfigSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(fluentdConfigSecret.Data["cfsyslog.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["error.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["fluent.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["json.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["metrics.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["monitoring.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["output_settings.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["buffer_settings.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["error_buffer_settings.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["sanitize_json.conf"]).ToNot(BeEmpty())
				Expect(fluentdConfigSecret.Data["sanitize_syslog.conf"]).ToNot(BeEmpty())

				Expect(string(fluentdConfigSecret.Data["output_settings.conf"])).To(ContainSubstring("@type opensearch_alias\n"))

				fluentdService := &corev1.Service{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-service", Namespace: namespace}, fluentdService)
				Expect(err).NotTo(HaveOccurred())
				Expect(fluentdService.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(fluentdService.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(fluentdService.Spec.Ports).To(HaveLen(5))
				Expect(fluentdService.Spec.Ports[0].Name).To(Equal("metrics"))
				Expect(fluentdService.Spec.Ports[0].Port).To(Equal(int32(24231)))
				Expect(fluentdService.Spec.Ports[1].Name).To(Equal("fluentbit"))
				Expect(fluentdService.Spec.Ports[1].Port).To(Equal(int32(24224)))
				Expect(fluentdService.Spec.Ports[2].Name).To(Equal("custommetrics"))
				Expect(fluentdService.Spec.Ports[2].Port).To(Equal(int32(24226)))
				Expect(fluentdService.Spec.Ports[3].Name).To(Equal("cfsyslog"))
				Expect(fluentdService.Spec.Ports[3].Port).To(Equal(int32(24222)))
				Expect(fluentdService.Spec.Ports[4].Name).To(Equal("monitoring"))
				Expect(fluentdService.Spec.Ports[4].Port).To(Equal(int32(9881)))

				fluentdAutoscaler := &autoscalingv2.HorizontalPodAutoscaler{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-scaler", Namespace: namespace}, fluentdAutoscaler)
				Expect(err).NotTo(HaveOccurred())
				Expect(fluentdAutoscaler.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(fluentdAutoscaler.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(*fluentdAutoscaler.Spec.MinReplicas).To(Equal(int32(2)))
				Expect(fluentdAutoscaler.Spec.MaxReplicas).To(Equal(int32(5)))

				fluentdIngress := &networkingv1.Ingress{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-ingress", Namespace: namespace}, fluentdIngress)
				Expect(err).NotTo(HaveOccurred())
				Expect(fluentdIngress.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(fluentdIngress.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(fluentdIngress.Spec.TLS[0].Hosts).To(ContainElements(
					"ingest-sf-test.test.ingress.sap.com",
					"fluentd-sf-test.test.ingress.sap.com"))

				fluentdIngressMtls := &networkingv1.Ingress{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-ingress-mtls", Namespace: namespace}, fluentdIngressMtls)
				Expect(err).NotTo(HaveOccurred())
				Expect(fluentdIngressMtls.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(fluentdIngressMtls.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(fluentdIngressMtls.Spec.TLS[0].Hosts).To(ContainElement(
					"ingest-mtls-sf-test.test.ingress.sap.com"))

				fluentdPDB := &policyv1.PodDisruptionBudget{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-pdb", Namespace: namespace}, fluentdPDB)
				Expect(err).NotTo(HaveOccurred())
				Expect(fluentdPDB.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(fluentdPDB.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(fluentdPDB.Spec.MaxUnavailable.IntVal).To(Equal(int32(1)))
			})

			It("should set the reconciled condition", func() {
				result, err := fluentdReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				condition := conditionHelper.GetCondition(instance, conditions.FluentdReconciled)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Succeeded))
				Expect(condition.Message).To(Equal("Successfully reconciled resources"))
			})

			When("the instance is missing fluentd configuration", func() {
				BeforeEach(func() {
					instance.Spec.Fluentd.Resources.Requests = corev1.ResourceList{}
					instance.Spec.Fluentd.Autoscaler.MinReplicas = 0
				})

				It("should not requeue and not reconcile resources", func() {
					result, err := fluentdReconciler.Reconcile(ctx, instance)
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					fluentdDeployment := &appsv1.Deployment{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-deployment", Namespace: namespace}, fluentdDeployment)
					Expect(err).To(HaveOccurred())
				})
			})

			When("the instance is missing entries in fluentd AdditionalConfig", func() {
				BeforeEach(func() {
					delete(instance.Spec.Fluentd.AdditionalConfig, "output_chunk_limit_size")
					delete(instance.Spec.Fluentd.AdditionalConfig, "output_total_limit_size")
				})

				It("should not requeue and not reconcile resources", func() {
					result, err := fluentdReconciler.Reconcile(ctx, instance)
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					fluentdDeployment := &appsv1.Deployment{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-deployment", Namespace: namespace}, fluentdDeployment)
					Expect(err).To(HaveOccurred())
				})
			})

			When("fluentd is not in a HA setup (dev plan)", func() {
				BeforeEach(func() {
					instance.Spec.Fluentd.Autoscaler.MinReplicas = 1
					instance.Spec.Fluentd.Autoscaler.MaxReplicas = 1
				})

				It("should not deploy a PDB", func() {
					result, err := fluentdReconciler.Reconcile(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					fluentdPDB := &policyv1.PodDisruptionBudget{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-pod-disruption-budget", Namespace: namespace}, fluentdPDB)
					Expect(err).To(HaveOccurred())
					Expect(apierrors.IsNotFound(err)).To(BeTrue())
				})
			})
		})

		When("fluentd deployment has less than 1 available replicas", func() {
			BeforeEach(func() {
				// 1. deploy fluentd
				result, err := fluentdReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 2. update status of fluentd deployment
				deployment := builders.BuildFluentdDeployment(instance)
				deployment.Status = appsv1.DeploymentStatus{
					Replicas:          1,
					ReadyReplicas:     0,
					AvailableReplicas: 0,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to not available (false) state", func() {
				// 3. run reconcile again
				result, err := fluentdReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 4. check condition
				condition := conditionHelper.GetCondition(instance, conditions.FluentdAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionFalse))
				Expect(condition.Reason).To(Equal(conditions.NotAvailable))
				Expect(condition.Message).To(Equal("No replicas are available"))
			})
		})

		When("fluentd deployment has 1 or more available replicas", func() {
			BeforeEach(func() {
				// 1. deploy fluentd
				result, err := fluentdReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 2. update status of fluentd deployment
				deployment := builders.BuildFluentdDeployment(instance)
				deployment.Status = appsv1.DeploymentStatus{
					Replicas:          1,
					ReadyReplicas:     1,
					AvailableReplicas: 1,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to available (true) state", func() {
				// 3. run reconcile again
				result, err := fluentdReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 4. check condition
				condition := conditionHelper.GetCondition(instance, conditions.FluentdAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Available))
				Expect(condition.Message).To(Equal("1 of 1 replica(s) are available"))
			})
		})

		Context("Index rollover feature flags", func() {
			When("landscape-level feature flag disables the index rollover", func() {
				BeforeEach(func() {
					Expect(config.GetInstance().Read("../config/test_config/config_rollover_disabled.yaml", false)).To(Succeed())
				})

				AfterEach(func() {
					Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())
				})

				It("should reconcile the fluentd output_settings.conf correctly", func() {
					result, err := fluentdReconciler.Reconcile(ctx, instance)
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					fluentdConfigSecret := &corev1.Secret{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-config", Namespace: namespace}, fluentdConfigSecret)
					Expect(err).NotTo(HaveOccurred())
					Expect(string(fluentdConfigSecret.Data["output_settings.conf"])).To(ContainSubstring("@type opensearch\n"))
				})
			})

			When("landscape-level feature flag enables the index rollover", func() {
				// It is enabled by default in the test config: ../config/test_config/config.yaml

				It("should reconcile the fluentd output_settings.conf correctly", func() {
					result, err := fluentdReconciler.Reconcile(ctx, instance)
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					fluentdConfigSecret := &corev1.Secret{}
					err = k8sClient.Get(ctx, types.NamespacedName{Name: "fluentd-config", Namespace: namespace}, fluentdConfigSecret)
					Expect(err).NotTo(HaveOccurred())
					Expect(string(fluentdConfigSecret.Data["output_settings.conf"])).To(ContainSubstring("@type opensearch_alias\n"))
				})
			})
		})
	})

	Context("CleanUp()", func() {
		It("should return no error and schedule no requeue", func() {
			result, err := fluentdReconciler.CleanUp(ctx, instance)
			Expect(err).NotTo(HaveOccurred())
			Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
		})
	})

	Context("GetValidConditionNames()", func() {
		It("should return a list of valid condition names", func() {
			Expect(fluentdReconciler.GetValidConditionNames(instance)).To(Equal([]string{
				conditions.FluentdReconciled,
				conditions.FluentdAvailable,
			}))
		})
	})

	Context("UpdateStatusConditions()", func() {
		It("returns false", func() {
			Expect(fluentdReconciler.UpdateStatusConditions(ctx, instance)).To(BeFalse())
		})
	})

	Context("GetName()", func() {
		It("should return the name of the reconciler", func() {
			Expect(fluentdReconciler.GetName()).To(Equal("FluentdReconciler"))
		})
	})
})
