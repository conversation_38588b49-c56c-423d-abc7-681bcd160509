package reconcilers_test

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	policyv1 "k8s.io/api/policy/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("DashboardsReconciler", func() {
	const (
		instanceName           = "cls-test"
		namespace              = "sf-test"
		dashboardsTemplatePath = "../../templates/dashboards"
	)

	var (
		instance                 *clv1beta1.Instance
		dashboardsReconciler     reconcilers.ComponentReconciler
		resourceReconciler       reconciler.ResourceReconciler
		conditionHelper          = conditions.NewConditionHelper()
		passwordStoreServiceMock = fake.NewPasswordStoreServiceMock()
		dashboardsClientMock     = fake.NewDashboardsClientMock()
		dashboardsService        = services.NewDashboardsService(dashboardsClientMock, passwordStoreServiceMock)
		featureConfigurationMock = fake.NewFeatureConfigurationMock()
		imageHelperMock          = fake.NewImageHelperMock()
		ctx                      = context.Background()
		instanceNamespacedName   = types.NamespacedName{
			Name:      instanceName,
			Namespace: namespace,
		}
	)

	BeforeEach(func() {
		resourceReconciler = reconciler.NewReconcilerWith(k8sClient)

		Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())

		imageHelperMock.PrepareGetAvailableImages(map[string]string{
			"stable":      "docker.io/opensearchproject/opensearch-dashboards:1.0.0",
			"preview":     "docker.io/opensearchproject/opensearch-dashboards:2.0.0",
			"development": "docker.io/opensearchproject/opensearch-dashboards:2.1.0",
		})

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      instanceName,
				Namespace: namespace,
			},
			Spec: clv1beta1.InstanceSpec{
				OpenSearch: clv1beta1.OpenSearch{
					Dashboards: clv1beta1.Dashboards{
						Replicas: 2,
						Resources: corev1.ResourceRequirements{
							Requests: corev1.ResourceList{
								corev1.ResourceCPU:    *resource.NewQuantity(1, resource.DecimalSI),
								corev1.ResourceMemory: *resource.NewQuantity(100, resource.DecimalSI),
							},
							Limits: corev1.ResourceList{
								corev1.ResourceCPU:    *resource.NewQuantity(2, resource.DecimalSI),
								corev1.ResourceMemory: *resource.NewQuantity(200, resource.DecimalSI),
							},
						},
					},
				},
			},
		}

		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace,
			},
		}

		err := k8sClient.Get(ctx, types.NamespacedName{Name: namespace}, ns)
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the namespace")
			Expect(k8sClient.Create(ctx, ns)).To(Succeed())
		}

		err = k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{})
		if err != nil && apierrors.IsNotFound(err) {
			By("creating the CustomResource for the CLOE instance")
			Expect(k8sClient.Create(ctx, instance)).To(Succeed())
		}

		// Pretend OpenSearch update reconciler has provided an OpenSearch support version
		instance.Status.OpenSearchClusterSupportVersion = "1.0.0"
		err = k8sClient.Status().Update(ctx, instance)
		Expect(err).To(Succeed())

		dashboardsReconciler = reconcilers.NewDashboardsReconciler(k8sClient, conditionHelper, dashboardsService,
			resourceReconciler, imageHelperMock, passwordStoreServiceMock, featureConfigurationMock, dashboardsTemplatePath)
	})

	AfterEach(func() {
		Expect(k8sClient.DeleteAllOf(ctx, &clv1beta1.Instance{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &appsv1.Deployment{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Secret{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.ConfigMap{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Service{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &networkingv1.Ingress{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &policyv1.PodDisruptionBudget{}, client.InNamespace(namespace))).To(Succeed())
	})

	Context("Reconcile()", func() {
		When("Reconcile() is called", func() {
			It("should reconcile all dashboard resources", func() {
				result, err := dashboardsReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				deployment := &appsv1.Deployment{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "dashboards-deployment", Namespace: namespace}, deployment)
				Expect(err).NotTo(HaveOccurred())
				Expect(deployment.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(deployment.OwnerReferences[0].Name).To(Equal("cls-test"))

				dashboardsIngress := &networkingv1.Ingress{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "dashboards-ingress", Namespace: namespace}, dashboardsIngress)
				Expect(err).NotTo(HaveOccurred())
				Expect(dashboardsIngress.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(dashboardsIngress.OwnerReferences[0].Name).To(Equal("cls-test"))

				kibanaIngress := &networkingv1.Ingress{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "kibana-ingress", Namespace: namespace}, kibanaIngress)
				Expect(err).NotTo(HaveOccurred())
				Expect(kibanaIngress.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(kibanaIngress.OwnerReferences[0].Name).To(Equal("cls-test"))

				dashboardsService := &corev1.Service{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "dashboards-service", Namespace: namespace}, dashboardsService)
				Expect(err).NotTo(HaveOccurred())
				Expect(dashboardsService.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(dashboardsService.OwnerReferences[0].Name).To(Equal("cls-test"))

				dashboardsConfigSecret := &corev1.Secret{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "dashboards-config", Namespace: namespace}, dashboardsConfigSecret)
				Expect(err).NotTo(HaveOccurred())
				Expect(dashboardsConfigSecret.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(dashboardsConfigSecret.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(dashboardsConfigSecret.Data["customlogo.svg"]).ToNot(BeEmpty())
				Expect(dashboardsConfigSecret.Data["opensearch_dashboards.yml"]).ToNot(BeEmpty())

				dashboardsSavedObjects := &corev1.ConfigMap{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "dashboards-saved-objects-config", Namespace: namespace}, dashboardsSavedObjects)
				Expect(err).NotTo(HaveOccurred())
				Expect(dashboardsSavedObjects.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(dashboardsSavedObjects.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(dashboardsSavedObjects.Data["cf-data"]).ToNot(BeEmpty())
				Expect(dashboardsSavedObjects.Data["kyma-data"]).ToNot(BeEmpty())
				Expect(dashboardsSavedObjects.Data["metadata"]).ToNot(BeEmpty())
				Expect(dashboardsSavedObjects.Data["metrics-data"]).ToNot(BeEmpty())
				Expect(dashboardsSavedObjects.Data["config-data"]).ToNot(BeEmpty())
				Expect(dashboardsSavedObjects.Data["k8s-data"]).ToNot(BeEmpty())
				Expect(dashboardsSavedObjects.Data["otel-data"]).ToNot(BeEmpty())
				Expect(dashboardsSavedObjects.Data["using-your-instance-data"]).ToNot(BeEmpty())
				Expect(dashboardsSavedObjects.Data["service-data"]).ToNot(BeEmpty())

				dashboardsPDB := &policyv1.PodDisruptionBudget{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "dashboards-pdb", Namespace: namespace}, dashboardsPDB)
				Expect(err).NotTo(HaveOccurred())
				Expect(dashboardsPDB.OwnerReferences[0].Kind).To(Equal("Instance"))
				Expect(dashboardsPDB.OwnerReferences[0].Name).To(Equal("cls-test"))
				Expect(dashboardsPDB.Spec.MaxUnavailable.IntVal).To(Equal(int32(1)))
			})

			It("should set the reconciled condition", func() {
				result, err := dashboardsReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				condition := conditionHelper.GetCondition(instance, conditions.DashboardsReconciled)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Succeeded))
				Expect(condition.Message).To(Equal("Successfully reconciled resources (image channel: stable - 1.0.0)"))
			})
		})

		When("it is called with an instance that is missing dashboards configuration", func() {
			BeforeEach(func() {
				instance.Spec.OpenSearch.Dashboards.Replicas = 0
			})

			It("should not requeue and not reconcile resources", func() {
				result, err := dashboardsReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				dashboardsDeployment := &appsv1.Deployment{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "dashboards-deployment", Namespace: namespace}, dashboardsDeployment)
				Expect(err).To(HaveOccurred())
			})
		})

		When("there is no dashboards image available, matching the openSearch version", func() {
			BeforeEach(func() {
				instance.Status.OpenSearchClusterSupportVersion = "2.5.0"
				err := k8sClient.Status().Update(ctx, instance)
				Expect(err).To(Succeed())
			})

			It("should not reconcile resources", func() {
				result, err := dashboardsReconciler.Reconcile(ctx, instance)
				Expect(err).ToNot(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				dashboardsDeployment := &appsv1.Deployment{}
				err = k8sClient.Get(ctx, types.NamespacedName{Name: "dashboards-deployment", Namespace: namespace}, dashboardsDeployment)
				Expect(err).To(HaveOccurred())
			})

			It("should set the reconciled condition to false", func() {
				result, err := dashboardsReconciler.Reconcile(ctx, instance)
				Expect(err).ToNot(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				condition := conditionHelper.GetCondition(instance, conditions.DashboardsReconciled)
				Expect(condition.Status).To(Equal(metav1.ConditionFalse))
				Expect(condition.Reason).To(Equal(conditions.Failed))
				Expect(condition.Message).To(Equal("Error: failed to determine OpenSearch Dashboards image: no matching image version found for support version '2.5.0'"))
			})

			When("there is a deployment with a different version running already", func() {
				BeforeEach(func() {
					dashboardsDeployment := builders.BuildDashboardsDeployment(instance, "dashboards-fallback:1.7.0")
					Expect(k8sClient.Create(ctx, dashboardsDeployment)).To(Succeed())
				})

				It("should set the reconciled condition to false", func() {
					result, err := dashboardsReconciler.Reconcile(ctx, instance)
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					condition := conditionHelper.GetCondition(instance, conditions.DashboardsReconciled)
					Expect(condition.Status).To(Equal(metav1.ConditionFalse))
					Expect(condition.Reason).To(Equal(conditions.Failed))
					Expect(condition.Message).To(Equal("Error: failed to determine OpenSearch Dashboards image: no matching image version found for support version '2.5.0'"))
				})
			})

			When("there is a deployment with a matching version running already", func() {
				BeforeEach(func() {
					dashboardsDeployment := builders.BuildDashboardsDeployment(instance, "dashboards-fallback:2.5.0")
					Expect(k8sClient.Create(ctx, dashboardsDeployment)).To(Succeed())
				})

				It("reconcile all resources and set the reconciled condition to true", func() {
					result, err := dashboardsReconciler.Reconcile(ctx, instance)
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

					condition := conditionHelper.GetCondition(instance, conditions.DashboardsReconciled)
					Expect(condition.Status).To(Equal(metav1.ConditionTrue))
					Expect(condition.Reason).To(Equal(conditions.Succeeded))
					Expect(condition.Message).To(Equal("Successfully reconciled resources (image channel: discovered - 2.5.0)"))
				})
			})
		})

		When("dashboards deployment has less than 1 available replicas", func() {
			BeforeEach(func() {
				// 1. deploy dashboards
				result, err := dashboardsReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 2. update status of dashboards deployment
				deployment := builders.BuildDashboardsDeployment(instance, "osd:1.3")
				deployment.Status = appsv1.DeploymentStatus{
					Replicas:          1,
					ReadyReplicas:     0,
					AvailableReplicas: 0,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to not available (false) state", func() {
				// 3. run reconcile again
				result, err := dashboardsReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 4. check condition
				condition := conditionHelper.GetCondition(instance, conditions.DashboardsAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionFalse))
				Expect(condition.Reason).To(Equal(conditions.NotAvailable))
				Expect(condition.Message).To(Equal("No replicas are available"))
			})
		})

		When("dashboards deployment has 1 or more available replicas", func() {
			BeforeEach(func() {
				// 1. deploy dashboards
				result, err := dashboardsReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 2. update status of dashboards deployment
				deployment := builders.BuildDashboardsDeployment(instance, "osd:1.3")
				deployment.Status = appsv1.DeploymentStatus{
					Replicas:          1,
					ReadyReplicas:     1,
					AvailableReplicas: 1,
				}
				Expect(k8sClient.Status().Update(ctx, deployment)).To(Succeed())
			})

			It("should return no error and schedule no requeue, and update condition to available (true) state", func() {
				// 3. run reconcile again
				result, err := dashboardsReconciler.Reconcile(ctx, instance)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))

				// 4. check condition
				condition := conditionHelper.GetCondition(instance, conditions.DashboardsAvailable)
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Available))
				Expect(condition.Message).To(Equal("1 of 1 replica(s) are available"))
			})
		})
	})

	Context("UpdateStatusConditions()", func() {
		var status = models.DashboardsStatusResponse{}
		BeforeEach(func() {
			status = models.DashboardsStatusResponse{
				Name: "dashboards",
				Status: models.DashboardStatus{
					Overall: models.StatusOverall{
						State: "green",
					},
				},
			}
		})

		When("dashboards status condition is not yet set in instance CR", func() {
			BeforeEach(func() {
				dashboardsClientMock.PrepareStatus(status)
			})

			It("should return true", func() {
				Expect(dashboardsReconciler.UpdateStatusConditions(ctx, instance)).To(BeTrue())
			})

			It("should set the status condition in the instance CR", func() {
				Expect(instance.Status.Conditions).To(BeEmpty())
				dashboardsReconciler.UpdateStatusConditions(ctx, instance)
				Expect(instance.Status.Conditions).To(HaveLen(1))

				createdCondition := conditionHelper.GetCondition(instance, conditions.DashboardsStatus)
				Expect(createdCondition.Status).To(Equal(metav1.ConditionTrue))
				Expect(createdCondition.Reason).To(Equal(conditions.Green))
				Expect(createdCondition.Message).To(Equal("Dashboard status is green."))
			})
		})

		When("dashboards status condition is already set", func() {
			BeforeEach(func() {
				condition := metav1.Condition{
					Type:    conditions.DashboardsStatus,
					Status:  metav1.ConditionTrue,
					Reason:  conditions.Green,
					Message: "Dashboard status is green.",
				}
				dashboardsClientMock.PrepareStatus(status)
				conditionHelper.SetCondition(instance, condition)
			})

			It("should return false", func() {
				Expect(dashboardsReconciler.UpdateStatusConditions(ctx, instance)).To(BeFalse())
			})

			It("should not change the status condition in the instance CR", func() {
				Expect(instance.Status.Conditions).To(HaveLen(1))
				dashboardsReconciler.UpdateStatusConditions(ctx, instance)
				Expect(instance.Status.Conditions).To(HaveLen(1))

				createdCondition := conditionHelper.GetCondition(instance, conditions.DashboardsStatus)
				Expect(createdCondition.Status).To(Equal(metav1.ConditionTrue))
				Expect(createdCondition.Reason).To(Equal(conditions.Green))
				Expect(createdCondition.Message).To(Equal("Dashboard status is green."))
			})
		})
	})

	Context("CleanUp()", func() {
		It("should return no error and schedule no requeue", func() {
			result, err := dashboardsReconciler.CleanUp(ctx, instance)
			Expect(err).NotTo(HaveOccurred())
			Expect(result).To(Equal(reconcile.Result{Requeue: false, RequeueAfter: 0}))
		})
	})

	Context("GetValidConditionNames()", func() {
		It("should return a list of valid condition names", func() {
			Expect(dashboardsReconciler.GetValidConditionNames(instance)).To(Equal([]string{
				conditions.DashboardsReconciled,
				conditions.DashboardsAvailable,
				conditions.DashboardsStatus,
			}))
		})
	})

	Context("GetName()", func() {
		It("should return the name of the reconciler", func() {
			Expect(dashboardsReconciler.GetName()).To(Equal("DashboardsReconciler"))
		})
	})
})
