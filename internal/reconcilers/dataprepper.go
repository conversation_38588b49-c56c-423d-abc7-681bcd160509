package reconcilers

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/templating"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	"github.com/go-logr/logr"
	appsv1 "k8s.io/api/apps/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

const (
	DataPrepperReconcilerName          = "DataPrepperReconciler"
	dataPrepperOSUsername              = "otelWriter"
	circuitBreakersHeapUsagePercentage = 95
	minHeap                            = "512m"
	defaultCircuitBreakersHeapUsage    = "10000mb"
)

type DataPrepperReconciler struct {
	logger                   logr.Logger
	client                   client.Client
	conditionHelper          conditions.ConditionHelper
	resourceReconciler       reconciler.ResourceReconciler
	passwordStoreReader      services.PasswordStoreService
	templatedConfigConfigMap templating.TemplatedConfig
	templatedConfigSecret    templating.TemplatedConfig
}

func NewDataPrepperReconciler(client client.Client, conditionHelper conditions.ConditionHelper,
	resourceReconciler reconciler.ResourceReconciler, passwordStoreReader services.PasswordStoreService,
	templatePath string) ComponentReconciler {
	return &DataPrepperReconciler{
		logger:              log.Log.WithName("data-prepper-reconciler"),
		client:              client,
		conditionHelper:     conditionHelper,
		resourceReconciler:  resourceReconciler,
		passwordStoreReader: passwordStoreReader,
		templatedConfigConfigMap: templating.NewTemplatedConfig(templatePath, "data-prepper-config.yaml",
			"log4j2-rolling.properties", "probes.sh"),
		templatedConfigSecret: templating.NewTemplatedConfig(templatePath, "data-prepper-wait-for-os-and-start.sh",
			"trace_analytics_no_ssl.yaml"),
	}
}

func (r *DataPrepperReconciler) Reconcile(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	result := reconciler.CombinedResult{}

	// Check if the instance CR is lacking the required configuration.
	if instance.Spec.DataPrepper.Resources.Requests.Cpu().IsZero() {
		result.CombineErr(errors.New("dataprepper configuration is not available in instance CR"))
		r.conditionHelper.SetReconcileCondition(instance, conditions.DataPrepperReconciled, result)

		// Do not return an error here, as retrying would not help in this case
		return result.Result, nil
	}

	// Get credentials object from password-store
	osCredentials, err := r.passwordStoreReader.GetOpenSearchCredentials(ctx, instance, dataPrepperOSUsername)
	if err != nil {
		return ctrl.Result{}, err
	}

	// Get additional configuration
	maxHeap, circuitBreakersHeapUsage, err := r.getDataPrepperConfig(instance)
	if err != nil {
		return ctrl.Result{}, err
	}

	// Source object for config templates and builder
	configSource := struct {
		DataPrepper              clv1beta1.DataPrepper
		Credentials              models.Credentials
		MaxHeap                  string
		CircuitBreakersHeapUsage string
	}{
		DataPrepper:              instance.Spec.DataPrepper,
		Credentials:              osCredentials,
		MaxHeap:                  maxHeap,
		CircuitBreakersHeapUsage: circuitBreakersHeapUsage,
	}

	// Determine component states
	componentStateCommon := reconciler.StateAbsent
	componentStateHeadlessService := reconciler.StateAbsent
	if instance.Spec.DataPrepper.Enabled {
		componentStateCommon = reconciler.StatePresent
		if instance.Spec.DataPrepper.Replicas > 1 {
			componentStateHeadlessService = reconciler.StatePresent
		}
	}

	// Reconcile Data Prepper related Kubernetes resources
	service := builders.BuildDataPrepperService(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, service, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(service, componentStateCommon))

	headlessService := builders.BuildDataPrepperHeadlessService(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, headlessService, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(headlessService, componentStateHeadlessService))

	ingress := builders.BuildDataPrepperIngress(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, ingress, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(ingress, componentStateCommon))

	// Create configmap and populate with instance-specific templated data
	config := builders.BuildDataPrepperConfigMap(instance)
	result.CombineErr(r.templatedConfigConfigMap.PopulateConfigMap(templating.PARSE_AS_FILES,
		configSource, config))
	result.CombineErr(ctrl.SetControllerReference(instance, config, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(config, componentStateCommon))

	// Create secret and populate with instance-specific templated data
	secret := builders.BuildDataPrepperSecret(instance)
	result.CombineErr(r.templatedConfigSecret.PopulateSecret(templating.PARSE_AS_FILES,
		configSource, secret))
	result.CombineErr(ctrl.SetControllerReference(instance, secret, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(secret, componentStateCommon))

	deployment := builders.BuildDataPrepperDeployment(instance, maxHeap)
	result.CombineErr(utils.SetHashFromCM(deployment.Spec.Template.Annotations, config))
	result.CombineErr(utils.SetHashFromSecret(deployment.Spec.Template.Annotations, secret))
	result.CombineErr(ctrl.SetControllerReference(instance, deployment, r.client.Scheme()))
	result.Combine(r.resourceReconciler.ReconcileResource(deployment, componentStateCommon))

	// Only create/reconcile the PDB for instances with multiple Data Prepper pods
	podDisruptionBudget := builders.BuildDataPrepperPodDisruptionBudget(instance)
	result.CombineErr(ctrl.SetControllerReference(instance, podDisruptionBudget, r.client.Scheme()))
	if instance.Spec.DataPrepper.Enabled && instance.Spec.DataPrepper.Replicas > 1 {
		result.Combine(r.resourceReconciler.ReconcileResource(podDisruptionBudget, reconciler.StatePresent))
	} else {
		result.Combine(r.resourceReconciler.ReconcileResource(podDisruptionBudget, reconciler.StateAbsent))
	}

	// Set available conditions
	result.CombineErr(r.updateAvailableCondition(ctx, instance, deployment))

	// Set reconcile condition eventually
	stateLabel := strings.ToLower(string(componentStateCommon))
	r.conditionHelper.SetReconcileCondition(instance, conditions.DataPrepperReconciled, result, stateLabel)

	return result.Result, result.Err
}

func (r *DataPrepperReconciler) CleanUp(context.Context, *clv1beta1.Instance) (ctrl.Result, error) {
	// Nothing to do here, as the Data Prepper resources are owned by the instance
	return ctrl.Result{}, nil
}

// GetValidConditionNames returns a list of condition names related to the component.
// If the component can be disabled AND is disabled, only the '...Reconciled' condition should be returned.
func (r *DataPrepperReconciler) GetValidConditionNames(instance *clv1beta1.Instance) []string {
	validConditions := []string{conditions.DataPrepperReconciled}
	if instance.Spec.DataPrepper.Enabled {
		validConditions = append(validConditions, conditions.DataPrepperAvailable)
	}
	return validConditions
}

// UpdateStatusConditions updates the components status condition(s). This method is called frequently and
// should be lightweight. It should only update the local instance, which will be persisted by the watcher
// after all component reconcilers have been called.
func (r *DataPrepperReconciler) UpdateStatusConditions(context.Context, *clv1beta1.Instance) bool {
	// not (yet) implemented for Data Prepper
	return false
}

// GetName returns the name of the reconciler for monitoring and logging purposes.
func (r *DataPrepperReconciler) GetName() string {
	return DataPrepperReconcilerName
}

//nolint:dupl // This availability condition differs from component to component
func (r *DataPrepperReconciler) updateAvailableCondition(ctx context.Context, instance *clv1beta1.Instance, deployment *appsv1.Deployment) error {
	// Determine ready status based on replica availability
	err := r.client.Get(ctx, client.ObjectKeyFromObject(deployment), deployment)
	if err != nil {
		if apierrors.IsNotFound(err) {
			r.conditionHelper.SetCondition(instance, metav1.Condition{
				Type:    conditions.DataPrepperAvailable,
				Status:  metav1.ConditionFalse,
				Reason:  conditions.NotFound,
				Message: "Deployment does not exist (yet)",
			})

			// Do not return an error in this case as rescheduling would not help
			// Immediately after the deployment is created we cannot expect it to exist already (async processing)
			return nil
		}
		return err
	}

	if deployment.Status.AvailableReplicas >= 1 {
		r.conditionHelper.SetCondition(instance, metav1.Condition{
			Type:   conditions.DataPrepperAvailable,
			Status: metav1.ConditionTrue,
			Reason: conditions.Available,
			Message: fmt.Sprintf("%d of %d replica(s) are available",
				deployment.Status.AvailableReplicas, deployment.Status.Replicas),
		})
		return nil
	}
	r.conditionHelper.SetCondition(instance, metav1.Condition{
		Type:    conditions.DataPrepperAvailable,
		Status:  metav1.ConditionFalse,
		Reason:  conditions.NotAvailable,
		Message: "No replicas are available",
	})
	return nil
}

func (r *DataPrepperReconciler) getDataPrepperConfig(instance *clv1beta1.Instance) (string, string, error) {
	var maxHeap, circuitBreakersHeapUsage string
	maxHeapPercentageString, exists := instance.Spec.DataPrepper.AdditionalConfig["max_heap_percentage"]
	if !exists {
		return "", "", errors.New("instance CR does not contain Data Prepper field 'max_heap_percentage'")
	}

	maxHeapPercentageInt, err := strconv.Atoi(maxHeapPercentageString)
	if err != nil {
		return "", "", fmt.Errorf("error converting value for 'max_heap_percentage' to integer: %w", err)
	}

	maxHeap, err = utils.GetHeapFromMemory(maxHeapPercentageInt,
		instance.Spec.DataPrepper.Resources.Limits.Memory().String())
	if err != nil {
		r.logger.Error(err, "Error calculating max heap size: %v, using default value: %s", err, minHeap)
		maxHeap = minHeap
	}

	circuitBreakersHeapUsage, err = utils.GetDataPrepperCircuitBreakerFromHeap(
		circuitBreakersHeapUsagePercentage, maxHeap)
	if err != nil {
		r.logger.Error(err, "Error calculating circuit breaker heap usage: %v, using default value: %s",
			err, defaultCircuitBreakersHeapUsage)
		circuitBreakersHeapUsage = defaultCircuitBreakersHeapUsage
	}

	return maxHeap, circuitBreakersHeapUsage, nil
}
