package builders

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders/helpers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	policyv1 "k8s.io/api/policy/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/utils/ptr"
)

func BuildDashboardsDeployment(instance *clv1beta1.Instance, image string) *appsv1.Deployment {
	dashboardsImage := helpers.GetImageSpec(image)
	nodeMaxOldSpaceSize := strings.TrimSuffix(GetHeapFromMemory(50, instance.Spec.OpenSearch.Dashboards.Resources.Limits.Memory().String()), "m")

	return &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "dashboards-deployment",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app":     "dashboards",
				"release": "cls",
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: ptr.To(instance.Spec.OpenSearch.Dashboards.Replicas),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "dashboards",
				},
			},
			Strategy: appsv1.DeploymentStrategy{
				Type: appsv1.RollingUpdateDeploymentStrategyType,
				RollingUpdate: &appsv1.RollingUpdateDeployment{
					MaxUnavailable: &intstr.IntOrString{
						Type:   intstr.Int,
						IntVal: 1,
					},
					MaxSurge: &intstr.IntOrString{
						Type:   intstr.Int,
						IntVal: 0,
					},
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":     "dashboards",
						"release": "cls",
					},
					Annotations: map[string]string{},
				},
				Spec: corev1.PodSpec{
					DNSPolicy:     corev1.DNSClusterFirst,
					RestartPolicy: corev1.RestartPolicyAlways,
					SchedulerName: "default-scheduler",
					SecurityContext: &corev1.PodSecurityContext{
						FSGroup: ptr.To(int64(1000)),
					},
					TerminationGracePeriodSeconds: ptr.To(int64(30)),
					Containers: []corev1.Container{
						{
							Command: []string{
								"/usr/share/opensearch-dashboards/entry.sh",
							},
							Name:            "dashboards",
							Image:           dashboardsImage.Image,
							ImagePullPolicy: dashboardsImage.ImagePullPolicy,
							Ports: []corev1.ContainerPort{
								{
									Name:          "http",
									ContainerPort: 5601,
									Protocol:      corev1.ProtocolTCP,
								},
							},
							Env: []corev1.EnvVar{
								{
									Name:  "NODE_OPTIONS",
									Value: fmt.Sprintf("--max-old-space-size=%s", nodeMaxOldSpaceSize),
								},
							},
							ReadinessProbe: &corev1.Probe{
								InitialDelaySeconds: 10,
								TimeoutSeconds:      1,
								PeriodSeconds:       10,
								SuccessThreshold:    1,
								FailureThreshold:    3,
								ProbeHandler: corev1.ProbeHandler{
									TCPSocket: &corev1.TCPSocketAction{
										Port: intstr.IntOrString{
											Type:   intstr.Int,
											IntVal: 5601,
										},
									},
								},
							},
							TerminationMessagePath:   "/dev/termination-log",
							TerminationMessagePolicy: corev1.TerminationMessageReadFile,
							Resources:                instance.Spec.OpenSearch.Dashboards.Resources,
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "config",
									MountPath: "/usr/share/opensearch-dashboards/config/opensearch_dashboards.yml",
									SubPath:   "opensearch_dashboards.yml",
								},
								{
									Name:      "config",
									MountPath: "/usr/share/opensearch-dashboards/assets/cls/customlogo.svg",
									SubPath:   "customlogo.svg",
								},
								{
									Name:      "config",
									MountPath: "/usr/share/opensearch-dashboards/entry.sh",
									SubPath:   "entry.sh",
								},
							},
						},
					},
					ImagePullSecrets: dashboardsImage.ImagePullSecrets,
					Volumes: []corev1.Volume{
						{
							Name: "config",
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									DefaultMode: ptr.To[int32](0o544),
									SecretName:  "dashboards-config",
								},
							},
						},
					},
					TopologySpreadConstraints: []corev1.TopologySpreadConstraint{
						{
							MaxSkew:           1,
							TopologyKey:       "topology.kubernetes.io/zone",
							WhenUnsatisfiable: corev1.DoNotSchedule,
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{
									"app": "dashboards",
								},
							},
						},
						{
							MaxSkew:           1,
							TopologyKey:       "kubernetes.io/hostname",
							WhenUnsatisfiable: corev1.ScheduleAnyway,
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{
									"app": "dashboards",
								},
							},
						},
					},
				},
			},
		},
	}
}

func BuildDashboardsPodDisruptionBudget(instance *clv1beta1.Instance) *policyv1.PodDisruptionBudget {
	return &policyv1.PodDisruptionBudget{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "dashboards-pdb",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app": "dashboards",
			},
		},
		Spec: policyv1.PodDisruptionBudgetSpec{
			MaxUnavailable: ptr.To(intstr.FromInt32(1)),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": "dashboards",
				},
			},
		},
	}
}

func BuildDashboardsService(instance *clv1beta1.Instance) *corev1.Service {
	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "dashboards-service",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app":     "dashboards",
				"release": "cls",
				"role":    "data",
			},
		},
		Spec: corev1.ServiceSpec{
			Ports: []corev1.ServicePort{
				{
					Port:       443,
					TargetPort: intstr.FromInt32(5601),
					Protocol:   corev1.ProtocolTCP,
				},
			},
			Selector: map[string]string{
				"app":     "dashboards",
				"release": "cls",
			},
		},
	}
}

func BuildDashboardsIngress(instance *clv1beta1.Instance) *networkingv1.Ingress {
	cfg := config.GetInstance()

	return &networkingv1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "dashboards-ingress",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app":     "dashboards",
				"release": "cls",
			},
			Annotations: map[string]string{
				"kubernetes.io/tls-acme":                        "true",
				"nginx.ingress.kubernetes.io/proxy-buffer-size": "16k",
				"nginx.ingress.kubernetes.io/configuration-snippet": `more_set_headers "X-Frame-Options: DENY";
more_set_headers "Referrer-Policy: no-referrer";
server_tokens off;`,
			},
		},
		Spec: networkingv1.IngressSpec{
			IngressClassName: ptr.To("nginx"),
			Rules: []networkingv1.IngressRule{
				{
					Host: helpers.GetDashboardsHost(instance, cfg.GetIngress()),
					IngressRuleValue: networkingv1.IngressRuleValue{
						HTTP: &networkingv1.HTTPIngressRuleValue{
							Paths: []networkingv1.HTTPIngressPath{
								{
									Path:     "/",
									PathType: ptr.To(networkingv1.PathTypePrefix),
									Backend: networkingv1.IngressBackend{
										Service: &networkingv1.IngressServiceBackend{
											Name: "dashboards-service",
											Port: networkingv1.ServiceBackendPort{
												Number: 443,
											},
										},
									},
								},
							},
						},
					},
				},
			},
			TLS: []networkingv1.IngressTLS{
				{
					Hosts: []string{helpers.GetDashboardsHost(instance, cfg.GetIngress())},
				},
			},
		},
	}
}

func BuildKibanaIngress(instance *clv1beta1.Instance) *networkingv1.Ingress {
	cfg := config.GetInstance()

	return &networkingv1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "kibana-ingress",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app":     "dashboards",
				"release": "cls",
			},
			Annotations: map[string]string{
				"kubernetes.io/tls-acme":                         "true",
				"nginx.ingress.kubernetes.io/proxy-buffer-size":  "16k",
				"nginx.ingress.kubernetes.io/server-alias":       helpers.GetKibanaHost(instance, cfg.GetIngress()),
				"nginx.ingress.kubernetes.io/permanent-redirect": fmt.Sprintf("https://%s$request_uri", helpers.GetDashboardsHost(instance, cfg.GetIngress())),
				"nginx.ingress.kubernetes.io/configuration-snippet": `more_set_headers "X-Frame-Options: DENY";
more_set_headers "Referrer-Policy: no-referrer";
server_tokens off;
location /api/status {
 deny all;
 return 403;
}`,
			},
		},
		Spec: networkingv1.IngressSpec{
			IngressClassName: ptr.To("nginx"),
			Rules: []networkingv1.IngressRule{
				{
					Host: helpers.GetKibanaHost(instance, cfg.GetIngress()),
					IngressRuleValue: networkingv1.IngressRuleValue{
						HTTP: &networkingv1.HTTPIngressRuleValue{
							Paths: []networkingv1.HTTPIngressPath{
								{
									Path:     "/",
									PathType: ptr.To(networkingv1.PathTypePrefix),
									Backend: networkingv1.IngressBackend{
										Service: &networkingv1.IngressServiceBackend{
											Name: "dashboards-service",
											Port: networkingv1.ServiceBackendPort{
												Number: 443,
											},
										},
									},
								},
							},
						},
					},
				},
			},
			TLS: []networkingv1.IngressTLS{
				{
					Hosts: []string{helpers.GetKibanaHost(instance, cfg.GetIngress())},
				},
			},
		},
	}
}

func BuildDashboardsSavedObjectsConfigMap(instance *clv1beta1.Instance) *corev1.ConfigMap {
	return &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "dashboards-saved-objects-config",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"im.cls.sap/datatype": "saved-objects",
			},
		},
	}
}

// BuildDashboardsConfigSecret builds configuration secret - content to be filled by the reconciler
func BuildDashboardsConfigSecret(instance *clv1beta1.Instance) *corev1.Secret {
	return &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "dashboards-config",
			Namespace: instance.Namespace,
		},
	}
}

// GetHeapFromMemory receives a memory in SI format and returns
func GetHeapFromMemory(percent int, memStr string) string {
	minHeap := "512m"

	unit, err := getUnit(memStr)
	if err != nil {
		return minHeap
	}

	u := len(memStr) - 2

	val, err := strconv.ParseFloat(memStr[0:u], 64)
	if err != nil {
		return minHeap
	}

	n := map[string]float64{"m": 1024, "g": 1048576}

	memInKiB := val * n[unit]

	heapSize := math.Round(memInKiB*float64(percent)) / (1024 * 100)

	return fmt.Sprintf("%.0fm", heapSize)
}

func getUnit(m string) (string, error) {
	switch {
	case strings.HasSuffix(m, "Mi"):
		return "m", nil
	case strings.HasSuffix(m, "Gi"):
		return "g", nil
	}
	return "", errors.New("invalid unit")
}
