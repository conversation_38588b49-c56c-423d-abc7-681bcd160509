package builders

import (
	"fmt"
	"strconv"
	"strings"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders/helpers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	semverv3 "github.com/Masterminds/semver/v3"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	policyv1 "k8s.io/api/policy/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/utils/ptr"
)

// BuildOpenSearchStatefulSet creates a StatefulSet for the OpenSearch nodes belonging to the given node pool with
// the given number of replicas.
func BuildOpenSearchStatefulSet(instance *clv1beta1.Instance, nodePool *clv1beta1.NodePool, image string) *appsv1.StatefulSet {
	openSearchImage := helpers.GetImageSpec(image)
	initImage := helpers.GetOpenSearchInitImageSpec()

	stsName := fmt.Sprintf("elastic-%s", nodePool.Name)

	hasClusterManagerRole := utils.Contains(nodePool.Roles, clv1beta1.ClusterManager)
	hasDataRole := utils.Contains(nodePool.Roles, clv1beta1.Data)

	rolesStr := utils.Join(nodePool.Roles, ",")
	imageTag := utils.GetImageTag(openSearchImage.Image)

	version, err := semverv3.NewVersion(imageTag)

	// TODO: Can be removed after 2.x upgrade
	// In case of an error we default to 1.x role name, which is still supported by 2.x
	if err != nil || version.Major() < 2 {
		rolesStr = strings.ReplaceAll(rolesStr, "cluster_manager", "master")
	}

	volumeMounts := []corev1.VolumeMount{
		{
			Name:      "config",
			MountPath: "/usr/share/opensearch/config/opensearch.yml",
			SubPath:   "opensearch.yml",
		},
		{
			Name:      "config",
			MountPath: "/usr/share/opensearch/config/log4j2.properties",
			SubPath:   "log4j2.properties",
		},
		{
			Name:      "transport-tls-secret",
			MountPath: "/usr/share/opensearch/config/tls-transport",
		},
		{
			Name:      "ca-secret",
			MountPath: "/usr/share/opensearch/config/ca",
		},
		{
			Name:      "entry",
			MountPath: "/usr/share/opensearch/entry.sh",
			SubPath:   "entry.sh",
		},
		{
			Name:      "init",
			MountPath: "/usr/share/opensearch/init.sh",
			SubPath:   "init.sh",
		},
		{
			Name:      "initsecurity",
			MountPath: "/usr/share/opensearch/plugins/opensearch-security/securityconfig",
		},
		{
			Name:      "user-secret",
			MountPath: "/usr/share/opensearch/config/user",
		},
	}

	var persistentVolumeClaimRetentionPolicy *appsv1.StatefulSetPersistentVolumeClaimRetentionPolicy
	var volumeClaimTemplates []corev1.PersistentVolumeClaim
	if !nodePool.Persistence.DiskSize.IsZero() {
		volumeClaimTemplates = []corev1.PersistentVolumeClaim{
			{
				ObjectMeta: metav1.ObjectMeta{
					Name: "data",
				},
				Spec: corev1.PersistentVolumeClaimSpec{
					AccessModes: []corev1.PersistentVolumeAccessMode{
						corev1.ReadWriteOnce,
					},
					Resources: corev1.VolumeResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceStorage: nodePool.Persistence.DiskSize,
						},
					},
				},
			},
		}

		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      "data",
			MountPath: "/usr/share/opensearch/data",
		})

		if hasClusterManagerRole {
			persistentVolumeClaimRetentionPolicy = &appsv1.StatefulSetPersistentVolumeClaimRetentionPolicy{
				WhenDeleted: appsv1.RetainPersistentVolumeClaimRetentionPolicyType,
				WhenScaled:  appsv1.RetainPersistentVolumeClaimRetentionPolicyType,
			}
		} else {
			persistentVolumeClaimRetentionPolicy = &appsv1.StatefulSetPersistentVolumeClaimRetentionPolicy{
				WhenDeleted: appsv1.RetainPersistentVolumeClaimRetentionPolicyType,
				WhenScaled:  appsv1.DeletePersistentVolumeClaimRetentionPolicyType,
			}
		}
	}

	var containerLifecycle *corev1.Lifecycle
	if hasClusterManagerRole {
		containerLifecycle = &corev1.Lifecycle{
			PostStart: &corev1.LifecycleHandler{
				Exec: &corev1.ExecAction{
					Command: []string{
						"/bin/bash",
						"-c",
						"echo \"Executing /usr/share/opensearch/init.sh\" >> /usr/share/opensearch/init.log && /usr/share/opensearch/init.sh >> /usr/share/opensearch/init.log",
					},
				},
			},
		}
	}

	return &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:        stsName,
			Namespace:   instance.Namespace,
			Annotations: map[string]string{},
			Labels: map[string]string{
				"app":             "opensearch",
				"pool":            nodePool.Name,
				"cluster-manager": strconv.FormatBool(hasClusterManagerRole),
				"data":            strconv.FormatBool(hasDataRole),
			},
		},
		Spec: appsv1.StatefulSetSpec{
			Replicas:                             ptr.To(nodePool.Autoscaler.MinReplicas), // final value will be set by topologyHelper
			PersistentVolumeClaimRetentionPolicy: persistentVolumeClaimRetentionPolicy,
			PodManagementPolicy:                  appsv1.ParallelPodManagement,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app":  "opensearch",
					"pool": nodePool.Name,
				},
			},
			ServiceName: fmt.Sprintf("opensearch-%s-headless", nodePool.Name),
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{},
					Labels: map[string]string{
						"app":             "opensearch",
						"pool":            nodePool.Name,
						"cluster-manager": strconv.FormatBool(hasClusterManagerRole),
						"data":            strconv.FormatBool(hasDataRole),
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Command: []string{
								"/usr/share/opensearch/entry.sh",
							},
							Env: []corev1.EnvVar{
								{
									Name:  "DISABLE_INSTALL_DEMO_CONFIG",
									Value: "true",
								},
								{
									Name:  "DISCOVERY_SERVICE",
									Value: "opensearch-discovery",
								},
								{
									Name:  "HTTP_ENABLE",
									Value: "false",
								},
								{
									Name: "KUBERNETES_NODE_NAME",
									ValueFrom: &corev1.EnvVarSource{
										FieldRef: &corev1.ObjectFieldSelector{
											APIVersion: "v1",
											FieldPath:  "spec.nodeName",
										},
									},
								},
								{
									Name: "NAMESPACE",
									ValueFrom: &corev1.EnvVarSource{
										FieldRef: &corev1.ObjectFieldSelector{
											APIVersion: "v1",
											FieldPath:  "metadata.namespace",
										},
									},
								},
								{
									Name: "NODE_NAME",
									ValueFrom: &corev1.EnvVarSource{
										FieldRef: &corev1.ObjectFieldSelector{
											APIVersion: "v1",
											FieldPath:  "metadata.name",
										},
									},
								},
								{
									Name:  "NODE_ROLES",
									Value: rolesStr,
								},
								{
									Name:  "OPENSEARCH_JAVA_OPTS",
									Value: nodePool.Jvm,
								},
							},
							Image:           openSearchImage.Image,
							ImagePullPolicy: openSearchImage.ImagePullPolicy,
							Name:            "opensearch",
							Ports: []corev1.ContainerPort{
								{
									ContainerPort: 9200,
									Name:          "http",
									Protocol:      corev1.ProtocolTCP,
								},
								{
									ContainerPort: 9300,
									Name:          "transport",
									Protocol:      corev1.ProtocolTCP,
								},
							},
							Lifecycle: containerLifecycle,
							LivenessProbe: &corev1.Probe{
								FailureThreshold:    3,
								InitialDelaySeconds: 300,
								PeriodSeconds:       30,
								SuccessThreshold:    1,
								ProbeHandler: corev1.ProbeHandler{
									TCPSocket: &corev1.TCPSocketAction{
										Port: intstr.IntOrString{
											Type:   intstr.String,
											StrVal: "transport",
										},
									},
								},
								TimeoutSeconds: 1,
							},
							ReadinessProbe: &corev1.Probe{
								FailureThreshold:    3,
								InitialDelaySeconds: 10,
								PeriodSeconds:       5,
								SuccessThreshold:    1,
								TimeoutSeconds:      5,
								ProbeHandler: corev1.ProbeHandler{
									TCPSocket: &corev1.TCPSocketAction{
										Port: intstr.IntOrString{
											Type:   intstr.String,
											StrVal: "http",
										},
									},
								},
							},
							Resources:    nodePool.Resources,
							VolumeMounts: volumeMounts,
						},
					},
					ImagePullSecrets: openSearchImage.ImagePullSecrets,
					InitContainers: []corev1.Container{
						{
							Command: []string{
								"sysctl",
								"-w",
								"vm.max_map_count=262144",
							},
							Image:           initImage.Image,
							ImagePullPolicy: initImage.ImagePullPolicy,
							Name:            "sysctl",
							SecurityContext: &corev1.SecurityContext{
								Privileged: ptr.To(true),
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyAlways,
					SecurityContext: &corev1.PodSecurityContext{
						FSGroup: ptr.To[int64](1000),
					},
					ServiceAccountName: "opensearch-account",
					TopologySpreadConstraints: []corev1.TopologySpreadConstraint{
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{
									"app":  "opensearch",
									"pool": nodePool.Name,
								},
							},
							MaxSkew:           1,
							TopologyKey:       "topology.kubernetes.io/zone",
							WhenUnsatisfiable: corev1.DoNotSchedule,
						},
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{
									"app":  "opensearch",
									"pool": nodePool.Name,
								},
							},
							MaxSkew:           1,
							TopologyKey:       "kubernetes.io/hostname",
							WhenUnsatisfiable: corev1.ScheduleAnyway,
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "config",
							VolumeSource: corev1.VolumeSource{
								ConfigMap: &corev1.ConfigMapVolumeSource{
									DefaultMode: ptr.To[int32](0o600),
									LocalObjectReference: corev1.LocalObjectReference{
										Name: "opensearch-config",
									},
								},
							},
						},
						{
							Name: "initsecurity",
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									DefaultMode: ptr.To[int32](0o600),
									SecretName:  "opensearch-security",
								},
							},
						},
						{
							Name: "init",
							VolumeSource: corev1.VolumeSource{
								ConfigMap: &corev1.ConfigMapVolumeSource{
									DefaultMode: ptr.To[int32](0o777),
									LocalObjectReference: corev1.LocalObjectReference{
										Name: "opensearch-config",
									},
									Items: []corev1.KeyToPath{
										{
											Key:  "init.sh",
											Path: "init.sh",
										},
									},
								},
							},
						},
						{
							Name: "entry",
							VolumeSource: corev1.VolumeSource{
								ConfigMap: &corev1.ConfigMapVolumeSource{
									DefaultMode: ptr.To[int32](0o777),
									LocalObjectReference: corev1.LocalObjectReference{
										Name: "opensearch-config",
									},
									Items: []corev1.KeyToPath{
										{
											Key:  "entry.sh",
											Path: "entry.sh",
										},
									},
								},
							},
						},
						{
							Name: "transport-tls-secret",
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									DefaultMode: ptr.To[int32](0o600),
									SecretName:  "elasticsearch-transport-tls",
									Items: []corev1.KeyToPath{
										{
											Key:  "tls.crt",
											Path: "crt.pem",
										},
										{
											Key:  "tls.key",
											Path: "key.pem",
										},
									},
								},
							},
						},
						{
							Name: "ca-secret",
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									DefaultMode: ptr.To[int32](0o600),
									SecretName:  "elasticsearch-ca",
									Items: []corev1.KeyToPath{
										{
											Key:  "tls.crt",
											Path: "crt.pem",
										},
									},
								},
							},
						},
						{
							Name: "user-secret",
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									DefaultMode: ptr.To[int32](0o600),
									SecretName:  "elasticsearch-user",
									Items: []corev1.KeyToPath{
										{
											Key:  "tls.crt",
											Path: "admin_crt.pem",
										},
										{
											Key:  "tls.key",
											Path: "admin_key.pem",
										},
									},
								},
							},
						},
					},
				},
			},
			UpdateStrategy: appsv1.StatefulSetUpdateStrategy{
				Type: appsv1.OnDeleteStatefulSetStrategyType,
			},
			VolumeClaimTemplates: volumeClaimTemplates,
		},
	}
}

// BuildOpenSearchPodDisruptionBudget creates a PodDisruptionBudget for the OpenSearch nodes belonging to the given node pool.
func BuildOpenSearchPodDisruptionBudget(instance *clv1beta1.Instance, nodePool *clv1beta1.NodePool) *policyv1.PodDisruptionBudget {
	return &policyv1.PodDisruptionBudget{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("opensearch-%s-pdb", nodePool.Name),
			Namespace: instance.Namespace,
		},
		Spec: policyv1.PodDisruptionBudgetSpec{
			MaxUnavailable: ptr.To(intstr.FromInt32(1)),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app":  "opensearch",
					"pool": nodePool.Name,
				},
			},
		},
	}
}

// BuildOpenSearchHeadlessService creates a headless service for the OpenSearch nodes belonging to the
// given node pool. It is used by the respective statefulset to provide a stable network identity.
func BuildOpenSearchHeadlessService(instance *clv1beta1.Instance, nodePool *clv1beta1.NodePool) *corev1.Service {
	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("opensearch-%s-headless", nodePool.Name),
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app":  "opensearch",
				"pool": nodePool.Name,
			},
		},
		Spec: corev1.ServiceSpec{
			ClusterIP: "None",
			Ports: []corev1.ServicePort{
				{
					Name:       "http",
					Port:       9200,
					Protocol:   corev1.ProtocolTCP,
					TargetPort: intstr.FromInt32(9200),
				},
			},
			PublishNotReadyAddresses: true,
			Selector: map[string]string{
				"app":  "opensearch",
				"pool": nodePool.Name,
			},
			Type: corev1.ServiceTypeClusterIP,
		},
	}
}

// BuildOpenSearchDiscoveryService creates a service for discovering the OpenSearch cluster.
// It targets all OpenSearch nodes that have the cluster-manager-role label set to true.
func BuildOpenSearchDiscoveryService(instance *clv1beta1.Instance) *corev1.Service {
	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "opensearch-discovery",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app":             "opensearch",
				"cluster-manager": "true",
			},
		},
		Spec: corev1.ServiceSpec{
			ClusterIP: "None",
			Ports: []corev1.ServicePort{
				{
					Name:       "transport",
					Port:       9300,
					Protocol:   corev1.ProtocolTCP,
					TargetPort: intstr.FromInt32(9300),
				},
			},
			Selector: map[string]string{
				"app":             "opensearch",
				"cluster-manager": "true",
			},
			Type: corev1.ServiceTypeClusterIP,
			// TODO: TopologyKeys are deprecated, check if replacement is needed
		},
	}
}

// BuildLegacyOpenSearchClientService creates a service for communicating with the OpenSearch cluster.
// It targets all OpenSearch nodes and is used by following components:
// - Fluentd and Data Prepper for Ingestion
// - CLOE for various operations
// - Instance Maintainer for various operations
// - OpenSearch Dashboards
// - OpenSearch backend ingress
// TODO Remove once all dependent components are updated to use the new service
func BuildLegacyOpenSearchClientService(instance *clv1beta1.Instance) *corev1.Service {
	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "elastic-client", // Legacy name required for IM, OSD, Fluentd and Dataprepper
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app": "opensearch",
			},
		},
		Spec: corev1.ServiceSpec{
			Ports: []corev1.ServicePort{
				{
					Name:       "http",
					Port:       9200,
					Protocol:   corev1.ProtocolTCP,
					TargetPort: intstr.FromInt32(9200),
				},
			},
			Selector: map[string]string{
				"app": "opensearch",
			},
			Type: corev1.ServiceTypeClusterIP,
		},
	}
}

// BuildOpenSearchClientService creates a service for communicating with the OpenSearch cluster.
// It targets all OpenSearch nodes and WILL be used by following components:
// - Fluentd and Data Prepper for Ingestion
// - CLOE for various operations
// - Instance Maintainer for various operations
// - OpenSearch Dashboards
// - OpenSearch backend ingress
func BuildOpenSearchClientService(instance *clv1beta1.Instance) *corev1.Service {
	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "opensearch-client",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app": "opensearch",
			},
		},
		Spec: corev1.ServiceSpec{
			Ports: []corev1.ServicePort{
				{
					Name:       "http",
					Port:       9200,
					Protocol:   corev1.ProtocolTCP,
					TargetPort: intstr.FromInt32(9200),
				},
			},
			Selector: map[string]string{
				"app": "opensearch",
			},
			Type: corev1.ServiceTypeClusterIP,
		},
	}
}

// BuildOpenSearchConfigMap creates a ConfigMap for the OpenSearch configuration.
// The actual content needs to be provided by the reconciler.
func BuildOpenSearchConfigMap(instance *clv1beta1.Instance) *corev1.ConfigMap {
	return &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "opensearch-config",
			Namespace: instance.Namespace,
		},
	}
}

// BuildOpenSearchSecuritySecret creates a Secret for the OpenSearch Security.
// The actual content needs to be provided by the reconciler.
func BuildOpenSearchSecuritySecret(instance *clv1beta1.Instance) *corev1.Secret {
	return &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "opensearch-security",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"im.cls.sap/datatype": "access-control",
			},
		},
	}
}

// BuildOpenSearchServiceAccount creates a service account for the OpenSearch nodes.
func BuildOpenSearchServiceAccount(instance *clv1beta1.Instance) *corev1.ServiceAccount {
	return &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "opensearch-account",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app": "opensearch",
			},
		},
	}
}

// BuildOpenSearchClusterRole creates a cluster role for the OpenSearch nodes.
func BuildOpenSearchClusterRole() *rbacv1.ClusterRole {
	return &rbacv1.ClusterRole{
		ObjectMeta: metav1.ObjectMeta{
			Name: "opensearch-clusterrole",
			Labels: map[string]string{
				"app": "opensearch",
			},
		},
		Rules: []rbacv1.PolicyRule{
			{
				APIGroups: []string{""},
				Resources: []string{"nodes"},
				Verbs:     []string{"get", "list", "watch"},
			},
		},
	}
}

// BuildOpenSearchClusterRoleBinding creates a cluster role binding for the OpenSearch nodes.
func BuildOpenSearchClusterRoleBinding(instance *clv1beta1.Instance) *rbacv1.ClusterRoleBinding {
	return &rbacv1.ClusterRoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name: fmt.Sprintf("opensearch-clusterrolebinding-%s", instance.Namespace),
			Labels: map[string]string{
				"app": "opensearch",
			},
		},
		RoleRef: rbacv1.RoleRef{
			APIGroup: "rbac.authorization.k8s.io",
			Kind:     "ClusterRole",
			Name:     "opensearch-clusterrole",
		},
		Subjects: []rbacv1.Subject{
			{
				Kind:      "ServiceAccount",
				Name:      "opensearch-account",
				Namespace: instance.Namespace,
			},
		},
	}
}

// BuildOpenSearchIngress creates an Ingress for the OpenSearch nodes.
func BuildOpenSearchIngress(instance *clv1beta1.Instance) *networkingv1.Ingress {
	cfg := config.GetInstance()
	return &networkingv1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "opensearch-ingress",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"app": "opensearch",
			},
			Annotations: map[string]string{
				"nginx.ingress.kubernetes.io/proxy-buffer-size": "32m",
				"nginx.ingress.kubernetes.io/proxy-body-size":   "4m",
				"nginx.ingress.kubernetes.io/backend-protocol":  "HTTPS",
				"nginx.ingress.kubernetes.io/server-alias":      helpers.GetOpenSearchHostAlias(instance, cfg.GetIngress()),
			},
		},
		Spec: networkingv1.IngressSpec{
			IngressClassName: ptr.To("nginx"),
			Rules: []networkingv1.IngressRule{
				{
					Host: helpers.GetOpenSearchHost(instance, cfg.GetIngress()),
					IngressRuleValue: networkingv1.IngressRuleValue{
						HTTP: &networkingv1.HTTPIngressRuleValue{
							Paths: []networkingv1.HTTPIngressPath{
								{
									Path:     "/",
									PathType: ptr.To(networkingv1.PathTypePrefix),
									Backend: networkingv1.IngressBackend{
										Service: &networkingv1.IngressServiceBackend{
											Name: "opensearch-client",
											Port: networkingv1.ServiceBackendPort{
												Number: 9200,
											},
										},
									},
								},
							},
						},
					},
				},
			},
			TLS: []networkingv1.IngressTLS{
				{
					Hosts: []string{
						helpers.GetOpenSearchHost(instance, cfg.GetIngress()),
						helpers.GetOpenSearchHostAlias(instance, cfg.GetIngress()),
					},
				},
			},
		},
	}
}

// BuildOpenSearchComposableTemplatesConfigMap creates a ConfigMap for the OpenSearch composable templates.
// The actual content needs to be provided by the reconciler.
func BuildOpenSearchComposableTemplatesConfigMap(instance *clv1beta1.Instance) *corev1.ConfigMap {
	return &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "opensearch-composable-templates-config",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"im.cls.sap/datatype": "composable-templates",
			},
		},
	}
}

// BuildOpenSearchISMPoliciesConfigMap creates a ConfigMap for the OpenSearch ISM policies.
// The actual content needs to be provided by the reconciler.
func BuildOpenSearchISMPoliciesConfigMap(instance *clv1beta1.Instance) *corev1.ConfigMap {
	return &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "opensearch-ism-policies-config",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"im.cls.sap/datatype": "ism-policies",
			},
		},
	}
}
