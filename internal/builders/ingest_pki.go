package builders

import (
	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

func BuildActiveRootCANamespaceName(instance *clv1beta1.Instance) types.NamespacedName {
	return types.NamespacedName{
		Name:      "root-ca-active",
		Namespace: instance.Namespace,
	}
}

func BuildActiveRootCASecret(instance *clv1beta1.Instance, data map[string][]byte) *corev1.Secret {
	return &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "root-ca-active",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"x509-certificate-exporter": "scrape",
			},
		},
		Type: corev1.SecretTypeTLS,
		Data: data,
	}
}

func BuildOldRootCANamespaceName(instance *clv1beta1.Instance) types.NamespacedName {
	return types.NamespacedName{
		Name:      "root-ca-old",
		Namespace: instance.Namespace,
	}
}

func BuildOldRootCASecret(instance *clv1beta1.Instance, data map[string][]byte) *corev1.Secret {
	return &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "root-ca-old",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"x509-certificate-exporter": "scrape",
			},
		},
		Type: corev1.SecretTypeTLS,
		Data: data,
	}
}

func BuildCAStoreNamespaceName(instance *clv1beta1.Instance) types.NamespacedName {
	return types.NamespacedName{
		Name:      "ca-store",
		Namespace: instance.Namespace,
	}
}

func BuildCAStoreSecret(instance *clv1beta1.Instance, data map[string][]byte) *corev1.Secret {
	return &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "ca-store",
			Namespace: instance.Namespace,
			Labels: map[string]string{
				"x509-certificate-exporter": "scrape",
			},
		},
		Type: corev1.SecretTypeOpaque,
		Data: data,
	}
}
