/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller_test

import (
	"context"
	"errors"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/controller"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"

	"github.com/prometheus/client_golang/prometheus"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("InstanceReconciler", func() {
	const (
		instanceName = "cls-test"
		namespace    = "default"
	)

	var (
		instance               *clv1beta1.Instance
		conditionComponent1    metav1.Condition
		conditionComponent2    metav1.Condition
		ctx                    = context.Background()
		instanceNamespacedName = types.NamespacedName{
			Name:      instanceName,
			Namespace: namespace,
		}
		componentReconciler1 *fake.ComponentReconcilerMock
		componentReconciler2 *fake.ComponentReconcilerMock
		instanceReconciler   *controller.InstanceReconciler
		conditionHelper      = conditions.NewConditionHelper()
	)

	BeforeEach(func() {
		conditionComponent1 = metav1.Condition{
			Type:               "component1/test",
			Status:             metav1.ConditionTrue,
			Reason:             "reason",
			Message:            "message",
			LastTransitionTime: metav1.Time{Time: time.Now()},
		}
		conditionComponent2 = metav1.Condition{
			Type:               "component2/test",
			Status:             metav1.ConditionTrue,
			Reason:             "reason",
			Message:            "message",
			LastTransitionTime: metav1.Time{Time: time.Now()},
		}

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      instanceName,
				Namespace: namespace,
			},
			Spec: clv1beta1.InstanceSpec{
				OpenSearch: clv1beta1.OpenSearch{
					Cluster: clv1beta1.Cluster{
						NodePools: []clv1beta1.NodePool{
							{
								Roles: []clv1beta1.NodeRole{"data"},
							},
						},
					},
				},
			},
		}

		componentReconciler1 = fake.NewComponentReconcilerMock([]string{conditionComponent1.Type})
		componentReconciler2 = fake.NewComponentReconcilerMock([]string{conditionComponent2.Type})

		componentReconcilers := []reconcilers.ComponentReconciler{componentReconciler1, componentReconciler2}
		instanceReconciler = controller.NewInstanceReconciler(k8sClient, k8sClient.Scheme(), prometheus.NewRegistry(), componentReconcilers)

		conditionHelper.SetCondition(instance, conditionComponent1)
		conditionHelper.SetCondition(instance, conditionComponent2)
	})

	AfterEach(func() {
		deletedInstance := &clv1beta1.Instance{}
		err := k8sClient.Get(ctx, instanceNamespacedName, deletedInstance)
		if err == nil {
			By("deleting the instance CR")
			Expect(k8sClient.Delete(ctx, deletedInstance)).To(Succeed())
			err = k8sClient.Get(ctx, instanceNamespacedName, deletedInstance)
			Expect(err).ToNot(HaveOccurred())
			deletedInstance.Finalizers = nil
			Expect(k8sClient.Update(ctx, deletedInstance)).To(Succeed())
		}
	})

	Context("Reconcile", func() {
		When("no instance CR exists", func() {
			It("should return no error and empty controller result", func() {
				result, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
					NamespacedName: instanceNamespacedName,
				})
				Expect(err).NotTo(HaveOccurred())
				Expect(result).To(Equal(ctrl.Result{}))
			})
		})

		When("an instance CR is created", func() {
			BeforeEach(func() {
				Expect(apierrors.IsNotFound(k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{}))).To(BeTrue())
				By("creating the instance CustomResource")
				Expect(k8sClient.Create(ctx, instance)).To(Succeed())

				// set instance status (subresource) value
				instance.Status = clv1beta1.InstanceStatus{
					Conditions:      []metav1.Condition{conditionComponent1, conditionComponent2},
					LifecycleStatus: clv1beta1.InProgress,
				}
				Expect(k8sClient.Status().Update(ctx, instance)).To(Succeed())
			})

			When("the status of all conditions is true", func() {
				It("should return no error, requeue, set a finalizer, and set the status to 'up'", func() {
					result, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(ctrl.Result{
						Requeue:      true,
						RequeueAfter: config.GetInstance().GetInstanceReconciler().RequeueDelay,
					}))

					updatedInstance := &clv1beta1.Instance{}
					err = k8sClient.Get(ctx, instanceNamespacedName, updatedInstance)
					Expect(err).NotTo(HaveOccurred())
					Expect(updatedInstance.ObjectMeta.Finalizers).To(ConsistOf(controller.InstanceFinalizer))
					Expect(updatedInstance.Status.HealthStatus).To(Equal(clv1beta1.Up))
					Expect(updatedInstance.Status.LifecycleStatus).To(Equal(clv1beta1.Succeeded))
				})

				It("should call Reconcile method of the component reconcilers", func() {
					_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})

					Expect(err).NotTo(HaveOccurred())
					Expect(componentReconciler1.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler1.IsReconcileCalled).To(BeTrue())
					Expect(componentReconciler2.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler2.IsReconcileCalled).To(BeTrue())
				})

				It("should set the lifecycle status to succeeded even after reconciling again", func() {
					_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})
					Expect(err).NotTo(HaveOccurred())

					_, err = instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})
					Expect(err).NotTo(HaveOccurred())

					updatedInstance := &clv1beta1.Instance{}
					err = k8sClient.Get(ctx, instanceNamespacedName, updatedInstance)
					Expect(err).NotTo(HaveOccurred())
					Expect(updatedInstance.Status.LifecycleStatus).To(Equal(clv1beta1.Succeeded))
				})
			})

			When("the status of one condition is false", func() {
				BeforeEach(func() {
					conditionComponent1.Status = metav1.ConditionFalse
					conditionHelper.SetCondition(instance, conditionComponent1)
					Expect(k8sClient.Status().Update(ctx, instance)).To(Succeed())
				})

				It("should return no error, requeue, set a finalizer, set the instance condition health status to 'down'", func() {
					result, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})
					Expect(err).NotTo(HaveOccurred())
					Expect(result).To(Equal(ctrl.Result{
						Requeue:      true,
						RequeueAfter: config.GetInstance().GetInstanceReconciler().RequeueDelay,
					}))

					updatedInstance := &clv1beta1.Instance{}
					err = k8sClient.Get(ctx, instanceNamespacedName, updatedInstance)
					Expect(err).NotTo(HaveOccurred())
					Expect(updatedInstance.ObjectMeta.Finalizers).To(ConsistOf(controller.InstanceFinalizer))
					Expect(updatedInstance.Status.HealthStatus).To(Equal(clv1beta1.Down))
					Expect(updatedInstance.Status.LifecycleStatus).To(Equal(clv1beta1.InProgress))
				})

				It("should call Reconcile method of the component reconcilers", func() {
					_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})

					Expect(err).NotTo(HaveOccurred())
					Expect(componentReconciler1.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler1.IsReconcileCalled).To(BeTrue())
					Expect(componentReconciler2.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler2.IsReconcileCalled).To(BeTrue())
				})
			})

			When("one of the component reconcilers has an error", func() {
				BeforeEach(func() {
					componentReconciler1.PrepareReconcile(ctrl.Result{}, errors.New("errFromComponent1"))
					componentReconciler2.PrepareReconcile(ctrl.Result{RequeueAfter: time.Minute}, nil)
				})

				It("should return no error, requeue, set a finalizer, set the instance condition health status to 'down'", func() {
					result, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})
					Expect(err).To(Equal(errors.New("errFromComponent1")))
					Expect(result).To(Equal(ctrl.Result{}))

					updatedInstance := &clv1beta1.Instance{}
					err = k8sClient.Get(ctx, instanceNamespacedName, updatedInstance)
					Expect(err).NotTo(HaveOccurred())
					Expect(updatedInstance.ObjectMeta.Finalizers).To(ConsistOf(controller.InstanceFinalizer))
					Expect(updatedInstance.Status.HealthStatus).To(Equal(clv1beta1.Down))
					Expect(updatedInstance.Status.LifecycleStatus).To(Equal(clv1beta1.InProgress))
				})

				It("should call Reconcile method of the first component reconcilers only", func() {
					_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})

					Expect(err).To(HaveOccurred())
					Expect(componentReconciler1.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler1.IsReconcileCalled).To(BeTrue())
					Expect(componentReconciler2.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler2.IsReconcileCalled).To(BeFalse())
				})
			})

			When("one of the component reconcilers returned a non-zero result", func() {
				BeforeEach(func() {
					componentReconciler1.PrepareReconcile(ctrl.Result{RequeueAfter: time.Minute}, nil)
					componentReconciler2.PrepareReconcile(ctrl.Result{}, nil)
				})

				It("should return no error, requeue accordingly and set the health status to 'up'", func() {
					result, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})
					Expect(err).ToNot(HaveOccurred())
					Expect(result).To(Equal(ctrl.Result{
						Requeue:      true,
						RequeueAfter: time.Minute,
					}))

					updatedInstance := &clv1beta1.Instance{}
					err = k8sClient.Get(ctx, instanceNamespacedName, updatedInstance)
					Expect(err).ToNot(HaveOccurred())
					Expect(updatedInstance.ObjectMeta.Finalizers).To(ConsistOf(controller.InstanceFinalizer))
					Expect(updatedInstance.Status.HealthStatus).To(Equal(clv1beta1.Up))
					Expect(updatedInstance.Status.LifecycleStatus).To(Equal(clv1beta1.Succeeded))
				})

				It("should call Reconcile method of all component reconcilers", func() {
					_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})

					Expect(err).ToNot(HaveOccurred())
					Expect(componentReconciler1.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler1.IsReconcileCalled).To(BeTrue())
					Expect(componentReconciler2.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler2.IsReconcileCalled).To(BeTrue())
				})
			})

			When("skip reconciliation is set", func() {
				BeforeEach(func() {
					// set skip reconcile timestamp to a future value
					instance.Spec.SkipReconciliationUntil = time.Now().Add(time.Hour).Format(time.RFC3339) // 1 hour in the future
					Expect(k8sClient.Update(ctx, instance)).To(Succeed())
				})

				It("should not call Reconcile method of all component reconcilers", func() {
					_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})

					Expect(err).ToNot(HaveOccurred())
					Expect(componentReconciler1.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler1.IsReconcileCalled).To(BeFalse())
					Expect(componentReconciler2.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler2.IsReconcileCalled).To(BeFalse())
				})
			})
		})

		When("an instance CR is deleted", func() {
			BeforeEach(func() {
				Expect(apierrors.IsNotFound(k8sClient.Get(ctx, instanceNamespacedName, &clv1beta1.Instance{}))).To(BeTrue())
				By("creating the instance CustomResource")
				Expect(k8sClient.Create(ctx, instance)).To(Succeed())

				// set instance status (subresource) value
				instance.Status = clv1beta1.InstanceStatus{
					Conditions: []metav1.Condition{conditionComponent1, conditionComponent2},
				}
				Expect(k8sClient.Status().Update(ctx, instance)).To(Succeed())

				// reconcile the instance once to update statuses and add finalizer
				_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
					NamespacedName: instanceNamespacedName,
				})
				Expect(err).ToNot(HaveOccurred())

				// trigger a deletion of the instance
				By("deleting the instance CustomResource")
				Expect(k8sClient.Delete(ctx, instance)).To(Succeed())

				// reset the mocks to track calls to CleanUp and Reconcile
				componentReconciler1.IsCleanUpCalled = false
				componentReconciler1.IsReconcileCalled = false
				componentReconciler2.IsCleanUpCalled = false
				componentReconciler2.IsReconcileCalled = false
			})

			When("the lifecycle status is not yet 'InDeletion'", func() {
				It("should return no error or requeue and set the lifecycle status to 'InDeletion'", func() {
					result, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})
					Expect(err).NotTo(HaveOccurred())
					Expect(result.IsZero()).To(BeTrue())

					updatedInstance := &clv1beta1.Instance{}
					err = k8sClient.Get(ctx, instanceNamespacedName, updatedInstance)
					Expect(err).NotTo(HaveOccurred())
					Expect(updatedInstance.Status.LifecycleStatus).To(Equal(clv1beta1.InDeletion))
				})

				It("should call not call any CleanUp or Reconcile methods", func() {
					_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
						NamespacedName: instanceNamespacedName,
					})

					Expect(err).NotTo(HaveOccurred())
					Expect(componentReconciler1.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler1.IsReconcileCalled).To(BeFalse())
					Expect(componentReconciler2.IsCleanUpCalled).To(BeFalse())
					Expect(componentReconciler2.IsReconcileCalled).To(BeFalse())
				})
			})

			When("the lifecycle status is 'InDeletion'", func() {
				BeforeEach(func() {
					Expect(k8sClient.Get(ctx, instanceNamespacedName, instance)).ToNot(HaveOccurred())
					instance.Status.LifecycleStatus = clv1beta1.InDeletion
					Expect(k8sClient.Status().Update(ctx, instance)).To(Succeed())
				})

				When("none of the component reconcilers has an error", func() {
					It("should return no error, no requeue and removed the finalizer", func() {
						result, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
							NamespacedName: instanceNamespacedName,
						})
						Expect(err).NotTo(HaveOccurred())
						Expect(result).To(Equal(ctrl.Result{}))

						deletedInstance := &clv1beta1.Instance{}
						err = k8sClient.Get(ctx, instanceNamespacedName, deletedInstance)
						Expect(err).To(HaveOccurred())
						Expect(apierrors.IsNotFound(err)).To(BeTrue())
					})

					It("should call CleanUp method of the component reconcilers", func() {
						_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
							NamespacedName: instanceNamespacedName,
						})

						Expect(err).NotTo(HaveOccurred())
						Expect(componentReconciler1.IsCleanUpCalled).To(BeTrue())
						Expect(componentReconciler1.IsReconcileCalled).To(BeFalse())
						Expect(componentReconciler2.IsCleanUpCalled).To(BeTrue())
						Expect(componentReconciler2.IsReconcileCalled).To(BeFalse())
					})
				})

				When("one of the component reconcilers has an error", func() {
					BeforeEach(func() {
						componentReconciler1.PrepareCleanUp(reconcile.Result{}, errors.New("errFromComponent1"))
						componentReconciler2.PrepareCleanUp(reconcile.Result{}, nil)
					})

					It("should return an error and not remove the finalizer", func() {
						result, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
							NamespacedName: instanceNamespacedName,
						})
						Expect(err).To(Equal(errors.New("errFromComponent1")))
						Expect(result).To(Equal(ctrl.Result{}))

						updatedInstance := &clv1beta1.Instance{}
						err = k8sClient.Get(ctx, instanceNamespacedName, updatedInstance)
						Expect(err).NotTo(HaveOccurred())
						Expect(updatedInstance.ObjectMeta.Finalizers).To(ConsistOf(controller.InstanceFinalizer))
					})

					It("should call CleanUp methods of the component reconcilers despite the error", func() {
						_, err := instanceReconciler.Reconcile(ctx, reconcile.Request{
							NamespacedName: instanceNamespacedName,
						})

						Expect(err).To(HaveOccurred())
						Expect(componentReconciler1.IsCleanUpCalled).To(BeTrue())
						Expect(componentReconciler1.IsReconcileCalled).To(BeFalse())
						Expect(componentReconciler2.IsCleanUpCalled).To(BeTrue())
						Expect(componentReconciler2.IsReconcileCalled).To(BeFalse())
					})
				})
			})
		})
	})
})
