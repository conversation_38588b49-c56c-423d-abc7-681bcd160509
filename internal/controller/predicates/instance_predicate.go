package predicates

import (
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/predicate"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
)

// GetInstancePredicate returns a predicate for Instance events.
// Besides creation and deletion events, we only care about specific update events,
// e.g. when the spec changes or when the status/conditions change.
func GetInstancePredicate() predicate.Predicate {
	return predicate.Funcs{
		UpdateFunc: func(e event.UpdateEvent) bool {
			oldInstance, okOld := e.ObjectOld.(*clv1beta1.Instance)
			newInstance, okNew := e.ObjectNew.(*clv1beta1.Instance)

			if okOld && okNew {
				// Check if Generation has changed -> spec has changed
				if newInstance.Generation != oldInstance.Generation {
					return true
				}

				// Check if LifecycleStatus has changed (e.g. by Interoperator)
				if newInstance.Status.LifecycleStatus != oldInstance.Status.LifecycleStatus {
					return true
				}

				// Check if the number of conditions has changed
				if len(newInstance.Status.Conditions) != len(oldInstance.Status.Conditions) {
					return true
				}

				// Check if any of the conditions have changed
				for i, condition := range newInstance.Status.Conditions {
					if condition.Type != oldInstance.Status.Conditions[i].Type {
						return true
					}
					if condition.Status != oldInstance.Status.Conditions[i].Status {
						return true
					}
					if condition.Reason != oldInstance.Status.Conditions[i].Reason {
						return true
					}
					// We don't check the message field to avoid unnecessary reconciliations
				}
			}

			return false
		},
		CreateFunc: func(event.CreateEvent) bool {
			return true
		},
		DeleteFunc: func(event.DeleteEvent) bool {
			return true
		},
		GenericFunc: func(event.GenericEvent) bool {
			return false
		},
	}
}
