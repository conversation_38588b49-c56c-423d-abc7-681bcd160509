package predicates

import (
	appsv1 "k8s.io/api/apps/v1"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
)

// GetStatefulSetPredicate returns a predicate for StatefulSet events.
// We only care about creation, deletion and specific status changes.
func GetStatefulSetPredicate() predicate.Predicate {
	return predicate.Funcs{
		UpdateFunc: func(e event.UpdateEvent) bool {
			newSts, okNew := e.ObjectNew.(*appsv1.StatefulSet)
			oldSts, okOld := e.ObjectOld.(*appsv1.StatefulSet)

			if okNew && okOld {
				return newSts.Status.AvailableReplicas != oldSts.Status.AvailableReplicas ||
					newSts.Status.ReadyReplicas != oldSts.Status.ReadyReplicas ||
					newSts.Status.UpdatedReplicas != oldSts.Status.UpdatedReplicas
			}

			return false
		},
		CreateFunc: func(event.CreateEvent) bool {
			return true
		},
		DeleteFunc: func(event.DeleteEvent) bool {
			return true
		},
		GenericFunc: func(event.GenericEvent) bool {
			return false
		},
	}
}
