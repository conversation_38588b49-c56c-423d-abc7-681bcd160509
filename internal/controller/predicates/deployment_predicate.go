package predicates

import (
	appsv1 "k8s.io/api/apps/v1"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
)

// GetDeploymentPredicate returns a predicate for Deployment events.
// We only care about creation, deletion and specific status changes.
func GetDeploymentPredicate() predicate.Predicate {
	return predicate.Funcs{
		UpdateFunc: func(e event.UpdateEvent) bool {
			newDeployment, okNew := e.ObjectNew.(*appsv1.Deployment)
			oldDeployment, okOld := e.ObjectOld.(*appsv1.Deployment)

			if okNew && okOld {
				return newDeployment.Status.AvailableReplicas != oldDeployment.Status.AvailableReplicas ||
					newDeployment.Status.ReadyReplicas != oldDeployment.Status.ReadyReplicas ||
					newDeployment.Status.UpdatedReplicas != oldDeployment.Status.UpdatedReplicas
			}

			return false
		},
		CreateFunc: func(event.CreateEvent) bool {
			return true
		},
		DeleteFunc: func(event.DeleteEvent) bool {
			return true
		},
		GenericFunc: func(event.GenericEvent) bool {
			return false
		},
	}
}
