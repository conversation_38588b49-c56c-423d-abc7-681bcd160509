package helpers

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"

	"github.com/prometheus/client_golang/prometheus"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

// CustomMapper maps event objects to custom reconcile requests
type CustomMapper interface {
	// Map maps the event object to a custom reconcile request
	Map(ctx context.Context, obj client.Object) []reconcile.Request
}

// MetricsHelper sets a set of prometheus metrics based on instance CRs
type MetricsHelper interface {
	// Register registers the metrics with the given prometheus registry
	Register(registry prometheus.Registerer)

	// Update updates the metrics based on the given instance status
	Update(instance *clv1beta1.Instance)

	// MarkAsDeleted sets the lifecycle status metrics of the instance to deleted
	MarkAsDeleted(namespacedName types.NamespacedName)
}
