package helpers

import (
	"strconv"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"

	semverv3 "github.com/Masterminds/semver/v3"
	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

type metricsHelper struct {
	logger                         logr.Logger
	conditionHelper                conditions.ConditionHelper
	instanceHealthStatusGauge      *prometheus.GaugeVec
	instanceLifecycleStatusGauge   *prometheus.GaugeVec
	instancePlanVersionGauge       *prometheus.GaugeVec
	instanceOSV2UpgradeStatusGauge *prometheus.GaugeVec
}

const (
	instancePlanVersionLabel  = "plan_version"
	healthStatusDown          = 0
	healthStatusUp            = 1
	healthStatusNotApplicable = 2
	lifecycleStatusInProgress = 0
	lifecycleStatusSucceeded  = 1
	lifecycleStatusFailed     = 2
	lifecycleStatusInDeletion = 3
	lifecycleStatusDeleted    = 4

	osUpgradeTooLongThreshold = 90 * time.Minute
)

func NewMetricsHelper(conditionHelper conditions.ConditionHelper) MetricsHelper {
	return &metricsHelper{
		conditionHelper: conditionHelper,
		logger:          log.Log.WithName("metrics-helper"),
		instanceHealthStatusGauge: prometheus.NewGaugeVec(prometheus.GaugeOpts{
			Name: "instance_health_status",
			Help: "Health status of instances",
		}, []string{"name", "namespace"}),
		instanceLifecycleStatusGauge: prometheus.NewGaugeVec(prometheus.GaugeOpts{
			Name: "instance_lifecycle_status",
			Help: "Lifecycle status of instances",
		}, []string{"name", "namespace"}),
		instancePlanVersionGauge: prometheus.NewGaugeVec(prometheus.GaugeOpts{
			Name: "instance_plan_version",
			Help: "Instance plan version for tracking plan updates",
		}, []string{"name", "namespace"}),
		instanceOSV2UpgradeStatusGauge: prometheus.NewGaugeVec(prometheus.GaugeOpts{
			Name: "instance_os_v2_upgrade_status",
			Help: "Status of OS V2 upgrades",
		}, []string{"name", "namespace"}),
	}
}

// Register registers the metrics with the given prometheus registry
func (h *metricsHelper) Register(registry prometheus.Registerer) {
	registry.MustRegister(h.instanceHealthStatusGauge)
	registry.MustRegister(h.instanceLifecycleStatusGauge)
	registry.MustRegister(h.instancePlanVersionGauge)
	registry.MustRegister(h.instanceOSV2UpgradeStatusGauge)
}

// Update updates the metrics based on the given instance status
func (h *metricsHelper) Update(instance *clv1beta1.Instance) {
	switch instance.Status.HealthStatus {
	case clv1beta1.Down:
		h.instanceHealthStatusGauge.WithLabelValues(instance.Name, instance.Namespace).Set(healthStatusDown)
	case clv1beta1.Up:
		h.instanceHealthStatusGauge.WithLabelValues(instance.Name, instance.Namespace).Set(healthStatusUp)
	}

	switch instance.Status.LifecycleStatus {
	case clv1beta1.InProgress:
		h.instanceLifecycleStatusGauge.WithLabelValues(instance.Name, instance.Namespace).Set(lifecycleStatusInProgress)
	case clv1beta1.Succeeded:
		h.instanceLifecycleStatusGauge.WithLabelValues(instance.Name, instance.Namespace).Set(lifecycleStatusSucceeded)
	case clv1beta1.Failed:
		h.instanceLifecycleStatusGauge.WithLabelValues(instance.Name, instance.Namespace).Set(lifecycleStatusFailed)
	case clv1beta1.InDeletion:
		h.instanceLifecycleStatusGauge.WithLabelValues(instance.Name, instance.Namespace).Set(lifecycleStatusInDeletion)
	}

	var planVersion int
	var err error
	if planVersionStr, ok := instance.GetLabels()[instancePlanVersionLabel]; ok {
		planVersion, err = strconv.Atoi(planVersionStr)
		if err != nil {
			h.logger.Error(err, "Failed to parse plan version from instance labels", "instance", instance.Name, "planVersion", planVersionStr)
		}
	}
	h.instancePlanVersionGauge.WithLabelValues(instance.Name, instance.Namespace).Set(float64(planVersion))

	// use conditions.OpenSearchUpgraded and target version to determine OS 2.X upgrade state
	targetVersion, err := semverv3.NewVersion(instance.Status.OpenSearchClusterTargetVersion)
	if err != nil {
		h.logger.Info("Failed to parse target version from instance status", "instance", instance.Name, "error", err.Error())
	}

	upgradeCondition := h.conditionHelper.GetCondition(instance, conditions.OpenSearchUpgraded)

	upgradeStatus := 4 // Default: not relevant

	if targetVersion != nil {
		switch {
		case upgradeCondition.Status == metav1.ConditionFalse && targetVersion.Major() == 2:
			if time.Since(upgradeCondition.LastTransitionTime.Time) > osUpgradeTooLongThreshold {
				upgradeStatus = 3 // Very long major upgrade (potential issues)
			} else {
				upgradeStatus = 2 // Major upgrade in progress
			}
		case upgradeCondition.Status == metav1.ConditionTrue:
			if targetVersion.Major() == 2 {
				upgradeStatus = 1 // Successful upgrade to version 2
			} else {
				upgradeStatus = 0 // Still on version 1, no major upgrade
			}
		}
	}

	h.instanceOSV2UpgradeStatusGauge.WithLabelValues(instance.Name, instance.Namespace).Set(float64(upgradeStatus))
}

// MarkAsDeleted sets the lifecycle status metric to deleted
// This needs to be done separately as the instance is already deleted when this method is called
func (h *metricsHelper) MarkAsDeleted(namespacedName types.NamespacedName) {
	h.instanceLifecycleStatusGauge.WithLabelValues(namespacedName.Name, namespacedName.Namespace).Set(lifecycleStatusDeleted)
	h.instanceHealthStatusGauge.WithLabelValues(namespacedName.Name, namespacedName.Namespace).Set(healthStatusNotApplicable)
}
