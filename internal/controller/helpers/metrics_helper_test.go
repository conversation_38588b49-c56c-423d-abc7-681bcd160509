package helpers_test

import (
	"fmt"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/controller/helpers"

	"github.com/prometheus/client_golang/prometheus"
	io_prometheus_client "github.com/prometheus/client_model/go"
	"google.golang.org/protobuf/proto"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("MetricsHelper", func() {
	var (
		metricsHelper   helpers.MetricsHelper
		conditionHelper conditions.ConditionHelper
		registry        *prometheus.Registry
		instance        *clv1beta1.Instance
	)

	BeforeEach(func() {
		conditionHelper = conditions.NewConditionHelper()
		metricsHelper = helpers.NewMetricsHelper(conditionHelper)
	})

	Describe("NewMetricsHelper", func() {
		It("should create a new metrics helper", func() {
			Expect(metricsHelper).NotTo(BeNil())
		})
	})

	Describe("Update", func() {
		BeforeEach(func() {
			registry = prometheus.NewRegistry()
			metricsHelper.Register(registry)
			instance = &clv1beta1.Instance{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-instance",
					Namespace: "test-namespace",
					Labels: map[string]string{
						"plan_version": "42",
					},
				},
				Status: clv1beta1.InstanceStatus{
					HealthStatus:    clv1beta1.Up,
					LifecycleStatus: clv1beta1.Succeeded,
				},
			}
		})

		Context("Test instance_health_status metric", func() {
			It("should assign the correct labels", func() {
				metricsHelper.Update(instance)
				metric, err := getMetric(registry, "instance_health_status")
				Expect(err).NotTo(HaveOccurred())
				Expect(metric.GetLabel()).To(ConsistOf(
					&io_prometheus_client.LabelPair{Name: proto.String("name"), Value: proto.String("test-instance")},
					&io_prometheus_client.LabelPair{Name: proto.String("namespace"), Value: proto.String("test-namespace")},
				))
			})

			When("the instance health status is Up", func() {
				BeforeEach(func() {
					instance.Status.HealthStatus = clv1beta1.Up
				})

				It("should set the correct value for the instance_health_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_health_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(1)))
				})
			})

			When("the instance health status is Down", func() {
				BeforeEach(func() {
					instance.Status.HealthStatus = clv1beta1.Down
				})

				It("should set the correct value for the instance_health_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_health_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(0)))
				})
			})
		})

		Context("Test instance_lifecycle_status metrics", func() {
			It("should assign the correct labels", func() {
				metricsHelper.Update(instance)
				metric, err := getMetric(registry, "instance_lifecycle_status")
				Expect(err).NotTo(HaveOccurred())
				Expect(metric.GetLabel()).To(ConsistOf(
					&io_prometheus_client.LabelPair{Name: proto.String("name"), Value: proto.String("test-instance")},
					&io_prometheus_client.LabelPair{Name: proto.String("namespace"), Value: proto.String("test-namespace")},
				))
			})

			When("the instance lifecycle status is InProgress", func() {
				BeforeEach(func() {
					instance.Status.LifecycleStatus = clv1beta1.InProgress
				})

				It("should set the correct value for the instance_lifecycle_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_lifecycle_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(0)))
				})
			})

			When("the instance lifecycle status is Succeeded", func() {
				BeforeEach(func() {
					instance.Status.LifecycleStatus = clv1beta1.Succeeded
				})

				It("should set the correct value for the instance_lifecycle_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_lifecycle_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(1)))
				})
			})

			When("the instance lifecycle status is Failed", func() {
				BeforeEach(func() {
					instance.Status.LifecycleStatus = clv1beta1.Failed
				})

				It("should set the correct value for the instance_lifecycle_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_lifecycle_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(2)))
				})
			})

			When("the instance lifecycle status is InDeletion", func() {
				BeforeEach(func() {
					instance.Status.LifecycleStatus = clv1beta1.InDeletion
				})

				It("should set the correct value for the instance_lifecycle_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_lifecycle_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(3)))
				})
			})
		})

		Context("Test instance_plan_version metric", func() {
			It("should assign the correct labels", func() {
				metricsHelper.Update(instance)
				metric, err := getMetric(registry, "instance_plan_version")
				Expect(err).NotTo(HaveOccurred())
				Expect(metric.GetLabel()).To(ConsistOf(
					&io_prometheus_client.LabelPair{Name: proto.String("name"), Value: proto.String("test-instance")},
					&io_prometheus_client.LabelPair{Name: proto.String("namespace"), Value: proto.String("test-namespace")},
				))
			})

			It("should set the correct value for the instance_plan_version metric", func() {
				metricsHelper.Update(instance)
				metric, err := getMetric(registry, "instance_plan_version")
				Expect(err).NotTo(HaveOccurred())
				Expect(metric.GetGauge().GetValue()).To(Equal(float64(42)))
			})
		})

		Context("Test instance_os_v2_upgrade_status metric", func() {
			It("should assign the correct labels", func() {
				metricsHelper.Update(instance)
				metric, err := getMetric(registry, "instance_os_v2_upgrade_status")
				Expect(err).NotTo(HaveOccurred())
				Expect(metric.GetLabel()).To(ConsistOf(
					&io_prometheus_client.LabelPair{Name: proto.String("name"), Value: proto.String("test-instance")},
					&io_prometheus_client.LabelPair{Name: proto.String("namespace"), Value: proto.String("test-namespace")},
				))
			})

			When("OpenSearch is not yet upgraded to V2", func() {
				BeforeEach(func() {
					instance.Status.OpenSearchClusterTargetVersion = "1.0.0"

					conditionHelper.SetCondition(instance, metav1.Condition{
						Type:   conditions.OpenSearchUpgraded,
						Status: metav1.ConditionTrue,
					})
				})

				It("should set the correct value for the instance_os_v2_upgrade_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_os_v2_upgrade_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(0)))
				})
			})

			When("OpenSearch is completely upgraded to V2", func() {
				BeforeEach(func() {
					instance.Status.OpenSearchClusterTargetVersion = "2.0.0"

					conditionHelper.SetCondition(instance, metav1.Condition{
						Type:   conditions.OpenSearchUpgraded,
						Status: metav1.ConditionTrue,
					})
				})

				It("should set the correct value for the instance_os_v2_upgrade_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_os_v2_upgrade_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(1)))
				})
			})

			When("OpenSearch upgrade to V2 is in progress", func() {
				BeforeEach(func() {
					instance.Status.OpenSearchClusterTargetVersion = "2.0.1"

					conditionHelper.SetCondition(instance, metav1.Condition{
						Type:   conditions.OpenSearchUpgraded,
						Status: metav1.ConditionFalse,
					})
				})

				It("should set the correct value for the instance_os_v2_upgrade_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_os_v2_upgrade_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(2)))
				})
			})

			When("OpenSearch upgrade to V2 is in progress, but taking too long", func() {
				BeforeEach(func() {
					instance.Status.OpenSearchClusterTargetVersion = "2.0.0"

					conditionHelper.SetCondition(instance, metav1.Condition{
						Type:               conditions.OpenSearchUpgraded,
						Status:             metav1.ConditionFalse,
						LastTransitionTime: metav1.Time{Time: time.Now().Add(-91 * time.Minute)},
					})
				})

				It("should set the correct value for the instance_os_v2_upgrade_status metric", func() {
					metricsHelper.Update(instance)
					metric, err := getMetric(registry, "instance_os_v2_upgrade_status")
					Expect(err).NotTo(HaveOccurred())
					Expect(metric.GetGauge().GetValue()).To(Equal(float64(3)))
				})
			})
		})
	})

	Describe("MarkAsDeleted", func() {
		BeforeEach(func() {
			registry = prometheus.NewRegistry()
			metricsHelper.Register(registry)
			instance = &clv1beta1.Instance{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-instance",
					Namespace: "test-namespace",
				},
			}
		})

		It("should set the lifecycle status of the instance to deleted", func() {
			metricsHelper.MarkAsDeleted(types.NamespacedName{Name: instance.Name, Namespace: instance.Namespace})
			metric, err := getMetric(registry, "instance_lifecycle_status")
			Expect(err).NotTo(HaveOccurred())
			Expect(metric.GetGauge().GetValue()).To(Equal(float64(4)))
		})

		It("should set the health status of the instance to not applicable", func() {
			metricsHelper.MarkAsDeleted(types.NamespacedName{Name: instance.Name, Namespace: instance.Namespace})
			metric, err := getMetric(registry, "instance_health_status")
			Expect(err).NotTo(HaveOccurred())
			Expect(metric.GetGauge().GetValue()).To(Equal(float64(2)))
		})
	})
})

// getMetric returns the metric with the given family name and index from the given registry
func getMetric(registry *prometheus.Registry, familyName string) (*io_prometheus_client.Metric, error) {
	families, err := registry.Gather()
	Expect(err).NotTo(HaveOccurred())
	for _, family := range families {
		if family.GetName() == familyName {
			if len(family.GetMetric()) == 0 {
				return nil, fmt.Errorf("metric family '%s' is empty", familyName)
			}
			return family.GetMetric()[0], nil
		}
	}
	return nil, fmt.Errorf("metric family '%s' not found", familyName)
}
