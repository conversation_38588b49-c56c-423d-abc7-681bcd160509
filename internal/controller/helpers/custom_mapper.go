package helpers

import (
	"context"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

type customMapper struct {
	name string
}

// NewCustomMapper provides a new customMapper that maps event objects to custom reconcile requests
func NewCustomMapper(name string) CustomMapper {
	return &customMapper{name}
}

func (m *customMapper) Map(_ context.Context, obj client.Object) []reconcile.Request {
	if shouldProcessEvent(obj, m.name) {
		newName := replaceNamespacePrefixForInstance(obj.GetNamespace())
		req := reconcile.Request{
			NamespacedName: client.ObjectKey{
				Namespace: obj.GetNamespace(),
				Name:      newName,
			},
		}
		return []reconcile.Request{req}
	}
	return nil
}

func replaceNamespacePrefixForInstance(ns string) string {
	if strings.HasPrefix(ns, "sf-") {
		return strings.Replace(ns, "sf-", "instance-", 1)
	}

	return ns
}

func shouldProcessEvent(obj metav1.Object, name string) bool {
	return strings.HasPrefix(obj.GetNamespace(), "sf-") && obj.GetName() == name
}
