/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/controller/helpers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/controller/predicates"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

// InstanceReconciler reconciles an Instance object
type InstanceReconciler struct {
	client.Client
	*runtime.Scheme
	logr.Logger
	reconciler.ResourceReconciler
	conditionHelper      conditions.ConditionHelper
	metricsHelper        helpers.MetricsHelper
	config               config.Reader
	componentReconcilers []reconcilers.ComponentReconciler
}

const InstanceFinalizer = "operator.cloud-logging.sap.com"

func NewInstanceReconciler(client client.Client, scheme *runtime.Scheme, metricsRegistry prometheus.Registerer,
	componentReconcilers []reconcilers.ComponentReconciler) *InstanceReconciler {
	conditionHelper := conditions.NewConditionHelper()
	metricsHelper := helpers.NewMetricsHelper(conditionHelper)
	metricsHelper.Register(metricsRegistry)

	return &InstanceReconciler{
		Client:               client,
		Scheme:               scheme,
		Logger:               log.Log.WithName("instance-reconciler"),
		conditionHelper:      conditionHelper,
		metricsHelper:        metricsHelper,
		config:               config.GetInstance(),
		componentReconcilers: componentReconcilers,
	}
}

// +kubebuilder:rbac:groups=cloud-logging.sap.com,resources=instances,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=cloud-logging.sap.com,resources=instances/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=cloud-logging.sap.com,resources=instances/finalizers,verbs=update
// +kubebuilder:rbac:groups=apps,resources=statefulsets;statefulsets/status,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=apps,resources=deployments,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=configmaps,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=services,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=secrets,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=pods,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=namespaces,verbs=get;list;create;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=events,verbs=create;update;patch
// +kubebuilder:rbac:groups=core,resources=serviceaccounts,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=policy,resources=poddisruptionbudgets,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=rbac.authorization.k8s.io,resources=roles,verbs=get;list;watch;create;update;patch;bind;delete
// +kubebuilder:rbac:groups=rbac.authorization.k8s.io,resources=rolebindings,verbs=get;list;watch;create;update;patch;delete

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.17.0/pkg/reconcile
func (r *InstanceReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	instance := &clv1beta1.Instance{}
	err := r.Get(ctx, req.NamespacedName, instance)

	if err != nil {
		if errors.IsNotFound(err) {
			// object not found, could have been deleted after
			// reconcile request, hence don't requeue
			return ctrl.Result{}, nil
		}
		// error reading the object, requeue the request
		return ctrl.Result{}, err
	}

	// check if instance is not marked for deletion
	if instance.ObjectMeta.DeletionTimestamp.IsZero() {
		// add finalizer if not present
		if !controllerutil.ContainsFinalizer(instance, InstanceFinalizer) {
			r.Logger.Info("Adding finalizer to instance", "instance", instance.GetName())
			controllerutil.AddFinalizer(instance, InstanceFinalizer)
			if err = r.Update(ctx, instance); err != nil {
				return ctrl.Result{}, err
			}
		}

		// check if instance is set to skip reconciliation
		expired, err := utils.IsRFC3339TimestampExpired(instance.Spec.SkipReconciliationUntil)
		if err != nil {
			r.Logger.Error(err, "Failed to parse reconcile skip timestamp", "instance", instance.Name)
		} else if !expired {
			r.Logger.Info("Skip reconciling instance",
				"instance", instance.Name, "until", instance.Spec.SkipReconciliationUntil)
			return ctrl.Result{}, nil
		}

		return r.reconcileInstance(ctx, instance)
	}

	// the object is being deleted, run cleanup
	if controllerutil.ContainsFinalizer(instance, InstanceFinalizer) {
		return r.cleanupInstance(ctx, instance)
	}
	r.Logger.Error(nil, "Instance has no finalizer, but is marked for deletion", "instance", instance.GetName())
	return ctrl.Result{}, nil
}

// reconcileInstance runs all reconcilers for the given instance and updates the health and lifecycle status
func (r *InstanceReconciler) reconcileInstance(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	r.Logger.V(1).Info("Reconciling instance", "instance", instance.GetName())

	// Run reconcilers one by one and use a combined result to
	// - return and requeue immediately, if an error gets reported by any reconciler
	// - continue reconciling, but requeue after the minimum 'RequeueAfter' time returned by reconcilers
	// - requeue after a default time if no reconciler requested a shorter requeue time
	var reconcileResult reconciler.CombinedResult
	for _, componentReconciler := range r.componentReconcilers {
		// if the context is canceled, the reconciliation is stopped
		if ctx.Err() != nil {
			reconcileResult.CombineErr(ctx.Err())
			break
		}
		res, err := componentReconciler.Reconcile(ctx, instance)
		reconcileResult.Combine(&res, err)
		if err != nil {
			r.Logger.Error(err, "Failed to reconcile instance", "instance", instance.GetName(), "reconciler", componentReconciler.GetName())
			break
		}
	}

	// set health status based on conditions and combined results
	r.updateHealthStatus(instance, reconcileResult)

	// set lifecycle status based on health status and combined results
	r.updateLifecycleStatus(instance, reconcileResult)

	// sort conditions for better readability
	r.conditionHelper.SortConditions(instance)

	// update the status of the instance in the cluster
	reconcileResult.Combine(r.updateStatus(ctx, instance))

	// update prometheus metrics
	r.metricsHelper.Update(instance)

	// return error if any reconciler reported an error
	if reconcileResult.Err != nil {
		return ctrl.Result{}, reconcileResult.Err
	}

	// requeue with a delay
	reconcileResult.Combine(&ctrl.Result{Requeue: true,
		RequeueAfter: r.config.GetInstanceReconciler().RequeueDelay}, nil)

	return reconcileResult.Result, nil
}

// cleanupInstance runs cleanup for all reconcilers and removes the finalizer if no requeue is requested.
func (r *InstanceReconciler) cleanupInstance(ctx context.Context, instance *clv1beta1.Instance) (ctrl.Result, error) {
	var cleanupResult reconciler.CombinedResult

	// set lifecycle status to 'InDeletion' if not already set, update metrics
	// the updated status will trigger an immediate requeue for the actual deletion
	if instance.Status.LifecycleStatus != clv1beta1.InDeletion {
		instance.Status.LifecycleStatus = clv1beta1.InDeletion
		r.metricsHelper.Update(instance)
		cleanupResult.Combine(r.updateStatus(ctx, instance))
		return cleanupResult.Result, cleanupResult.Err
	}

	r.Logger.Info("Cleaning up instance", "instance", instance.GetName())

	// run cleanup for all reconcilers
	for _, componentReconciler := range r.componentReconcilers {
		if ctx.Err() != nil {
			cleanupResult.CombineErr(ctx.Err())
		}
		res, err := componentReconciler.CleanUp(ctx, instance)
		cleanupResult.Combine(&res, err)
	}

	// return error if any reconciler reported an error
	if cleanupResult.Err != nil {
		r.Logger.Error(cleanupResult.Err, "Failed to clean up instance", "instance", instance.GetName())
		return ctrl.Result{}, cleanupResult.Err
	}

	// remove finalizer if no requeue is requested
	if cleanupResult.Result.IsZero() {
		r.Logger.Info("Removing finalizer from instance", "instance", instance.GetName())
		namespacedName := client.ObjectKeyFromObject(instance)
		controllerutil.RemoveFinalizer(instance, InstanceFinalizer)
		err := r.Update(ctx, instance)
		if err != nil {
			r.Logger.Error(err, "Failed to remove finalizer from instance", "instance", instance.GetName())
			return ctrl.Result{}, err
		}

		r.metricsHelper.MarkAsDeleted(namespacedName)
	}
	return cleanupResult.Result, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *InstanceReconciler) SetupWithManager(mgr ctrl.Manager, passwordStoreService services.PasswordStoreService) error {
	passwordStoreMapper := helpers.NewCustomMapper(passwordStoreService.GetSecretName())

	return ctrl.NewControllerManagedBy(mgr).
		WithOptions(controller.Options{
			MaxConcurrentReconciles: r.config.GetInstanceReconciler().MaxConcurrentReconciles,
		}).
		For(&clv1beta1.Instance{}, builder.WithPredicates(predicates.GetInstancePredicate())).
		Owns(&appsv1.Deployment{}, builder.WithPredicates(predicates.GetDeploymentPredicate())).
		Owns(&appsv1.StatefulSet{}, builder.WithPredicates(predicates.GetStatefulSetPredicate())).
		// TODO: remove the watcher for password-store once instance become the owner for password-store secret.
		// add 'Owns' definition for secrets(password-store) in the future.
		Watches(
			&corev1.Secret{},
			handler.EnqueueRequestsFromMapFunc(passwordStoreMapper.Map),
			builder.WithPredicates(predicates.GetSecretPredicate()),
		).
		Complete(r)
}

// updateHealthStatus updates the health status of the instance based on conditions and reconcile result.
// It only modifies the status locally, the actual update to the cluster is done in updateStatus
func (r *InstanceReconciler) updateHealthStatus(instance *clv1beta1.Instance,
	reconcileResult reconciler.CombinedResult,
) {
	// if reconciliation failed we set the status to DOWN
	if reconcileResult.Err != nil {
		instance.Status.HealthStatus = clv1beta1.Down
		return
	}

	// get relevant conditions from reconcilers (this list can change based on components being enabled/disabled)
	var relevantConditions []string
	for _, componentReconciler := range r.componentReconcilers {
		relevantConditions = append(relevantConditions, componentReconciler.GetValidConditionNames(instance)...)
	}

	// check relevant conditions and set health status accordingly
	if r.conditionHelper.CheckAllConditionsTrue(instance, relevantConditions...) {
		if instance.Status.HealthStatus != clv1beta1.Up {
			r.Logger.Info("Instance HealthStatus switched to Up", "instance", instance.GetName())
			instance.Status.HealthStatus = clv1beta1.Up
		}
	} else {
		if instance.Status.HealthStatus != clv1beta1.Down {
			r.Logger.Info("Instance HealthStatus switched to Down", "instance", instance.GetName())
			instance.Status.HealthStatus = clv1beta1.Down
		}
	}
}

// updateLifecycleStatus updates the lifecycle status of the instance based on the health status
// It only modifies the status locally, the actual update to the cluster is done in updateStatus
func (r *InstanceReconciler) updateLifecycleStatus(instance *clv1beta1.Instance,
	reconcileResult reconciler.CombinedResult,
) {
	// if reconciliation failed conditions might not be in correct state
	if reconcileResult.Err != nil {
		return
	}

	// do nothing if instance is in succeeded state or failed state
	if instance.Status.LifecycleStatus == clv1beta1.Succeeded || instance.Status.LifecycleStatus == clv1beta1.Failed {
		return
	}

	// set the lifecycle status to succeeded if health status is up
	if instance.Status.HealthStatus == clv1beta1.Up {
		r.Logger.Info("Instance LifecycleStatus switched to Succeeded", "instance", instance.GetName())
		instance.Status.LifecycleStatus = clv1beta1.Succeeded
	}
}

// updateStatus updates the status of the instance in the cluster
// This includes any changes to the conditions, lifecycleStatus and healthStatus
func (r *InstanceReconciler) updateStatus(ctx context.Context, instance *clv1beta1.Instance) (*ctrl.Result, error) {
	err := r.Client.Status().Update(ctx, instance)

	// if the resource version has changed, we need to requeue rather than retrying immediately
	// this way we can avoid skipping updates in case of concurrent modifications
	if errors.IsConflict(err) {
		return &ctrl.Result{Requeue: true}, nil
	}
	return &ctrl.Result{}, err
}
