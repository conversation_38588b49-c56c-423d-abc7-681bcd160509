package services_test

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/builders"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	policyv1 "k8s.io/api/policy/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test the OpenSearch topology service ", func() {
	const (
		instanceName = "cls-test"
		namespace    = "sf-test"
	)

	var (
		openSearchTopologyService services.OpenSearchTopologyService
		instance                  *clv1beta1.Instance
		sts1                      *appsv1.StatefulSet
		sts2                      *appsv1.StatefulSet
		hs1                       *corev1.Service
		hs2                       *corev1.Service
		pdb1                      *policyv1.PodDisruptionBudget
		pdb2                      *policyv1.PodDisruptionBudget
		ctx                       = context.Background()
	)

	BeforeEach(func() {
		openSearchTopologyService = services.NewOpenSearchTopologyService(k8sClient)

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:        instanceName,
				Namespace:   namespace,
				Annotations: map[string]string{},
			},
			Spec: clv1beta1.InstanceSpec{
				OpenSearch: clv1beta1.OpenSearch{
					Cluster: clv1beta1.Cluster{
						Name: "cls-test",
						NodePools: []clv1beta1.NodePool{
							{
								Name:  "group-1",
								Roles: []clv1beta1.NodeRole{clv1beta1.ClusterManager},
							},
							{
								Name:  "group-2",
								Roles: []clv1beta1.NodeRole{clv1beta1.Data},
							},
						},
						ExposeHttpEndpoint: true,
						MaxDataNodes:       4,
						AdditionalConfig: map[string]string{
							"rollover_min_primary_shard_size": "20Gi",
						},
					},
				},
			},
		}

		ns := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace,
			},
		}

		err := k8sClient.Get(ctx, types.NamespacedName{Name: namespace}, ns)
		if err != nil && apierrors.IsNotFound(err) {
			Expect(k8sClient.Create(ctx, ns)).To(Succeed())
		}

		err = k8sClient.Get(ctx, types.NamespacedName{Name: instanceName, Namespace: namespace}, &clv1beta1.Instance{})
		if err != nil && apierrors.IsNotFound(err) {
			Expect(k8sClient.Create(ctx, instance)).To(Succeed())
		}

		sts1 = builders.BuildOpenSearchStatefulSet(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0], "opensearch:1.0.0")
		sts2 = builders.BuildOpenSearchStatefulSet(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1], "opensearch:1.0.1")
		hs1 = builders.BuildOpenSearchHeadlessService(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0])
		hs2 = builders.BuildOpenSearchHeadlessService(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1])
		pdb1 = builders.BuildOpenSearchPodDisruptionBudget(instance, &instance.Spec.OpenSearch.Cluster.NodePools[0])
		pdb2 = builders.BuildOpenSearchPodDisruptionBudget(instance, &instance.Spec.OpenSearch.Cluster.NodePools[1])

		sts1.Spec.Replicas = ptr.To(int32(3))
		sts2.Spec.Replicas = ptr.To(int32(2))
	})

	AfterEach(func() {
		Expect(k8sClient.DeleteAllOf(ctx, &clv1beta1.Instance{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &appsv1.StatefulSet{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.Service{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &corev1.ConfigMap{}, client.InNamespace(namespace))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(ctx, &policyv1.PodDisruptionBudget{}, client.InNamespace(namespace))).To(Succeed())
	})

	Context("Discover", func() {
		When("no StatefulSets are present", func() {
			It("should return an empty topology", func() {
				topology, err := openSearchTopologyService.Discover(ctx, instance)
				Expect(err).ToNot(HaveOccurred())
				Expect(topology.NodeGroups).To(HaveLen(0))
			})
		})

		When("the discovered StatefulSets have a corresponding NodePool definition in the instance CR", func() {
			When("all resources are present", func() {
				BeforeEach(func() {
					Expect(k8sClient.Create(ctx, sts1)).To(Succeed())
					Expect(k8sClient.Create(ctx, sts2)).To(Succeed())
					Expect(k8sClient.Create(ctx, hs1)).To(Succeed())
					Expect(k8sClient.Create(ctx, hs2)).To(Succeed())
					Expect(k8sClient.Create(ctx, pdb1)).To(Succeed())
					Expect(k8sClient.Create(ctx, pdb2)).To(Succeed())
				})

				It("should return the expected topology", func() {
					topology, err := openSearchTopologyService.Discover(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(topology.NodeGroups).To(HaveLen(2))
					Expect(topology.NodeGroups[0].NodePool.Name).To(Equal("group-1"))
					Expect(topology.NodeGroups[1].NodePool.Name).To(Equal("group-2"))
					Expect(topology.NodeGroups[0].StatefulSet.Name).To(Equal("elastic-group-1"))
					Expect(topology.NodeGroups[1].StatefulSet.Name).To(Equal("elastic-group-2"))
					Expect(topology.NodeGroups[0].HeadlessService.Name).To(Equal("opensearch-group-1-headless"))
					Expect(topology.NodeGroups[1].HeadlessService.Name).To(Equal("opensearch-group-2-headless"))
					Expect(topology.NodeGroups[0].PodDisruptionBudget.Name).To(Equal("opensearch-group-1-pdb"))
					Expect(topology.NodeGroups[1].PodDisruptionBudget.Name).To(Equal("opensearch-group-2-pdb"))

					Expect(topology.GetOpenSearchImages()).To(HaveLen(2))
					Expect(topology.GetOpenSearchImages()).To(ConsistOf("opensearch:1.0.0", "opensearch:1.0.1"))
				})
			})

			When("headless services are missing", func() {
				BeforeEach(func() {
					Expect(k8sClient.Create(ctx, sts1)).To(Succeed())
					Expect(k8sClient.Create(ctx, sts2)).To(Succeed())
					Expect(k8sClient.Create(ctx, pdb1)).To(Succeed())
					Expect(k8sClient.Create(ctx, pdb2)).To(Succeed())
				})

				It("should not return an error", func() {
					_, err := openSearchTopologyService.Discover(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
				})
			})

			When("PDBs are missing", func() {
				BeforeEach(func() {
					Expect(k8sClient.Create(ctx, sts1)).To(Succeed())
					Expect(k8sClient.Create(ctx, sts2)).To(Succeed())
					Expect(k8sClient.Create(ctx, hs1)).To(Succeed())
					Expect(k8sClient.Create(ctx, hs2)).To(Succeed())
				})

				It("should not return an error", func() {
					_, err := openSearchTopologyService.Discover(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
				})
			})

			When("all resources are present with legacy labels", func() {
				BeforeEach(func() {
					sts1.Labels = map[string]string{
						"app": "elasticsearch",
					}
					sts2.Labels = map[string]string{
						"app": "elasticsearch",
					}

					Expect(k8sClient.Create(ctx, sts1)).To(Succeed())
					Expect(k8sClient.Create(ctx, sts2)).To(Succeed())
					Expect(k8sClient.Create(ctx, hs1)).To(Succeed())
					Expect(k8sClient.Create(ctx, hs2)).To(Succeed())
					Expect(k8sClient.Create(ctx, pdb1)).To(Succeed())
					Expect(k8sClient.Create(ctx, pdb2)).To(Succeed())
				})

				It("should return the expected topology", func() {
					topology, err := openSearchTopologyService.Discover(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(topology.NodeGroups).To(HaveLen(2))
					Expect(topology.NodeGroups[0].NodePool.Name).To(Equal("group-1"))
					Expect(topology.NodeGroups[1].NodePool.Name).To(Equal("group-2"))
					Expect(topology.NodeGroups[0].StatefulSet.Name).To(Equal("elastic-group-1"))
					Expect(topology.NodeGroups[1].StatefulSet.Name).To(Equal("elastic-group-2"))
				})
			})
		})

		When("a discovered StatefulSet does not have a corresponding NodePool definition in the instance CR (leftover node group)", func() {
			BeforeEach(func() {
				instance.Spec.OpenSearch.Cluster.NodePools = []clv1beta1.NodePool{}
				Expect(k8sClient.Create(ctx, sts1)).To(Succeed())
				Expect(k8sClient.Create(ctx, hs1)).To(Succeed())
				Expect(k8sClient.Create(ctx, pdb1)).To(Succeed())
			})

			It("should not return an error", func() {
				_, err := openSearchTopologyService.Discover(ctx, instance)
				Expect(err).ToNot(HaveOccurred())
			})

			It("should return the expected topology", func() {
				topology, err := openSearchTopologyService.Discover(ctx, instance)
				Expect(err).ToNot(HaveOccurred())
				Expect(topology.NodeGroups).To(HaveLen(1))
				Expect(topology.NodeGroups[0].NodePool).To(BeNil()) // <-- we don't have a NodePool definition for this group
				Expect(topology.NodeGroups[0].StatefulSet.Name).To(Equal("elastic-group-1"))
				Expect(topology.NodeGroups[0].HeadlessService.Name).To(Equal("opensearch-group-1-headless"))
				Expect(topology.NodeGroups[0].PodDisruptionBudget.Name).To(Equal("opensearch-group-1-pdb"))

				Expect(topology.GetOpenSearchImages()).To(HaveLen(1))
				Expect(topology.GetOpenSearchImages()).To(ConsistOf("opensearch:1.0.0"))
			})

			When("there is no associated PDB", func() {
				BeforeEach(func() {
					Expect(k8sClient.Delete(ctx, pdb1)).To(Succeed())
				})

				It("should not return an error", func() {
					_, err := openSearchTopologyService.Discover(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
				})

				It("should return the expected topology", func() {
					topology, err := openSearchTopologyService.Discover(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(topology.NodeGroups).To(HaveLen(1))
					Expect(topology.NodeGroups[0].NodePool).To(BeNil()) // <-- we don't have a NodePool definition for this group
					Expect(topology.NodeGroups[0].StatefulSet.Name).To(Equal("elastic-group-1"))
					Expect(topology.NodeGroups[0].HeadlessService.Name).To(Equal("opensearch-group-1-headless"))
					Expect(topology.NodeGroups[0].PodDisruptionBudget).To(BeNil()) // <-- the pdb must be nil
				})
			})

			When("there is no associated headless service", func() {
				BeforeEach(func() {
					Expect(k8sClient.Delete(ctx, hs1)).To(Succeed())
				})

				It("should not return an error", func() {
					_, err := openSearchTopologyService.Discover(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
				})

				It("should return the expected topology", func() {
					topology, err := openSearchTopologyService.Discover(ctx, instance)
					Expect(err).ToNot(HaveOccurred())
					Expect(topology.NodeGroups).To(HaveLen(1))
					Expect(topology.NodeGroups[0].NodePool).To(BeNil()) // <-- we don't have a NodePool definition for this group
					Expect(topology.NodeGroups[0].StatefulSet.Name).To(Equal("elastic-group-1"))
					Expect(topology.NodeGroups[0].HeadlessService).To(BeNil()) // <-- the headless service must be nil
					Expect(topology.NodeGroups[0].PodDisruptionBudget.Name).To(Equal("opensearch-group-1-pdb"))
				})
			})
		})
	})

	Context("GetNumberOfPVCs", func() {
		When("no PVCs are present", func() {
			It("should return 0", func() {
				count, err := openSearchTopologyService.GetNumberOfPVCs(ctx, instance, "data-elastic-group-1")
				Expect(err).ToNot(HaveOccurred())
				Expect(count).To(Equal(int32(0)))
			})
		})

		When("PVCs are present", func() {
			BeforeEach(func() {
				pvc1 := &corev1.PersistentVolumeClaim{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "data-elastic-group-1-0",
						Namespace: namespace,
					},
					Spec: corev1.PersistentVolumeClaimSpec{
						AccessModes: []corev1.PersistentVolumeAccessMode{corev1.ReadWriteOnce},
						Resources: corev1.VolumeResourceRequirements{
							Requests: corev1.ResourceList{
								corev1.ResourceStorage: resource.MustParse("10Gi"),
							},
						},
					},
				}

				pvc2 := &corev1.PersistentVolumeClaim{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "data-elastic-group-1-1",
						Namespace: namespace,
					},
					Spec: corev1.PersistentVolumeClaimSpec{
						AccessModes: []corev1.PersistentVolumeAccessMode{corev1.ReadWriteOnce},
						Resources: corev1.VolumeResourceRequirements{
							Requests: corev1.ResourceList{
								corev1.ResourceStorage: resource.MustParse("10Gi"),
							},
						},
					},
				}

				Expect(k8sClient.Create(ctx, pvc1)).To(Succeed())
				Expect(k8sClient.Create(ctx, pvc2)).To(Succeed())
			})

			It("should return the correct number of PVCs", func() {
				count, err := openSearchTopologyService.GetNumberOfPVCs(ctx, instance, "data-elastic-group-1")
				Expect(err).ToNot(HaveOccurred())
				Expect(count).To(Equal(int32(2)))
			})
		})
	})
})
