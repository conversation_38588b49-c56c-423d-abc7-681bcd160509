package services

import (
	"context"
	"fmt"
	"strings"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	policyv1 "k8s.io/api/policy/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type openSearchTopologyService struct {
	client client.Client
}

func NewOpenSearchTopologyService(client client.Client) OpenSearchTopologyService {
	return &openSearchTopologyService{
		client: client,
	}
}

// Discover checks the namespace of the given namespace for StatefulSets belonging to an OpenSearch cluster
func (t *openSearchTopologyService) Discover(ctx context.Context, instance *clv1beta1.Instance) (*models.Topology, error) {
	labelSelector := &metav1.LabelSelector{
		MatchExpressions: []metav1.LabelSelectorRequirement{
			{
				Key:      "app",
				Operator: metav1.LabelSelectorOpIn,
				Values:   []string{"opensearch", "elasticsearch"},
			},
		},
	}

	selector, err := metav1.LabelSelectorAsSelector(labelSelector)
	if err != nil {
		return nil, fmt.Errorf("failed to create label selector: %w", err)
	}

	statefulSets := &appsv1.StatefulSetList{}
	err = t.client.List(ctx, statefulSets, client.InNamespace(instance.Namespace), client.MatchingLabelsSelector{Selector: selector})
	if err != nil {
		return nil, fmt.Errorf("failed to list StatefulSets: %w", err)
	}

	topology := &models.Topology{
		NodeGroups: []models.NodeGroup{},
	}

	for _, sts := range statefulSets.Items {
		// Try to find and attach the NodePool definition for the current StatefulSet based on naming convention
		// A nil NodePool means that the StatefulSet has no corresponding NodePool definition.
		nodePool := t.findNodePoolByName(instance, sts.Name)

		// Get the headless service for the StatefulSet
		headlessService := &corev1.Service{}
		err = t.client.Get(ctx, client.ObjectKey{Namespace: instance.Namespace, Name: sts.Spec.ServiceName}, headlessService)
		if err != nil {
			if errors.IsNotFound(err) {
				headlessService = nil
			} else {
				return nil, fmt.Errorf("failed to get headless service: %w", err)
			}
		}

		// Get the PodDisruptionBudget for the StatefulSet
		// This only works for the new naming convention, where the PDB is named after the StatefulSet name. E.g.
		// StatefulSet: elastic-data
		// PodDisruptionBudget: opensearch-data-pdb
		pdb := &policyv1.PodDisruptionBudget{}
		pdbName := fmt.Sprintf("%s-pdb", strings.Replace(sts.Name, "elastic-", "opensearch-", 1))
		err = t.client.Get(ctx, client.ObjectKey{Namespace: instance.Namespace, Name: pdbName}, pdb)
		if err != nil {
			if errors.IsNotFound(err) { // Ignore NotFound errors
				pdb = nil
			} else {
				return nil, fmt.Errorf("failed to get PodDisruptionBudget: %w", err)
			}
		}

		// Add the NodeGroup to the topology
		topology.NodeGroups = append(topology.NodeGroups, models.NodeGroup{
			StatefulSet:         sts.DeepCopy(),
			HeadlessService:     headlessService,
			PodDisruptionBudget: pdb,
			NodePool:            nodePool,
		})
	}

	topology.Sort(models.SortOrderNormal)

	return topology, nil
}

// GetNumberOfPVCs returns the number of PVCs with the given name prefix
func (t *openSearchTopologyService) GetNumberOfPVCs(ctx context.Context, instance *clv1beta1.Instance, prefix string) (int32, error) {
	pvcs := &corev1.PersistentVolumeClaimList{}

	err := t.client.List(ctx, pvcs, client.InNamespace(instance.Namespace))
	if err != nil {
		return 0, fmt.Errorf("failed to list PVCs: %w", err)
	}

	// filter PVCs by name prefix and count them
	var count int32
	for _, pvc := range pvcs.Items {
		if strings.HasPrefix(pvc.Name, prefix) {
			count++
		}
	}
	return count, nil
}

func (t *openSearchTopologyService) findNodePoolByName(instance *clv1beta1.Instance, stsName string) *clv1beta1.NodePool {
	for _, nodePool := range instance.Spec.OpenSearch.Cluster.NodePools {
		if fmt.Sprintf("elastic-%s", nodePool.Name) == stsName {
			return &nodePool
		}
	}

	return nil
}
