package services_test

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test the password-store service ", func() {
	var (
		passwordStoreService services.PasswordStoreService
		instance             *clv1beta1.Instance
		secret               *corev1.Secret
		ctx                  = context.Background()
	)

	BeforeEach(func() {
		passwordStoreService = services.NewPasswordStoreService(k8sClient)
		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test_instance",
				Namespace: "default",
			},
			Spec: clv1beta1.InstanceSpec{},
		}

		secret = &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "passwords-store",
				Namespace: instance.Namespace,
			},
			Data: map[string][]byte{
				"test_user":      []byte("test_password"),
				"test_userHash":  []byte("test_hash"),
				"test_user2":     []byte("test_password2"),
				"test_user2Hash": []byte("test_hash2"),
			},
		}
	})

	Context("Test GetOpenSearchCredentials", func() {
		When("the password-store secret does not exist", func() {
			BeforeEach(func() {
				Expect(errors.IsNotFound(k8sClient.Get(ctx, client.ObjectKey{Name: secret.Name, Namespace: secret.Namespace}, secret))).To(BeTrue())
			})

			It("should return an error", func() {
				_, err := passwordStoreService.GetOpenSearchCredentials(ctx, instance, "test_user")
				Expect(err).To(HaveOccurred())
			})
		})

		When("the password-store secret exist", func() {
			BeforeEach(func() {
				Expect(k8sClient.Create(ctx, secret)).To(Succeed())
			})

			AfterEach(func() {
				Expect(k8sClient.Delete(ctx, secret)).To(Succeed())
			})

			Context("The requested credentials exist", func() {
				It("should return the expected credentials", func() {
					credentials, err := passwordStoreService.GetOpenSearchCredentials(ctx, instance, "test_user")
					Expect(err).ToNot(HaveOccurred())
					Expect(credentials.HasUsername()).To(BeTrue())
					Expect(credentials.HasPassword()).To(BeTrue())
					Expect(credentials.HasHash()).To(BeTrue())
					Expect(credentials.Username).To(Equal("test_user"))
					Expect(credentials.Password).To(Equal("test_password"))
					Expect(credentials.Hash).To(Equal("test_hash"))
				})
			})

			Context("The requested credentials do not exist", func() {
				It("should return the empty credentials", func() {
					credentials, err := passwordStoreService.GetOpenSearchCredentials(ctx, instance, "test_user_unknown")
					Expect(err).ToNot(HaveOccurred())
					Expect(credentials.HasUsername()).To(BeTrue())
					Expect(credentials.HasPassword()).To(BeFalse())
					Expect(credentials.HasHash()).To(BeFalse())
					Expect(credentials.Username).To(Equal("test_user_unknown"))
					Expect(credentials.Password).To(Equal(""))
					Expect(credentials.Hash).To(Equal(""))
				})
			})
		})
	})

	Context("Test GetAllOpenSearchCredentials", func() {
		When("the password-store secret does not exist", func() {
			BeforeEach(func() {
				Expect(errors.IsNotFound(k8sClient.Get(ctx, client.ObjectKey{Name: secret.Name, Namespace: secret.Namespace}, secret))).To(BeTrue())
			})

			It("should return an error", func() {
				_, err := passwordStoreService.GetOpenSearchCredentials(ctx, instance, "test_user")
				Expect(err).To(HaveOccurred())
			})
		})

		When("the password-store secret exist", func() {
			BeforeEach(func() {
				Expect(k8sClient.Create(ctx, secret)).To(Succeed())
			})

			AfterEach(func() {
				Expect(k8sClient.Delete(ctx, secret)).To(Succeed())
			})

			It("should return the all OpenSearch credentials", func() {
				credentials, err := passwordStoreService.GetAllOpenSearchCredentials(ctx, instance)
				Expect(err).ToNot(HaveOccurred())
				Expect(credentials).To(HaveKey("test_user"))
				Expect(credentials["test_user"].Username).To(Equal("test_user"))
				Expect(credentials["test_user"].Password).To(Equal("test_password"))
				Expect(credentials["test_user"].Hash).To(Equal("test_hash"))
				Expect(credentials).To(HaveKey("test_user2"))
				Expect(credentials["test_user2"].Username).To(Equal("test_user2"))
				Expect(credentials["test_user2"].Password).To(Equal("test_password2"))
				Expect(credentials["test_user2"].Hash).To(Equal("test_hash2"))
			})
		})
	})
})
