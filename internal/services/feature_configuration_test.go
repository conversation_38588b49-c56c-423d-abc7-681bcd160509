package services_test

import (
	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

const (
	DevFeature        = "devFeature"
	DevAndProdFeature = "devAndProdFeature"
)

var _ = Describe("Test the feature flag service ", func() {
	var (
		featureConfiguration    services.FeatureConfiguration
		supportedFeatureFlags   []string
		allowedProdFeatureFlags []string
		instance                *clv1beta1.Instance
	)

	BeforeEach(func() {
		supportedFeatureFlags = []string{DevFeature, DevAndProdFeature}
		allowedProdFeatureFlags = []string{DevAndProdFeature}
		featureConfiguration = services.NewFeatureConfiguration(supportedFeatureFlags, allowedProdFeatureFlags)

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test_instance",
				Namespace: "default",
			},
			Spec: clv1beta1.InstanceSpec{
				Context: clv1beta1.Context{
					ClusterName: "cls-01",
				},
			},
		}
	})

	Context("Test IsFeatureSet", func() {
		When("instance feature flags cannot be parsed", func() {
			BeforeEach(func() {
				instance.Spec.FeatureFlags = "[This is not valid json"
			})

			It("should return false", func() {
				result := featureConfiguration.IsFeatureSet(instance, "flag")
				Expect(result).To(Equal(false))
			})
		})

		When("instance feature flags are empty", func() {
			BeforeEach(func() {
				instance.Spec.FeatureFlags = "[]"
			})

			It("should return false", func() {
				result := featureConfiguration.IsFeatureSet(instance, "flag")
				Expect(result).To(Equal(false))
			})
		})

		When("instance feature flag is not supported", func() {
			BeforeEach(func() {
				instance.Spec.FeatureFlags = "[\"featureC\"]"
			})

			It("should return false", func() {
				result := featureConfiguration.IsFeatureSet(instance, "featureC")
				Expect(result).To(Equal(false))
			})
		})

		When("instance feature flag is supported", func() {
			BeforeEach(func() {
				instance.Spec.FeatureFlags = "[\"" + DevFeature + "\",\"" + DevAndProdFeature + "\"]"
			})

			Context("instance is on a non-prod cluster", func() {
				BeforeEach(func() {
					instance.Spec.Context.ClusterName = "dev-cluster"
				})

				It("should return true if the feature flag is supported", func() {
					result := featureConfiguration.IsFeatureSet(instance, DevAndProdFeature)
					Expect(result).To(Equal(true))
				})

				It("should still return true if it is not an allowed prod feature flag", func() {
					result := featureConfiguration.IsFeatureSet(instance, DevFeature)
					Expect(result).To(Equal(true))
				})

				It("should return false when instance flags do not contain the feature", func() {
					result := featureConfiguration.IsFeatureSet(instance, "nonExistingFeature")
					Expect(result).To(Equal(false))
				})
			})

			Context("instance is on a prod cluster", func() {
				BeforeEach(func() {
					instance.Spec.Context.ClusterName = "cls-01"
				})

				It("should return true if the feature flag is supported", func() {
					result := featureConfiguration.IsFeatureSet(instance, DevAndProdFeature)
					Expect(result).To(Equal(true))
				})

				It("should  return false if it is not an allowed prod feature flag", func() {
					result := featureConfiguration.IsFeatureSet(instance, DevFeature)
					Expect(result).To(Equal(false))
				})

				It("should return false when instance flags do not contain the feature", func() {
					result := featureConfiguration.IsFeatureSet(instance, "nonExistingFeature")
					Expect(result).To(Equal(false))
				})
			})
		})
	})

	Context("Test IsFeatureAllowed", func() {
		When("instance is on a non-prod cluster", func() {
			BeforeEach(func() {
				instance.Spec.Context.ClusterName = "dev-cluster"
			})

			It("should return true if the feature flag is supported and allowed for production", func() {
				result, reason := featureConfiguration.IsFeatureAllowed(instance, DevAndProdFeature)
				Expect(result).To(Equal(true))
				Expect(reason).To(Equal(""))
			})

			It("should return true if the feature flag is supported and allowed for non-production", func() {
				result, reason := featureConfiguration.IsFeatureAllowed(instance, DevFeature)
				Expect(result).To(Equal(true))
				Expect(reason).To(Equal(""))
			})

			It("should return false if the feature flag is not supported", func() {
				result, reason := featureConfiguration.IsFeatureAllowed(instance, "nonExistingFeature")
				Expect(result).To(Equal(false))
				Expect(reason).To(Equal("Feature 'nonExistingFeature' is not supported"))
			})
		})

		When("instance is on a prod cluster", func() {
			BeforeEach(func() {
				instance.Spec.Context.ClusterName = "cls-01"
			})

			It("should return true if the feature flag is supported and allowed in production", func() {
				result, reason := featureConfiguration.IsFeatureAllowed(instance, DevAndProdFeature)
				Expect(result).To(Equal(true))
				Expect(reason).To(Equal(""))
			})

			It("should return false if the feature flag is supported but not allowed in production", func() {
				result, reason := featureConfiguration.IsFeatureAllowed(instance, DevFeature)
				Expect(result).To(Equal(false))
				Expect(reason).To(Equal("Feature 'devFeature' is not allowed in production"))
			})

			It("should return false if the feature flag is not supported", func() {
				result, reason := featureConfiguration.IsFeatureAllowed(instance, "nonExistingFeature")
				Expect(result).To(Equal(false))
				Expect(reason).To(Equal("Feature 'nonExistingFeature' is not supported"))
			})
		})
	})

	Context("Test GetSupportedFeatureFlags", func() {
		It("returns a map of supported feature flags", func() {
			result := featureConfiguration.GetSupportedFeatureFlags()
			Expect(result).To(Equal(supportedFeatureFlags))
		})
	})
})
