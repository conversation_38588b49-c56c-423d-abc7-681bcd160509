package services

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	dashboardsAdmin = "statuser"
)

type dashboardsService struct {
	passwordStoreService PasswordStoreService
	dashboardsClient     clients.DashboardsClient
}

func NewDashboardsService(dashboardsClient clients.DashboardsClient, passwordStoreService PasswordStoreService) DashboardsService {
	return &dashboardsService{
		dashboardsClient:     dashboardsClient,
		passwordStoreService: passwordStoreService,
	}
}

func (d *dashboardsService) GetStatusCondition(ctx context.Context, instance *clv1beta1.Instance) metav1.Condition {
	credentials, err := d.passwordStoreService.GetOpenSearchCredentials(ctx, instance, dashboardsAdmin)
	if err != nil {
		return metav1.Condition{
			Type:    conditions.DashboardsStatus,
			Status:  metav1.ConditionFalse,
			Reason:  conditions.StatusError,
			Message: err.Error(),
		}
	}

	status, err := d.dashboardsClient.GetStatus(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password))

	if err != nil {
		return metav1.Condition{
			Type:    conditions.DashboardsStatus,
			Status:  metav1.ConditionFalse,
			Reason:  conditions.StatusError,
			Message: err.Error(),
		}
	}

	return d.mapStatus(status)
}

func (d *dashboardsService) mapStatus(status models.DashboardsStatusResponse) metav1.Condition {
	state := status.Status.Overall.State

	switch state {
	case "green":
		return metav1.Condition{
			Type:    conditions.DashboardsStatus,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.Green,
			Message: "Dashboard status is green.",
		}
	case "yellow":
		return metav1.Condition{
			Type:    conditions.DashboardsStatus,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.Yellow,
			Message: "Dashboard status is yellow.",
		}
	case "red":
		return metav1.Condition{
			Type:    conditions.DashboardsStatus,
			Status:  metav1.ConditionFalse,
			Reason:  conditions.Red,
			Message: "Dashboard status is red.",
		}
	default:
		return metav1.Condition{
			Type:    conditions.DashboardsStatus,
			Status:  metav1.ConditionUnknown,
			Reason:  conditions.Unknown,
			Message: "Dashboard status is unknown.",
		}
	}
}
