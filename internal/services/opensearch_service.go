package services

import (
	"context"
	"errors"
	"fmt"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"

	semverv3 "github.com/Masterminds/semver/v3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type openSearchService struct {
	passwordStoreService PasswordStoreService
	openSearchClient     clients.OpenSearchClient
}

const (
	openSearchAdmin = "admin"
)

func NewOpenSearchService(openSearchClient clients.OpenSearchClient, passwordStoreService PasswordStoreService) OpenSearchService {
	return &openSearchService{
		openSearchClient:     openSearchClient,
		passwordStoreService: passwordStoreService,
	}
}

// GetHealth fetches the health of the OpenSearch cluster.
func (s *openSearchService) GetHealth(ctx context.Context, instance *clv1beta1.Instance) (models.OSHealthResponse, error) {
	credentials, err := s.passwordStoreService.GetOpenSearchCredentials(ctx, instance, openSearchAdmin)
	if err != nil {
		return models.OSHealthResponse{}, err
	}

	return s.openSearchClient.GetHealth(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password))
}

// GetDiskUsage gets the current average disk usage on all data nodes (0.0 - 1.0).
func (s *openSearchService) GetDiskUsage(ctx context.Context, instance *clv1beta1.Instance) (float64, error) {
	credentials, err := s.passwordStoreService.GetOpenSearchCredentials(ctx, instance, openSearchAdmin)
	if err != nil {
		return 0, err
	}

	// fetch node stats for data nodes
	nodeStatsResponse, err := s.openSearchClient.GetNodeStats(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password), "data:true")
	if err != nil {
		return 0, err
	}

	var availableBytes int64
	var totalBytes int64
	for _, node := range nodeStatsResponse.Nodes {
		availableBytes += node.FS.Total.AvailableInBytes
		totalBytes += node.FS.Total.TotalInBytes
	}

	if totalBytes == 0 {
		return 0, errors.New("total disk space is 0")
	}

	return 1.0 - (float64(availableBytes) / float64(totalBytes)), nil
}

// GetStatusCondition returns the health condition for OpenSearch based on the health response
func (s *openSearchService) GetStatusCondition(ctx context.Context, instance *clv1beta1.Instance) metav1.Condition {
	response, err := s.GetHealth(ctx, instance)
	if err != nil {
		return metav1.Condition{
			Type:    conditions.OpenSearchHealth,
			Status:  metav1.ConditionFalse,
			Reason:  conditions.StatusError,
			Message: err.Error(),
		}
	}

	shardsInfo := fmt.Sprintf("active: %d, active primary: %d, initializing: %d, relocating: %d, unassigned: %d",
		response.ActiveShards, response.ActivePrimaryShards, response.InitializingShards, response.RelocatingShards,
		response.UnassignedShards)

	switch response.Status {
	case models.HealthStatusGreen:
		return metav1.Condition{
			Type:    conditions.OpenSearchHealth,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.Green,
			Message: fmt.Sprintf("Cluster health is green (%s)", shardsInfo),
		}
	case models.HealthStatusYellow:
		return metav1.Condition{
			Type:    conditions.OpenSearchHealth,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.Yellow,
			Message: fmt.Sprintf("Cluster health is yellow (%s)", shardsInfo),
		}
	case models.HealthStatusRed:
		return metav1.Condition{
			Type:    conditions.OpenSearchHealth,
			Status:  metav1.ConditionFalse,
			Reason:  conditions.Red,
			Message: fmt.Sprintf("Cluster health is red (%s)", shardsInfo),
		}
	default:
		return metav1.Condition{
			Type:    conditions.OpenSearchHealth,
			Status:  metav1.ConditionFalse,
			Reason:  conditions.Unknown,
			Message: fmt.Sprintf("Unkown cluster health status (%s)", response.Status),
		}
	}
}

// GetNumberOfNodesMatchingVersion returns the number of nodes that have the given version.
func (s *openSearchService) GetNumberOfNodesMatchingVersion(ctx context.Context, instance *clv1beta1.Instance, filter string, version *semverv3.Version) (int, error) {
	if version == nil {
		return 0, errors.New("version is nil")
	}

	credentials, err := s.passwordStoreService.GetOpenSearchCredentials(ctx, instance, openSearchAdmin)
	if err != nil {
		return 0, err
	}

	nodes, err := s.openSearchClient.GetNodeInfo(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password), filter)
	if err != nil {
		return 0, err
	}

	updatedNodes := 0
	for _, node := range nodes.Nodes {
		nodeVersion, err := semverv3.NewVersion(node.Version)
		if err != nil {
			return 0, fmt.Errorf("failed to node parse version '%s': %w", node.Version, err)
		}
		if nodeVersion.Equal(version) {
			updatedNodes++
		}
	}

	return updatedNodes, nil
}

// GetExcludedNodes returns the names of nodes that are currently excluded from routing allocation.
func (s *openSearchService) GetExcludedNodes(ctx context.Context, instance *clv1beta1.Instance) ([]string, error) {
	credentials, err := s.passwordStoreService.GetOpenSearchCredentials(ctx, instance, openSearchAdmin)
	if err != nil {
		return []string{}, err
	}

	return s.openSearchClient.GetExcludedNodes(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password))
}

// IsNodeRestartAllowed checks if the node can be restarted without risking a red cluster state.
// It returns false and a reason if:
// - There are shards with no stores.
// - There are yellow/red indices with shards present on no other node than the given node.
// Otherwise, it returns true.
func (s *openSearchService) IsNodeRestartAllowed(ctx context.Context, instance *clv1beta1.Instance, nodeName string) (bool, string, error) {
	credentials, err := s.passwordStoreService.GetOpenSearchCredentials(ctx, instance, openSearchAdmin)
	if err != nil {
		return false, "", err
	}

	shardStores, err := s.openSearchClient.GetShardStores(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password), "", "")
	if err != nil {
		return false, "", err
	}

	for indexName, index := range shardStores.Indices {
		for shardID, shard := range index.Shards {
			if len(shard.Stores) == 0 {
				return false, fmt.Sprintf("Shard %s of index '%s' has no stores", shardID, indexName), nil
			}

			isPresentOnOtherNode := false
			for _, store := range shard.Stores {
				if store.Name != nodeName && (store.Allocation == models.ShardStoreAllocationPrimary ||
					store.Allocation == models.ShardStoreAllocationReplica) {
					isPresentOnOtherNode = true
					break
				}
			}

			if !isPresentOnOtherNode {
				return false, fmt.Sprintf("Shard %s of index '%s' is not present on any other nodes", shardID, indexName), nil
			}
		}
	}

	return true, "", nil
}

// SetExcludedNodes excludes the given nodes from routing allocation.
func (s *openSearchService) SetExcludedNodes(ctx context.Context, instance *clv1beta1.Instance, nodeNames ...string) error {
	credentials, err := s.passwordStoreService.GetOpenSearchCredentials(ctx, instance, openSearchAdmin)
	if err != nil {
		return err
	}

	return s.openSearchClient.SetExcludedNodes(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password), nodeNames)
}

// IsEvacuationComplete checks if the OpenSearch cluster has finished evacuating a data node.
// It checks if the node has less than or equal documents as the .opendistro_security index, which has replicas on all nodes.
func (s *openSearchService) IsEvacuationComplete(ctx context.Context, instance *clv1beta1.Instance, nodeName string) (bool, error) {
	credentials, err := s.passwordStoreService.GetOpenSearchCredentials(ctx, instance, openSearchAdmin)
	if err != nil {
		return false, err
	}

	nodeStatsResponse, err := s.openSearchClient.GetNodeStats(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password), nodeName)
	if err != nil {
		return false, err
	}

	nodeStats, ok := s.filterNodeStatsByName(nodeStatsResponse, nodeName)
	if !ok {
		return false, fmt.Errorf("node %s not found in node stats response", nodeName)
	}

	odSecurityDocCount, err := s.openSearchClient.GetDocCountByIndex(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password), ".opendistro_security")
	if err != nil {
		return false, err
	}

	if odSecurityDocCount == 0 {
		return false, errors.New("no documents found in .opendistro_security index")
	}

	// Due to the replica settings for .opendistro_security, the respective shards and docs cannot be
	// removed from the evacuating node.
	return nodeStats.Indices.Docs.Count <= odSecurityDocCount, nil
}

// SetShardAllocation sets the shard allocation of the OpenSearch cluster.
// When it is disabled, the cluster will not allocate replicas for the primary shards.
func (s *openSearchService) SetShardAllocation(ctx context.Context, instance *clv1beta1.Instance, setting models.OSShardAllocation) error {
	credentials, err := s.passwordStoreService.GetOpenSearchCredentials(ctx, instance, openSearchAdmin)
	if err != nil {
		return err
	}

	return s.openSearchClient.SetShardAllocation(ctx, instance.Namespace, models.NewAuth(credentials.Username, credentials.Password), setting)
}

// filterNodeStatsByName filters the node stats by the given node name.
func (s *openSearchService) filterNodeStatsByName(nodeStats models.OSNodeStatsResponse, nodeName string) (*models.OSNodeStats, bool) {
	if nodeName == "" {
		return nil, false
	}

	for _, node := range nodeStats.Nodes {
		if node.Name == nodeName {
			return &node, true
		}
	}

	return nil, false
}
