package clients

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"strings"

	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"
)

type apiClient struct {
	httpClient http.Client
}

func NewAPIClient(httpClient http.Client) APIClient {
	return &apiClient{
		httpClient: httpClient,
	}
}

// Do sends an APIRequest and returns the response.
// Please see the comments in the APIRequest struct for more information on how to
// construct requests and the behavior of this method.
//
//nolint:funlen // Just a complex function
func (c *apiClient) Do(request *models.APIRequest) (models.APIResponse, error) {
	response := models.APIResponse{}

	if request == nil {
		return response, errors.New("request is nil")
	}

	if request.Header == nil {
		request.Header = make(http.Header)
	}

	// We enforce the context to be set
	if request.Context == nil {
		return response, errors.New("request is lacking a context")
	}

	// No need to continue if the context is canceled already
	if err := request.Context.Err(); err != nil {
		return response, fmt.Errorf("request canceled: %w", err)
	}

	// Marshal the request body source if set
	if request.RequestBodySource != nil {
		jsonBody, err := json.Marshal(request.RequestBodySource)
		if err != nil {
			return response, fmt.Errorf("failed to marshal request body from source: %w", err)
		}

		// If we have a JSON body, we set the content type accordingly
		request.Body = jsonBody
		request.Header.Set("Content-Type", "application/json")
	}

	// Create a reader for the request body if set
	var bodyReader io.Reader
	if request.Body != nil {
		bodyReader = bytes.NewReader(request.Body)
	}

	// Create a new HTTP request
	req, err := http.NewRequestWithContext(request.Context, request.Method, request.Origin+request.Path, bodyReader)
	if err != nil {
		return response, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header = request.Header

	// Set basic auth
	if request.Username != "" && request.Password != "" {
		req.SetBasicAuth(request.Username, request.Password)
	}

	// Send the request
	res, err := c.httpClient.Do(req)
	if err != nil {
		return response, fmt.Errorf("failed to send request: %w", err)
	}

	response.Header = res.Header
	response.StatusCode = res.StatusCode

	// Read the response body
	defer res.Body.Close()
	resBody := new(bytes.Buffer)
	if _, err = resBody.ReadFrom(res.Body); err != nil {
		return response, fmt.Errorf("failed to read response body: %w", err)
	}

	responseBody := resBody.Bytes()
	response.Body = responseBody

	// Check the status code
	if len(request.ExpectedStatusCodes) > 0 {
		statusCodeMatch := false
		for _, code := range request.ExpectedStatusCodes {
			if res.StatusCode == code {
				statusCodeMatch = true
				break
			}
		}

		if !statusCodeMatch {
			return response, fmt.Errorf("status code %d is not among expected status codes %v", res.StatusCode, request.ExpectedStatusCodes)
		}
	}

	// Optionally unmarshal the response body to the given ResponseBodyTarget
	if request.ResponseBodyTarget != nil {
		if reflect.ValueOf(request.ResponseBodyTarget).Kind() != reflect.Ptr {
			return response, errors.New("response body target must be a pointer")
		}

		if !c.hasContentTypeJSON(res.Header) {
			return response, errors.New("response content-type is not application/json")
		}

		err = json.Unmarshal(responseBody, request.ResponseBodyTarget)
		if err != nil {
			return response, fmt.Errorf("failed to unmarshal response body: %w", err)
		}
	}

	return response, nil
}

// hasContentTypeJSON checks if the given header has a JSON content type.
func (c *apiClient) hasContentTypeJSON(header http.Header) bool {
	for _, v := range header.Values("Content-Type") {
		if strings.Contains(v, "application/json") {
			return true
		}
	}
	return false
}
