package clients

import (
	"context"

	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"
)

// APIClient provides simplified access to APIs.
type APIClient interface {
	// Do sends an APIRequest and returns the response.
	// Please see the comments in the APIRequest struct for more information on how to
	// construct requests and the behavior of this method.
	Do(request *models.APIRequest) (models.APIResponse, error)
}

// InstanceMaintainerClient is an interface for accessing the instance-maintainer API.
type InstanceMaintainerClient interface {
	// GetStatus fetches the status from instance-maintainer API.
	GetStatus(ctx context.Context, namespace string) (models.InstanceMaintainerStatus, error)
}

// DashboardsClient is an interface for accessing the OpenSearch dashboards API.
type DashboardsClient interface {
	GetStatus(ctx context.Context, namespace string, auth models.Auth) (models.DashboardsStatusResponse, error)
}

// OpenSearchClient is an interface for accessing the OpenSearch API.
type OpenSearchClient interface {
	// GetHealth fetches the health of the OpenSearch cluster.
	GetHealth(ctx context.Context, openSearchEndpoint string, auth models.Auth) (models.OSHealthResponse, error)

	// GetNodeStats fetches the stats for all or some nodes.
	// Use the filter to specify the nodes to get stats for.
	// See https://opensearch.org/docs/latest/api-reference/nodes-apis/index/
	GetNodeStats(ctx context.Context, namespace string, auth models.Auth, filter string) (models.OSNodeStatsResponse, error)

	// GetNodeInfo fetches the info for all or some nodes.
	// Use the filter to specify the nodes to get info for.
	// See https://opensearch.org/docs/latest/api-reference/nodes-apis/index/
	GetNodeInfo(ctx context.Context, namespace string, auth models.Auth, filter string) (models.OSNodeInfoResponse, error)

	// GetDocCountByIndex fetches the document count for the given index.
	GetDocCountByIndex(ctx context.Context, namespace string, auth models.Auth, indexName string) (int64, error)

	// GetExcludedNodes fetches the names of the nodes that are currently excluded from routing allocation.
	GetExcludedNodes(ctx context.Context, namespace string, auth models.Auth) ([]string, error)

	// GetShardStores fetches the shard stores for the OpenSearch cluster.
	// The index and status parameters are optional.
	// If the status is empty, yellow and red indices are considered. ("yellow,red")
	// It defaults to all indices and the status being yellow or red.
	GetShardStores(ctx context.Context, namespace string, auth models.Auth, index string, status string) (models.OSShardStoresResponse, error)

	// SetShardAllocation sets the shard allocation setting for the OpenSearch cluster.
	SetShardAllocation(ctx context.Context, namespace string, auth models.Auth, setting models.OSShardAllocation) error

	// SetExcludedNodes excludes the given nodes from routing allocation.
	SetExcludedNodes(ctx context.Context, namespace string, auth models.Auth, nodeNames []string) error
}
