package clients_test

import (
	"context"
	"errors"
	"net/http"

	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test Dashboards client", func() {

	const namespace = "sf-test"

	var (
		apiClientMock    *fake.APIClientMock
		auth             models.Auth
		dashboardsClient clients.DashboardsClient
		ctx              = context.Background()
	)

	BeforeEach(func() {
		apiClientMock = fake.NewAPIClientMock()
		dashboardsClient = clients.NewDashboardsClient(apiClientMock)
		auth = models.Auth{
			Username: "admin",
			Password: "password",
		}
	})

	Context("GetStatus", func() {
		When("Dashboards is reachable and returns a status", func() {
			BeforeEach(func() {
				apiClientMock.PrepareJSONResponse(dashboardsStatusResponse, http.StatusOK, nil, nil)
			})

			It("sends a request to the Dashboards API", func() {
				_, err := dashboardsClient.GetStatus(ctx, namespace, auth)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(apiClientMock.IsDoCalled).Should(BeTrue())
				Expect(apiClientMock.Request.Method).Should(Equal(http.MethodGet))
				Expect(apiClientMock.Request.Origin).Should(Equal("http://dashboards-service.sf-test.svc.cluster.local:443"))
				Expect(apiClientMock.Request.Path).Should(Equal("/api/status"))
				Expect(apiClientMock.Request.ExpectedStatusCodes).Should(ConsistOf(http.StatusOK))
			})

			It("returns the status object", func() {
				status, err := dashboardsClient.GetStatus(ctx, namespace, auth)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(status).ShouldNot(BeNil())
				Expect(status.Name).Should(Equal("dashboards-deployment-xyz"))
				Expect(status.Status.Overall.State).Should(Equal("green"))
			})
		})

		When("an request error occurs", func() {
			BeforeEach(func() {
				apiClientMock.PrepareJSONResponse("{}", 0, nil, errors.New("request error"))
			})

			It("returns an error", func() {
				_, err := dashboardsClient.GetStatus(ctx, namespace, auth)

				Expect(err).Should(HaveOccurred())
				Expect(apiClientMock.IsDoCalled).Should(BeTrue())
			})
		})
	})
})

const dashboardsStatusResponse = `{
  "name": "dashboards-deployment-xyz",
  "uuid": "8392dacb-6490-4182-a8ef-67634ce3ba9e",
  "version": {
	"number": "1.3.20",
	"build_hash": "cde43100e4a6b955b420c3de55f0b57844631537",
	"build_number": 8139,
	"build_snapshot": false
  },
  "status": {
	"overall": {
	  "since": "2025-02-18T14:50:04.831Z",
	  "state": "green",
	  "title": "Green",
	  "nickname": "Looking good",
	  "icon": "success",
	  "uiColor": "secondary"
	}
  }
}`
