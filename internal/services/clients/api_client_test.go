package clients_test

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"time"

	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test APICLient", func() {
	var (
		apiClient  clients.APIClient
		testServer *httptest.Server
		ctx        = context.Background()
	)

	BeforeEach(func() {
		httpClient := http.Client{
			Timeout: 5 * time.Second,
		}
		apiClient = clients.NewAPIClient(httpClient)
	})

	Context("Do", func() {
		When("request is nil", func() {
			It("returns an error", func() {
				response, err := apiClient.Do(nil)

				Expect(err).Should(HaveOccurred())
				Expect(response).Should(BeZero())
			})
		})

		When("request is missing a context", func() {
			It("returns an error", func() {
				request := models.NewAPIRequest(ctx)
				request.Context = nil

				response, err := apiClient.Do(request)

				Expect(err).Should(HaveOccurred())
				Expect(err.Error()).Should(ContainSubstring("request is lacking a context"))
				Expect(response).Should(BeZero())
			})
		})

		When("the context is already canceled", func() {
			It("returns an error", func() {
				ctxWithCancel, cancel := context.WithCancel(ctx)
				cancel()

				request := models.NewAPIRequest(ctxWithCancel)
				response, err := apiClient.Do(request)

				Expect(err).Should(HaveOccurred())
				Expect(err.Error()).Should(ContainSubstring("request canceled"))
				Expect(response).Should(BeZero())
			})
		})

		When("a GET request is sent", func() {
			When("the server is not reachable", func() {
				It("returns an error", func() {
					request := models.NewAPIRequest(ctx).
						WithMethod(http.MethodGet).
						WithOrigin("http://unreachable-host")

					response, err := apiClient.Do(request)

					Expect(err).Should(HaveOccurred())
					Expect(err.Error()).Should(ContainSubstring("no such host"))
					Expect(response).Should(BeZero())
				})
			})

			When("the server responds with a JSON payload", func() {
				BeforeEach(func() {
					testServer = httptest.NewServer(http.HandlerFunc(
						func(w http.ResponseWriter, r *http.Request) {
							w.Header().Set("Content-Type", "application/json")
							w.WriteHeader(http.StatusOK)
							_, err := fmt.Fprintln(w, `{"key": "response value"}`)
							Expect(err).ShouldNot(HaveOccurred())
							Expect(r.Method).Should(Equal(http.MethodGet))
							Expect(r.URL.Path).Should(Equal("/test"))
						},
					))
				})

				AfterEach(func() {
					testServer.Close()
				})

				When("no response body target is set", func() {
					It("returns the response", func() {
						request := models.NewAPIRequest(ctx).
							WithMethod(http.MethodGet).
							WithOrigin(testServer.URL).
							WithPath("/test").
							WithExpectStatusCode(http.StatusOK)

						response, err := apiClient.Do(request)

						Expect(err).ShouldNot(HaveOccurred())
						Expect(response).ShouldNot(BeZero())
						Expect(response.StatusCode).Should(Equal(http.StatusOK))
						Expect(response.Body).ShouldNot(BeNil())
						Expect(response.Header.Get("Content-Type")).Should(Equal("application/json"))
						Expect(string(response.Body)).Should(MatchJSON(`{"key": "response value"}`))
					})
				})

				When("a response body target is set", func() {
					When("the response body target is a pointer (correct)", func() {
						It("unmarshals the response body into the target", func() {
							// use an anonymous struct as target for the response body
							targetStruct := struct {
								Key string `json:"key"`
							}{
								Key: "initial value",
							}

							request := models.NewAPIRequest(ctx).
								WithMethod(http.MethodGet).
								WithOrigin(testServer.URL).
								WithPath("/test").
								WithExpectStatusCode(http.StatusOK).
								WithResponseBodyTarget(&targetStruct)

							response, err := apiClient.Do(request)

							Expect(err).ShouldNot(HaveOccurred())
							Expect(response).ShouldNot(BeZero())
							Expect(string(response.Body)).Should(MatchJSON(`{"key": "response value"}`))
							Expect(targetStruct.Key).Should(Equal("response value"))
						})
					})

					When("the response body target is not a pointer (incorrect)", func() {
						It("returns an error", func() {
							// use an anonymous struct as target for the response body
							targetStruct := struct {
								Key string `json:"key"`
							}{
								Key: "initial value",
							}

							request := models.NewAPIRequest(ctx).
								WithMethod(http.MethodGet).
								WithOrigin(testServer.URL).
								WithPath("/test").
								WithExpectStatusCode(http.StatusOK).
								WithResponseBodyTarget(targetStruct) // not a pointer

							_, err := apiClient.Do(request)

							Expect(err).Should(HaveOccurred())
							Expect(err.Error()).Should(ContainSubstring("response body target must be a pointer"))
						})
					})
				})

				When("a different status code is expected", func() {
					It("returns an error", func() {
						request := models.NewAPIRequest(ctx).
							WithMethod(http.MethodGet).
							WithOrigin(testServer.URL).
							WithPath("/test").
							WithExpectStatusCode(http.StatusNotFound)

						response, err := apiClient.Do(request)

						Expect(err).Should(HaveOccurred())
						Expect(err.Error()).Should(ContainSubstring("status code 200 is not among expected status codes [404]"))
						Expect(response).ShouldNot(BeZero())
						Expect(response.StatusCode).Should(Equal(http.StatusOK))
						Expect(response.Body).ShouldNot(BeNil())
						Expect(response.Header.Get("Content-Type")).Should(Equal("application/json"))
						Expect(string(response.Body)).Should(MatchJSON(`{"key": "response value"}`))
					})
				})
			})

			When("the server responds with a different content-type", func() {
				BeforeEach(func() {
					testServer = httptest.NewServer(http.HandlerFunc(
						func(w http.ResponseWriter, _ *http.Request) {
							w.Header().Set("Content-Type", "text/plain")
							w.WriteHeader(http.StatusOK)
							_, err := fmt.Fprintln(w, "plain text response")
							Expect(err).ShouldNot(HaveOccurred())
						},
					))
				})

				AfterEach(func() {
					testServer.Close()
				})

				It("returns the response", func() {
					request := models.NewAPIRequest(ctx).
						WithMethod(http.MethodGet).
						WithOrigin(testServer.URL).
						WithExpectStatusCode(http.StatusOK)

					response, err := apiClient.Do(request)

					Expect(err).ShouldNot(HaveOccurred())
					Expect(response).ShouldNot(BeZero())
					Expect(response.StatusCode).Should(Equal(http.StatusOK))
					Expect(response.Body).ShouldNot(BeNil())
					Expect(response.Header.Get("Content-Type")).Should(Equal("text/plain"))
					Expect(string(response.Body)).Should(Equal("plain text response\n"))
				})

				It("does not unmarshal the response body", func() {
					// use an anonymous struct as target for the response body
					targetStruct := struct {
						Key string `json:"key"`
					}{
						Key: "initial value",
					}

					request := models.NewAPIRequest(ctx).
						WithMethod(http.MethodGet).
						WithOrigin(testServer.URL).
						WithExpectStatusCode(http.StatusOK).
						WithResponseBodyTarget(&targetStruct)

					response, err := apiClient.Do(request)
					Expect(err).Should(HaveOccurred())
					Expect(err.Error()).Should(ContainSubstring("response content-type is not application/json"))
					Expect(response).ShouldNot(BeZero())
					Expect(targetStruct.Key).Should(Equal("initial value"))
				})
			})

			When("the server responds with an error", func() {
				BeforeEach(func() {
					testServer = httptest.NewServer(http.HandlerFunc(
						func(w http.ResponseWriter, _ *http.Request) {
							w.Header().Set("Content-Type", "text/plain")
							w.WriteHeader(http.StatusInternalServerError)
							_, err := fmt.Fprintln(w, "internal server error")
							Expect(err).ShouldNot(HaveOccurred())
						},
					))
				})

				AfterEach(func() {
					testServer.Close()
				})

				When("no expected status codes is set", func() {
					It("does not return any error and returns the response", func() {
						request := models.NewAPIRequest(ctx).
							WithMethod(http.MethodGet).
							WithOrigin(testServer.URL)

						response, err := apiClient.Do(request)

						Expect(err).ShouldNot(HaveOccurred())
						Expect(response).ShouldNot(BeZero())
						Expect(response.StatusCode).Should(Equal(500))
						Expect(response.Body).ShouldNot(BeNil())
						Expect(response.Header.Get("Content-Type")).Should(Equal("text/plain"))
						Expect(string(response.Body)).Should(Equal("internal server error\n"))
					})
				})

				When("an expected status code is set", func() {
					It("returns an error and the response", func() {
						request := models.NewAPIRequest(ctx).
							WithMethod(http.MethodGet).
							WithOrigin(testServer.URL).
							WithExpectStatusCode(http.StatusOK)

						response, err := apiClient.Do(request)

						Expect(err).Should(HaveOccurred())
						Expect(err.Error()).Should(ContainSubstring("status code 500 is not among expected status codes [200]"))
						Expect(response).ShouldNot(BeZero())
						Expect(response.StatusCode).Should(Equal(500))
						Expect(response.Body).ShouldNot(BeNil())
						Expect(response.Header.Get("Content-Type")).Should(Equal("text/plain"))
						Expect(string(response.Body)).Should(Equal("internal server error\n"))
					})
				})
			})
		})

		When("a POST request is sent", func() {
			BeforeEach(func() {
				testServer = httptest.NewServer(http.HandlerFunc(
					func(w http.ResponseWriter, r *http.Request) {
						Expect(r.Method).Should(Equal(http.MethodPost))
						Expect(r.Header.Get("Content-Type")).Should(Equal("application/json"))
						Expect(r.URL.Path).Should(Equal("/test"))
						buf := new(strings.Builder)
						_, err := io.Copy(buf, r.Body)
						Expect(err).ShouldNot(HaveOccurred())
						Expect(buf.String()).Should(MatchJSON(`{"key": "request value"}`))

						w.Header().Set("Content-Type", "application/json")
						w.WriteHeader(http.StatusOK)
						_, err = fmt.Fprintln(w, `{"key": "response value"}`)
						Expect(err).ShouldNot(HaveOccurred())
					},
				))
			})

			AfterEach(func() {
				testServer.Close()
			})

			It("sends the a marshalled body source to the server", func() {
				sourceStruct := struct {
					Key string `json:"key"`
				}{
					Key: "request value",
				}

				request := models.NewAPIRequest(ctx).
					WithMethod(http.MethodPost).
					WithOrigin(testServer.URL).
					WithPath("/test").
					WithRequestBodySource(sourceStruct).
					WithExpectStatusCode(http.StatusOK)

				_, err := apiClient.Do(request)
				Expect(err).ShouldNot(HaveOccurred())
			})

			It("sends the a raw body to the server", func() {
				request := models.NewAPIRequest(ctx).
					WithMethod(http.MethodPost).
					WithOrigin(testServer.URL).
					WithPath("/test").
					WithBody([]byte(`{"key": "request value"}`)).
					WithHeaderField("Content-Type", "application/json").
					WithExpectStatusCode(http.StatusOK)

				_, err := apiClient.Do(request)
				Expect(err).ShouldNot(HaveOccurred())
			})
		})
	})
})
