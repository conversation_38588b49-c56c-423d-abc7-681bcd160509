package clients

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"
)

type openSearchClient struct {
	apiClient APIClient
}

func NewOpenSearchClient(apiClient APIClient) OpenSearchClient {
	return &openSearchClient{
		apiClient: apiClient,
	}
}

// GetHealth fetches the OpenSearch health status
func (c *openSearchClient) GetHealth(ctx context.Context, namespace string, auth models.Auth) (models.OSHealthResponse, error) {
	health := models.OSHealthResponse{}

	apiRequest := models.NewAPIRequest(ctx).
		WithMethod(http.MethodGet).
		WithOrigin(c.getOrigin(namespace)).
		WithPath("/_cluster/health").
		WithBasicAuth(auth.Username, auth.Password).
		WithExpectStatusCode(200).
		WithResponseBodyTarget(&health)

	if _, err := c.apiClient.Do(apiRequest); err != nil {
		return models.OSHealthResponse{}, fmt.Errorf("failed to get OpenSearch health: %w", err)
	}

	return health, nil
}

// GetNodeStats fetches the stats for all or some nodes.
// Use the filter to specify the nodes to get stats for.
// See https://opensearch.org/docs/latest/api-reference/nodes-apis/index/
func (c *openSearchClient) GetNodeStats(ctx context.Context, namespace string, auth models.Auth, filter string) (models.OSNodeStatsResponse, error) {
	var nodeStats models.OSNodeStatsResponse

	apiRequest := models.NewAPIRequest(ctx).
		WithMethod(http.MethodGet).
		WithOrigin(c.getOrigin(namespace)).
		WithPath(fmt.Sprintf("/_nodes/%s/stats", filter)).
		WithBasicAuth(auth.Username, auth.Password).
		WithExpectStatusCode(200).
		WithResponseBodyTarget(&nodeStats)

	if _, err := c.apiClient.Do(apiRequest); err != nil {
		return models.OSNodeStatsResponse{}, fmt.Errorf("failed to get node stats (filter '%s'): %w", filter, err)
	}

	return nodeStats, nil
}

// GetNodeInfo fetches the info for all or some nodes.
// Use the filter to specify the nodes to get info for.
// See https://opensearch.org/docs/latest/api-reference/nodes-apis/index/
func (c *openSearchClient) GetNodeInfo(ctx context.Context, namespace string, auth models.Auth, filter string) (models.OSNodeInfoResponse, error) {
	var nodeInfo models.OSNodeInfoResponse

	apiRequest := models.NewAPIRequest(ctx).
		WithMethod(http.MethodGet).
		WithOrigin(c.getOrigin(namespace)).
		WithPath(fmt.Sprintf("/_nodes/%s/infos", filter)).
		WithBasicAuth(auth.Username, auth.Password).
		WithExpectStatusCode(200).
		WithResponseBodyTarget(&nodeInfo)

	if _, err := c.apiClient.Do(apiRequest); err != nil {
		return models.OSNodeInfoResponse{}, fmt.Errorf("failed to get node info (filter '%s'): %w", filter, err)
	}

	return nodeInfo, nil
}

// GetDocCountByIndex fetches the document count for the given index
func (c *openSearchClient) GetDocCountByIndex(ctx context.Context, namespace string, auth models.Auth, index string) (int64, error) {
	var indexDocStats struct {
		All struct {
			Primaries struct {
				Docs struct {
					Count int64 `json:"count"`
				} `json:"docs"`
			} `json:"primaries"`
		} `json:"_all"`
	}

	apiRequest := models.NewAPIRequest(ctx).
		WithMethod(http.MethodGet).
		WithOrigin(c.getOrigin(namespace)).
		WithPath(fmt.Sprintf("/%s/_stats/docs?filter_path=_all.primaries.docs.count", index)).
		WithBasicAuth(auth.Username, auth.Password).
		WithExpectStatusCode(200).
		WithResponseBodyTarget(&indexDocStats)

	if _, err := c.apiClient.Do(apiRequest); err != nil {
		return 0, fmt.Errorf("failed to get document count for index '%s': %w", index, err)
	}

	return indexDocStats.All.Primaries.Docs.Count, nil
}

// GetExcludedNodes returns the names of the nodes that are excluded from routing allocation
func (c *openSearchClient) GetExcludedNodes(ctx context.Context, namespace string, auth models.Auth) ([]string, error) {
	var clusterSettings struct {
		Transient struct {
			Cluster struct {
				Routing struct {
					Allocation struct {
						Exclude struct {
							Name string `json:"_name"`
						} `json:"exclude"`
					} `json:"allocation"`
				} `json:"routing"`
			} `json:"cluster"`
		} `json:"transient"`
	}

	apiRequest := models.NewAPIRequest(ctx).
		WithMethod(http.MethodGet).
		WithOrigin(c.getOrigin(namespace)).
		WithPath("/_cluster/settings").
		WithBasicAuth(auth.Username, auth.Password).
		WithExpectStatusCode(200).
		WithResponseBodyTarget(&clusterSettings)

	if _, err := c.apiClient.Do(apiRequest); err != nil {
		return []string{}, fmt.Errorf("failed to get OpenSearch cluster settings: %w", err)
	}

	// split name into individual node names
	nodeNames := strings.Split(clusterSettings.Transient.Cluster.Routing.Allocation.Exclude.Name, ",")

	// unfortunately, the Split function returns a slice with one empty string if the input string is empty.
	if len(nodeNames) == 1 && len(nodeNames[0]) == 0 {
		nodeNames = []string{}
	}

	return nodeNames, nil
}

// GetShardStores fetches the shard stores of all or some indices
// Use the index to specify the indices to get shard stores for.
// Use the status to specify the status of the shards to get stores for.
// It defaults to all indices and the status being yellow or red.
func (c *openSearchClient) GetShardStores(ctx context.Context, namespace string, auth models.Auth, index string, status string) (models.OSShardStoresResponse, error) {
	var shardStoresRaw models.OSShardStoresResponseRaw
	if status == "" {
		status = "yellow,red"
	}

	if index == "" {
		index = "_all"
	}

	apiRequest := models.NewAPIRequest(ctx).
		WithMethod(http.MethodGet).
		WithOrigin(c.getOrigin(namespace)).
		WithPath(fmt.Sprintf("/%s/_shard_stores?status=%s", index, status)).
		WithBasicAuth(auth.Username, auth.Password).
		WithExpectStatusCode(200).
		WithResponseBodyTarget(&shardStoresRaw)

	if _, err := c.apiClient.Do(apiRequest); err != nil {
		return models.OSShardStoresResponse{}, fmt.Errorf("failed to get shard stores (index '%s', status '%s'): %w", index, status, err)
	}

	shardStores := models.OSShardStoresResponse{
		Indices: make(map[string]models.OSShardStoreIndex),
	}

	// Unfortunately the raw response cannot be used directly, as the shard store details reside in a field
	// with an unknown name (node id). Therefore, we need to parse the raw response manually.
	for indexName, indexRaw := range shardStoresRaw.Indices {
		index := models.OSShardStoreIndex{
			Shards: make(map[string]models.OSShardStoreShard),
		}

		for shardName, shardRaw := range indexRaw.Shards {
			shard := models.OSShardStoreShard{
				Stores: make([]models.OSShardStore, 0),
			}

			for _, store := range shardRaw.Stores {
				var name = ""
				for _, value := range store {
					if storeDetails, ok := value.(map[string]interface{}); ok {
						if storeDetails["name"] != nil {
							name, ok = storeDetails["name"].(string)
							if !ok {
								return models.OSShardStoresResponse{}, errors.New("failed to cast shard store name to string")
							}
							break
						}
					}
				}

				if name == "" {
					return models.OSShardStoresResponse{}, errors.New("failed to determine shard store name")
				}

				allocation, ok := store["allocation"].(string)
				if !ok {
					return models.OSShardStoresResponse{}, errors.New("failed to determine shard store allocation")
				}

				shard.Stores = append(shard.Stores, models.OSShardStore{
					Name:       name,
					Allocation: models.OSShardStoreAllocation(allocation),
				})
			}
			index.Shards[shardName] = shard
		}
		shardStores.Indices[indexName] = index
	}

	return shardStores, nil
}

// SetShardAllocation sets the OpenSearch shard allocation setting
func (c *openSearchClient) SetShardAllocation(ctx context.Context, namespace string, auth models.Auth, setting models.OSShardAllocation) error {
	settings := map[string]interface{}{
		"persistent": map[string]interface{}{
			"cluster.routing.allocation.enable": setting,
		},
	}

	apiRequest := models.NewAPIRequest(ctx).
		WithMethod(http.MethodPut).
		WithOrigin(c.getOrigin(namespace)).
		WithPath("/_cluster/settings").
		WithBasicAuth(auth.Username, auth.Password).
		WithExpectStatusCode(200).
		WithRequestBodySource(settings)

	if _, err := c.apiClient.Do(apiRequest); err != nil {
		return fmt.Errorf("failed to set OpenSearch shard allocation: %w", err)
	}

	return nil
}

// SetExcludedNodes excludes the given nodes from routing allocation
func (c *openSearchClient) SetExcludedNodes(ctx context.Context, namespace string, auth models.Auth, nodeNames []string) error {
	nodes := strings.Join(nodeNames, ",")
	settings := map[string]interface{}{
		"transient": map[string]interface{}{
			"cluster.routing.allocation.exclude._name": nodes,
		},
	}

	apiRequest := models.NewAPIRequest(ctx).
		WithMethod(http.MethodPut).
		WithOrigin(c.getOrigin(namespace)).
		WithPath("/_cluster/settings").
		WithBasicAuth(auth.Username, auth.Password).
		WithExpectStatusCode(200).
		WithRequestBodySource(settings)

	if _, err := c.apiClient.Do(apiRequest); err != nil {
		return fmt.Errorf("failed to exclude node(s) '%s' from OpenSearch cluster: %w", nodes, err)
	}

	return nil
}

// getOrigin returns the protocol and host for OpenSearch in the given namespace.
func (c *openSearchClient) getOrigin(namespace string) string {
	return fmt.Sprintf("https://opensearch-client.%s.svc.cluster.local:9200", namespace)
}
