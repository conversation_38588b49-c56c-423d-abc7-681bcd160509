package clients

import (
	"context"
	"fmt"
	"net/http"

	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"
)

type dashboardsClient struct {
	apiClient APIClient
}

func NewDashboardsClient(apiClient APIClient) DashboardsClient {
	return &dashboardsClient{
		apiClient: apiClient,
	}
}

func (c *dashboardsClient) GetStatus(ctx context.Context, namespace string, auth models.Auth) (models.DashboardsStatusResponse, error) {
	dashboardsHost := c.getOrigin(namespace)
	dashboardsStatus := models.DashboardsStatusResponse{}

	request := models.NewAPIRequest(ctx).
		WithMethod(http.MethodGet).
		WithOrigin(dashboardsHost).
		WithPath("/api/status").
		WithBasicAuth(auth.Username, auth.Password).
		WithExpectStatusCode(http.StatusOK).
		WithResponseBodyTarget(&dashboardsStatus)

	if _, err := c.apiClient.Do(request); err != nil {
		return dashboardsStatus, fmt.Errorf("failed to get status: %w", err)
	}

	return dashboardsStatus, nil
}

func (c *dashboardsClient) getOrigin(namespace string) string {
	return fmt.Sprintf("http://dashboards-service.%s.svc.cluster.local:443", namespace)
}
