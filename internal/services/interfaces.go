package services

import (
	"context"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	clientmodels "github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"

	semverv3 "github.com/Masterminds/semver/v3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// PasswordStoreService is an interface for interacting with the password-store secret.
type PasswordStoreService interface {
	// GetSecretName returns the name of the password-store secret.
	GetSecretName() string

	// GetOpenSearchCredentials returns the credentials for the given username from the password-store secret.
	GetOpenSearchCredentials(ctx context.Context, instance *clv1beta1.Instance, username string) (models.Credentials, error)

	// GetAllOpenSearchCredentials returns all OpenSearch credentials from the password-store secret.
	GetAllOpenSearchCredentials(ctx context.Context, instance *clv1beta1.Instance) (map[string]models.Credentials, error)
}

// StatusService is an interface that services can implement to provide status conditions for components.
//
//nolint:iface // This interface might receive further methods in the future
type StatusService interface {
	// GetStatusCondition returns the status condition of the component for the given instance.
	GetStatusCondition(ctx context.Context, instance *clv1beta1.Instance) metav1.Condition
}

// InstanceMaintainerService is an interface for accessing instance-maintainer features.
//
//nolint:iface // This interface might receive further methods in the future.
type InstanceMaintainerService interface {
	StatusService
}

// DashboardsService is an interface for accessing OpenSearch dashboards features.
//
//nolint:iface // This interface might receive further methods in the future.
type DashboardsService interface {
	StatusService
}

// OpenSearchService is an interface for interacting with an OpenSearch cluster.
type OpenSearchService interface {
	StatusService

	// GetHealth fetches the health of the OpenSearch cluster.
	GetHealth(ctx context.Context, instance *clv1beta1.Instance) (clientmodels.OSHealthResponse, error)

	// GetDiskUsage fetches the average disk usage of the OpenSearch data nodes in the cluster (0.0 - 1.0).
	GetDiskUsage(ctx context.Context, instance *clv1beta1.Instance) (float64, error)

	// GetExcludedNodes returns the names of the nodes that are currently excluded from routing allocation.
	GetExcludedNodes(ctx context.Context, instance *clv1beta1.Instance) ([]string, error)

	// GetNumberOfNodesMachingVersion returns the number of nodes that have the given version.
	GetNumberOfNodesMatchingVersion(ctx context.Context, instance *clv1beta1.Instance, filter string, version *semverv3.Version) (int, error)

	// SetExcludedNodes excludes the given nodes from routing allocation.
	SetExcludedNodes(ctx context.Context, instance *clv1beta1.Instance, nodeNames ...string) error

	// IsNodeRestartAllowed checks if the node can be restarted without risking a red cluster state.
	IsNodeRestartAllowed(ctx context.Context, instance *clv1beta1.Instance, nodeName string) (bool, string, error)

	// IsEvacuationComplete checks if the OpenSearch cluster has finished evacuating a data node.
	IsEvacuationComplete(ctx context.Context, instance *clv1beta1.Instance, nodeName string) (bool, error)

	// SetShardAllocation sets the shard allocation of the OpenSearch cluster.
	SetShardAllocation(ctx context.Context, instance *clv1beta1.Instance, setting clientmodels.OSShardAllocation) error
}

// OpenSearchTopologyService is an interface for discovering the topology of an OpenSearch cluster based on StatefulSets.
type OpenSearchTopologyService interface {
	// Discover checks the namespace of the given namespace for StatefulSets belonging to an OpenSearch cluster
	Discover(ctx context.Context, instance *clv1beta1.Instance) (*models.Topology, error)

	// GetNumberOfPVCs returns the number of PVCs with the given name prefix
	GetNumberOfPVCs(ctx context.Context, instance *clv1beta1.Instance, prefix string) (int32, error)
}

// FeatureConfiguration is an interface for checking feature flags set for instances.
type FeatureConfiguration interface {
	// IsFeatureSet checks if the given feature is set for the given instance.
	IsFeatureSet(instance *clv1beta1.Instance, feature string) bool

	// IsFeatureAllowed checks if the given feature is allowed for the given instance.
	IsFeatureAllowed(instance *clv1beta1.Instance, feature string) (bool, string)

	// GetSupportedFeatureFlags returns the supported feature flags.
	GetSupportedFeatureFlags() []string
}

// TimeProvider is an interface to provide the current time.
type TimeProvider interface {
	Now() time.Time
}
