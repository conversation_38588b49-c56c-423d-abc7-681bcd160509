package models

import (
	"fmt"
	"sort"

	clbetav1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	policyv1 "k8s.io/api/policy/v1"
	"k8s.io/utils/ptr"
)

type Topology struct {
	NodeGroups []NodeGroup
}

type TopologyPatch struct {
	Topology         *Topology
	AbsentNodeGroups []NodeGroup
	TargetImage      utils.VersionTuple
}

type NodeGroup struct {
	StatefulSet         *appsv1.StatefulSet
	HeadlessService     *corev1.Service
	PodDisruptionBudget *policyv1.PodDisruptionBudget
	NodePool            *clbetav1.NodePool
}

type TopologySortOrder string

const (
	SortOrderNormal  TopologySortOrder = "normal"
	SortOrderUpgrade TopologySortOrder = "upgrade"
)

const (
	labelClusterManager = "cluster-manager"
	labelData           = "data"
	labelRole           = "role"
)

// String returns a string representation of the Topology struct
func (t *Topology) String() string {
	str := "Topology:\n"
	for id, nodeGroup := range t.NodeGroups {
		str += fmt.Sprintf("  Group %d\n", id)
		str += fmt.Sprintf("    StatefulSet: %s\n", nodeGroup.StatefulSet.Name)
		if nodeGroup.HeadlessService != nil {
			str += fmt.Sprintf("    Headless Service: %s\n", nodeGroup.HeadlessService.Name)
		}
		if nodeGroup.PodDisruptionBudget != nil {
			str += fmt.Sprintf("    PodDisruptionBudget: %s\n", nodeGroup.PodDisruptionBudget.Name)
		}
		str += fmt.Sprintf("    Replicas: %d\n", *nodeGroup.StatefulSet.Spec.Replicas)
		str += fmt.Sprintf("    IsClusterManager: %v\n", nodeGroup.IsClusterManager())
		str += fmt.Sprintf("    IsData: %v\n", nodeGroup.IsData())
	}

	return str
}

// IsData returns true if the StatefulSet has the data label set to true
func (t *NodeGroup) IsData() bool {
	val, ok := t.StatefulSet.Labels[labelData]
	if ok {
		return val == "true"
	}
	return false
}

// IsClusterManager returns true if the StatefulSet has the cluster-manager label set to true
func (t *NodeGroup) IsClusterManager() bool {
	val, ok := t.StatefulSet.Labels[labelClusterManager]
	if ok {
		return val == "true"
	}
	return false
}

// IsSingleNode returns true if the NodeGroup is a single node
// It is based on the associated NodePool's min and max replicas and not on the actual number of replicas
func (t *NodeGroup) IsSingleNode() bool {
	if t.NodePool == nil {
		return false
	}
	return t.NodePool.Autoscaler.MinReplicas == 1 && t.NodePool.Autoscaler.MaxReplicas == 1
}

// GetSortIndex returns the sort index of the node pool instance based on its roles:
// 1. pools with cluster_manager role but not data
// 2. pools with cluster_manager and data role
// 3. pools with data role but not cluster_manager role
// 4. pools without data and cluster_manager role
func (t *NodeGroup) GetSortIndex() int {
	if t.IsClusterManager() {
		if t.IsData() {
			return 2 // cluster_manager and data
		}
		return 1 // cluster_manager and not data
	}
	if t.IsData() {
		return 3 // data and not cluster_manager
	}
	return 4 // neither data nor cluster_manager
}

// GetStatefulSetPodName returns the name of the associated StatefulSet
func (t *NodeGroup) GetStatefulSetPodName(index int32) string {
	if t.StatefulSet == nil {
		return ""
	}
	return fmt.Sprintf("%s-%d", t.StatefulSet.Name, index)
}

// IsSingleNodeCluster returns true if the topology only contains a single node group with a single node
// It is based on the associated NodePool's min and max replicas and not on the actual number of replicas
func (t *Topology) IsSingleNodeCluster() bool {
	if len(t.NodeGroups) == 1 {
		return t.NodeGroups[0].IsSingleNode()
	}
	return false
}

// FindNodeGroupByStsName returns the NodeGroup with the given StatefulSet name
func (t *Topology) FindNodeGroupByStsName(name string) *NodeGroup {
	for _, nodeGroup := range t.NodeGroups {
		if nodeGroup.StatefulSet.Name == name {
			return &nodeGroup
		}
	}
	return nil
}

// GetClusterManagerPodNames returns the names of the pods that are cluster managers
func (t *Topology) GetClusterManagerPodNames() []string {
	var names []string
	for _, nodeGroup := range t.NodeGroups {
		if nodeGroup.IsClusterManager() {
			replicas := int(*nodeGroup.StatefulSet.Spec.Replicas)
			for i := range replicas {
				names = append(names, fmt.Sprintf("%s-%d", nodeGroup.StatefulSet.Name, i))
			}
		}
	}
	return names
}

// GetNumberOfDataNodes returns the number of data nodes in the topology
func (t *Topology) GetNumberOfDataNodes() int32 {
	var count int32
	for _, nodeGroup := range t.NodeGroups {
		if nodeGroup.IsData() {
			count += ptr.Deref(nodeGroup.StatefulSet.Spec.Replicas, 0)
		}
	}
	return count
}

// GetMinNumberOfDataNodes returns the minimum number of data nodes in the topology
func (t *Topology) GetMinNumberOfDataNodes() int32 {
	var count int32
	for _, nodeGroup := range t.NodeGroups {
		if nodeGroup.IsData() {
			count += nodeGroup.NodePool.Autoscaler.MinReplicas
		}
	}
	return count
}

// GetOpenSearchImages returns a list of unique images from the topology
func (t *Topology) GetOpenSearchImages() []string {
	imageSet := make(map[string]struct{})
	for _, nodeGroup := range t.NodeGroups {
		if nodeGroup.StatefulSet == nil {
			continue
		}

		image, ok := utils.GetImageFromContainer(nodeGroup.StatefulSet.Spec.Template.Spec.Containers, "opensearch")
		if ok {
			imageSet[image] = struct{}{}
		}
	}

	images := []string{}
	for image := range imageSet {
		images = append(images, image)
	}
	return images
}

// Sort by OpenSearch roles as follows:
// Normal:
// 1. cluster_manager and not data
// 2. cluster_manager and data
// 3. data and not cluster_manager
// 4. neither data nor cluster_manager
// Upgrade:
// 1. neither data nor cluster_manager
// 2. data and not cluster_manager
// 3. cluster_manager and data
// 4. cluster_manager and not data
func (t *Topology) Sort(order TopologySortOrder) {
	sort.Slice(t.NodeGroups, func(i, j int) bool {
		if order == SortOrderUpgrade {
			return t.NodeGroups[i].GetSortIndex() > t.NodeGroups[j].GetSortIndex()
		}
		return t.NodeGroups[i].GetSortIndex() < t.NodeGroups[j].GetSortIndex()
	})
}
