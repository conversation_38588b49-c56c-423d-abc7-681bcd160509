package services

import (
	"encoding/json"
	"fmt"
	"regexp"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"

	"github.com/go-logr/logr"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

const ProdClusterNameRegex = "^(cls-[0-9]+|[0-9]+)$"

type featureConfiguration struct {
	logger                  logr.Logger
	prodClusterNameRegex    *regexp.Regexp
	allowedProdFeatureFlags []string
	supportedFeatureFlags   []string
}

func NewFeatureConfiguration(supportedFeatureFlags []string, allowedProdFeatureFlags []string) FeatureConfiguration {
	l := log.Log.WithName("feature-configuration")

	fc := &featureConfiguration{
		logger:                  l,
		prodClusterNameRegex:    regexp.MustCompile(ProdClusterNameRegex),
		allowedProdFeatureFlags: allowedProdFeatureFlags,
		supportedFeatureFlags:   supportedFeatureFlags,
	}

	l.Info("Supported feature flags", "flags", fc.supportedFeatureFlags)
	l.Info("Allowed production feature flags", "flags", fc.allowedProdFeatureFlags)

	return fc
}

func (c *featureConfiguration) IsFeatureSet(instance *clv1beta1.Instance, feature string) bool {
	instanceFeatureFlagsStr := instance.Spec.FeatureFlags

	instanceFeatureFlags, err := c.parseFeatureFlags(instanceFeatureFlagsStr)
	if err != nil {
		c.logger.Error(err, "Failed to parse feature flags", "instanceFeatureFlagsStr", instanceFeatureFlagsStr)
		return false
	}

	if c.containsFeature(instanceFeatureFlags, feature) {
		if allowed, reason := c.IsFeatureAllowed(instance, feature); !allowed {
			c.logger.Info("Feature is not allowed", "feature", feature, "reason", reason)
			return false
		}
		return true
	}

	return false
}

// IsFeatureAllowed checks if a feature is allowed for the given instance.
// It allows all supported feature flags for non-prod instances.
// For prod instances, it also checks if the feature is in the allowed production feature flags.
func (c *featureConfiguration) IsFeatureAllowed(instance *clv1beta1.Instance, feature string) (bool, string) {
	if !c.prodClusterNameRegex.MatchString(instance.Spec.Context.ClusterName) {
		if c.containsFeature(c.supportedFeatureFlags, feature) {
			return true, ""
		}

		return false, fmt.Sprintf("Feature '%s' is not supported", feature)
	}

	if c.containsFeature(c.supportedFeatureFlags, feature) {
		if c.containsFeature(c.allowedProdFeatureFlags, feature) {
			return true, ""
		}

		return false, fmt.Sprintf("Feature '%s' is not allowed in production", feature)
	}

	return false, fmt.Sprintf("Feature '%s' is not supported", feature)
}

func (c *featureConfiguration) GetSupportedFeatureFlags() []string {
	return c.supportedFeatureFlags
}

func (c *featureConfiguration) parseFeatureFlags(data string) ([]string, error) {
	if len(data) == 0 {
		return []string{}, nil
	}

	featureFlags := make([]string, 0)
	err := json.Unmarshal([]byte(data), &featureFlags)

	return featureFlags, err
}

func (c *featureConfiguration) containsFeature(featureFlags []string, feature string) bool {
	for _, flag := range featureFlags {
		if flag == feature {
			return true
		}
	}

	return false
}
