package services

import (
	"context"
	"fmt"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const passwordStoreSecretName = "passwords-store"

type passwordStoreService struct {
	client client.Client
}

// NewPasswordStoreService creates a new password-store service.
func NewPasswordStoreService(client client.Client) PasswordStoreService {
	return &passwordStoreService{
		client: client,
	}
}

// GetSecretName returns the name of the password-store secret.
func (r *passwordStoreService) GetSecretName() string {
	return passwordStoreSecretName
}

// GetOpenSearchCredentials returns the OpenSearch credentials for the given username from the password-store secret.
func (r *passwordStoreService) GetOpenSearchCredentials(ctx context.Context, instance *clv1beta1.Instance, username string) (models.Credentials, error) {
	credentials := models.Credentials{
		Username: username,
	}

	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      passwordStoreSecretName,
			Namespace: instance.Namespace,
		},
	}

	err := r.client.Get(ctx, client.ObjectKeyFromObject(secret), secret)
	if err != nil {
		return credentials, fmt.Errorf("failed to get password-store secret: %w", err)
	}

	password := secret.Data[username]
	credentials.Password = string(password)
	hash := secret.Data[fmt.Sprintf("%sHash", username)]
	credentials.Hash = string(hash)
	return credentials, nil
}

// GetAllOpenSearchCredentials returns all OpenSearch credentials from the password-store secret.
func (r *passwordStoreService) GetAllOpenSearchCredentials(ctx context.Context, instance *clv1beta1.Instance) (map[string]models.Credentials, error) {
	allCredentials := make(map[string]models.Credentials)

	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      passwordStoreSecretName,
			Namespace: instance.Namespace,
		},
	}

	err := r.client.Get(ctx, client.ObjectKeyFromObject(secret), secret)
	if err != nil {
		return allCredentials, fmt.Errorf("failed to get password-store secret: %w", err)
	}

	// TODO: Check options for improving the structure of the secret
	// Storing hashes in a "<username>Hash" field is not optimal, it
	// requires the logic below. One option would be to store the
	// passwords and hashes in separate secrets.
	for key, value := range secret.Data {
		if len(key) > 4 && key[len(key)-4:] == "Hash" {
			username := key[:len(key)-4]
			password := secret.Data[username]
			hash := value
			allCredentials[username] = models.Credentials{
				Username: username,
				Password: string(password),
				Hash:     string(hash),
			}
		}
	}
	return allCredentials, nil
}
