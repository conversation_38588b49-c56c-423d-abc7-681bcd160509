package services_test

import (
	"context"
	"errors"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"
	serviceModels "github.tools.sap/perfx/cloud-logging-operator/internal/services/models"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test the dashboards service ", func() {
	var (
		passwordStoreServiceMock *fake.PasswordStoreServiceMock
		dashboardsClientMock     *fake.DashboardsClientMock
		dashboardsService        services.DashboardsService
		instance                 *clv1beta1.Instance
		ctx                      = context.Background()
	)

	BeforeEach(func() {
		passwordStoreServiceMock = fake.NewPasswordStoreServiceMock()
		dashboardsClientMock = fake.NewDashboardsClientMock()
		dashboardsService = services.NewDashboardsService(dashboardsClientMock, passwordStoreServiceMock)

		passwordStoreServiceMock.PrepareOpenSearchCredentials(serviceModels.Credentials{
			Username: "kibanaserver",
			Password: "password",
			Hash:     "some-hash",
		})

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "instance-test",
				Namespace: "sf-test",
			},
			Spec: clv1beta1.InstanceSpec{},
		}
	})

	Context("GetStatusCondition", func() {
		When("the client returns green state", func() {
			It("returns the expected green condition object", func() {
				preparedResponse := models.DashboardsStatusResponse{
					Status: models.DashboardStatus{
						Overall: models.StatusOverall{
							State: "green",
						},
					},
				}
				dashboardsClientMock.PrepareStatus(preparedResponse)

				condition := dashboardsService.GetStatusCondition(ctx, instance)
				Expect(condition.Type).To(Equal(conditions.DashboardsStatus))
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Green))
				Expect(dashboardsClientMock.IsGetStatusCalled).To(Equal(true))
				Expect(dashboardsClientMock.Namespace).To(Equal("sf-test"))
			})
		})

		When("the client returns yellow state", func() {
			It("returns the expected yellow condition object", func() {
				preparedResponse := models.DashboardsStatusResponse{
					Status: models.DashboardStatus{
						Overall: models.StatusOverall{
							State: "yellow",
						},
					},
				}
				dashboardsClientMock.PrepareStatus(preparedResponse)

				condition := dashboardsService.GetStatusCondition(ctx, instance)
				Expect(condition.Type).To(Equal(conditions.DashboardsStatus))
				Expect(condition.Status).To(Equal(metav1.ConditionTrue))
				Expect(condition.Reason).To(Equal(conditions.Yellow))
				Expect(dashboardsClientMock.IsGetStatusCalled).To(Equal(true))
				Expect(dashboardsClientMock.Namespace).To(Equal("sf-test"))
			})
		})

		When("the client returns red state", func() {
			It("returns the expected red condition object", func() {
				preparedResponse := models.DashboardsStatusResponse{
					Status: models.DashboardStatus{
						Overall: models.StatusOverall{
							State: "red",
						},
					},
				}
				dashboardsClientMock.PrepareStatus(preparedResponse)

				condition := dashboardsService.GetStatusCondition(ctx, instance)
				Expect(condition.Type).To(Equal(conditions.DashboardsStatus))
				Expect(condition.Status).To(Equal(metav1.ConditionFalse))
				Expect(condition.Reason).To(Equal(conditions.Red))
				Expect(dashboardsClientMock.IsGetStatusCalled).To(Equal(true))
				Expect(dashboardsClientMock.Namespace).To(Equal("sf-test"))
			})
		})

		When("the client returns an error", func() {
			It("returns error condition", func() {
				err := errors.New("some error")
				dashboardsClientMock.PrepareError(err)

				condition := dashboardsService.GetStatusCondition(ctx, instance)
				Expect(condition.Type).To(Equal(conditions.DashboardsStatus))
				Expect(condition.Status).To(Equal(metav1.ConditionFalse))
				Expect(condition.Reason).To(Equal(conditions.StatusError))
				Expect(condition.Message).To(Equal(err.Error()))
			})
		})
	})
})
