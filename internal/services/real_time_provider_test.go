package services_test

import (
	"time"

	"github.tools.sap/perfx/cloud-logging-operator/internal/services"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("RealTimeProvider", func() {
	var (
		timeProvider services.TimeProvider
	)

	BeforeEach(func() {
		timeProvider = services.NewRealTimeProvider()
	})

	Context("Now()", func() {
		It("should return the current time", func() {
			now := timeProvider.Now()
			Expect(now).To(BeTemporally("~", time.Now(), time.Second))
		})
	})
})
