package fake

import (
	"context"

	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"
)

type DashboardsClientMock struct {
	Namespace         string
	IsGetStatusCalled bool
	Auth              models.Auth
	status            models.DashboardsStatusResponse
	err               error
}

func NewDashboardsClientMock() *DashboardsClientMock {
	return &DashboardsClientMock{}
}

func (d *DashboardsClientMock) PrepareStatus(status models.DashboardsStatusResponse) {
	d.status = status
}

func (d *DashboardsClientMock) PrepareError(err error) {
	d.err = err
}

func (d *DashboardsClientMock) GetStatus(_ context.Context, namespace string, auth models.Auth) (models.DashboardsStatusResponse, error) {
	d.Namespace = namespace
	d.IsGetStatusCalled = true
	d.Auth = auth
	return d.status, d.err
}
