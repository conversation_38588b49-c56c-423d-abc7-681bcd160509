package fake

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"

	semverv3 "github.com/Masterminds/semver/v3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type OpenSearchServiceMock struct {
	IsGetHealthCalled                       bool
	IsGetDiskUsageCalled                    bool
	IsGetStatusConditionCalled              bool
	IsGetNumberOfNodesMatchingVersionCalled bool
	GetNumberOfNodesMatchingVersionVersion  *semverv3.Version
	GetNumberOfNodesMatchingVersionFilter   string
	IsIsEvacuationCompleteCalled            bool
	IsEvacuationCompleteNode                string
	IsSetExcludedNodesCalled                bool
	SectExcludedNodeNames                   []string
	IsIsNodeRestartAllowedCalled            bool
	IsNodeRestartAllowedNodeName            string
	IsGetExcludedNodesCalled                bool
	IsSetShardAllocationCalled              bool
	SetShardAllocationSetting               models.OSShardAllocation

	getHealthResponse              models.OSHealthResponse
	getHealthErr                   error
	diskUsage                      float64
	diskUsageErr                   error
	numberOfUpdatedInstances       int
	getNumberOfUpdatedInstancesErr error
	statusCondition                metav1.Condition
	isEvacuationComplete           bool
	isEvacuationCompleteErr        error
	isNodeRestartAllowed           bool
	isNodeRestartAllowedReason     string
	isNodeRestartAllowedErr        error
	setExcludedNodesErr            error
	getExcludedNodeNames           []string
	getExcludedNodesErr            error
	SetShardAllocationErr          error
}

func NewOpenSearchServiceMock() *OpenSearchServiceMock {
	return &OpenSearchServiceMock{}
}

// PrepareGetHealth prepares the response for GetHealth
func (m *OpenSearchServiceMock) PrepareGetHealth(healthResponse models.OSHealthResponse, err error) {
	m.getHealthResponse = healthResponse
	m.getHealthErr = err
}

// PrepareGetDiskUsage prepares the response for GetDiskUsage
func (m *OpenSearchServiceMock) PrepareGetDiskUsage(diskUsage float64, err error) {
	m.diskUsage = diskUsage
	m.diskUsageErr = err
}

// PrepareGetNumberOfNodesMatchingVersion prepares the response for GetNumberOfNodesMatchingVersion
func (m *OpenSearchServiceMock) PrepareGetNumberOfNodesMatchingVersion(numberOfUpdatedInstances int, err error) {
	m.numberOfUpdatedInstances = numberOfUpdatedInstances
	m.getNumberOfUpdatedInstancesErr = err
}

// PrepareGetStatusCondition prepares the response for GetStatusCondition
func (m *OpenSearchServiceMock) PrepareGetStatusCondition(statusCondition metav1.Condition) {
	m.statusCondition = statusCondition
}

// PrepareIsEvacuationComplete prepares the response for IsEvacuationComplete
func (m *OpenSearchServiceMock) PrepareIsEvacuationComplete(isEvacuationComplete bool, err error) {
	m.isEvacuationComplete = isEvacuationComplete
	m.isEvacuationCompleteErr = err
}

// PrepareIsNodeRestartAllowed prepares the response for IsNodeRestartAllowed
func (m *OpenSearchServiceMock) PrepareIsNodeRestartAllowed(isNodeRestartAllowed bool, reason string, err error) {
	m.isNodeRestartAllowed = isNodeRestartAllowed
	m.isNodeRestartAllowedReason = reason
	m.isNodeRestartAllowedErr = err
}

// PrepareSetExcludedNodes prepares the response for SetExcludedNodes
func (m *OpenSearchServiceMock) PrepareSetExcludedNodes(err error) {
	m.setExcludedNodesErr = err
}

// PrepareGetExcludedNodes prepares the response for GetExcludedNodes
func (m *OpenSearchServiceMock) PrepareGetExcludedNodes(nodeNames []string, err error) {
	m.getExcludedNodeNames = nodeNames
	m.getExcludedNodesErr = err
}

// PrepareSetShardAllocation prepares the response for SetShardAllocation
func (m *OpenSearchServiceMock) PrepareSetShardAllocation(err error) {
	m.SetShardAllocationErr = err
}

func (m *OpenSearchServiceMock) GetHealth(_ context.Context, _ *clv1beta1.Instance) (models.OSHealthResponse, error) {
	m.IsGetHealthCalled = true
	return m.getHealthResponse, m.getHealthErr
}

func (m *OpenSearchServiceMock) GetDiskUsage(_ context.Context, _ *clv1beta1.Instance) (float64, error) {
	m.IsGetDiskUsageCalled = true
	return m.diskUsage, m.diskUsageErr
}

func (m *OpenSearchServiceMock) GetStatusCondition(_ context.Context, _ *clv1beta1.Instance) metav1.Condition {
	m.IsGetStatusConditionCalled = true
	return m.statusCondition
}

func (m *OpenSearchServiceMock) GetNumberOfNodesMatchingVersion(_ context.Context, _ *clv1beta1.Instance, filter string, version *semverv3.Version) (int, error) {
	m.IsGetNumberOfNodesMatchingVersionCalled = true
	m.GetNumberOfNodesMatchingVersionVersion = version
	m.GetNumberOfNodesMatchingVersionFilter = filter
	return m.numberOfUpdatedInstances, m.getNumberOfUpdatedInstancesErr
}

func (m *OpenSearchServiceMock) IsEvacuationComplete(_ context.Context, _ *clv1beta1.Instance, nodeName string) (bool, error) {
	m.IsIsEvacuationCompleteCalled = true
	m.IsEvacuationCompleteNode = nodeName
	return m.isEvacuationComplete, m.isEvacuationCompleteErr
}

func (m *OpenSearchServiceMock) IsNodeRestartAllowed(_ context.Context, _ *clv1beta1.Instance, nodeName string) (bool, string, error) {
	m.IsIsNodeRestartAllowedCalled = true
	m.IsNodeRestartAllowedNodeName = nodeName
	return m.isNodeRestartAllowed, m.isNodeRestartAllowedReason, m.isNodeRestartAllowedErr
}

func (m *OpenSearchServiceMock) SetExcludedNodes(_ context.Context, _ *clv1beta1.Instance, nodeNames ...string) error {
	m.IsSetExcludedNodesCalled = true
	m.SectExcludedNodeNames = nodeNames
	return m.setExcludedNodesErr
}

func (m *OpenSearchServiceMock) GetExcludedNodes(_ context.Context, _ *clv1beta1.Instance) ([]string, error) {
	m.IsGetExcludedNodesCalled = true
	return m.getExcludedNodeNames, m.getExcludedNodesErr
}

func (m *OpenSearchServiceMock) SetShardAllocation(_ context.Context, _ *clv1beta1.Instance, setting models.OSShardAllocation) error {
	m.IsSetShardAllocationCalled = true
	m.SetShardAllocationSetting = setting
	return m.SetShardAllocationErr
}
