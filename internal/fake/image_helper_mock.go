package fake

import (
	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
)

type ImageHelperMock struct {
	Instance        *clv1beta1.Instance
	ImageChannels   config.ImageChannels
	availableImages map[string]string
}

func NewImageHelperMock() *ImageHelperMock {
	return &ImageHelperMock{}
}

func (m *ImageHelperMock) PrepareGetAvailableImages(availableImages map[string]string) {
	m.availableImages = availableImages
}

func (m *ImageHelperMock) GetAvailableImages(instance *clv1beta1.Instance, imageChannels config.ImageChannels) map[string]string {
	m.Instance = instance
	m.ImageChannels = imageChannels

	return m.availableImages
}
