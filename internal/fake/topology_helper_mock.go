package fake

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
)

// TopologyHelperMock is a mock implementation of the TopologyHelper interface
type TopologyHelperMock struct {
	Instance               *clv1beta1.Instance
	CurrentTopology        *models.Topology
	ImageChannel           string
	IsProcessCalled        bool
	AvailableImageChannels map[string]string
	patch                  models.TopologyPatch
	err                    error
}

// NewTopologyHelperMock creates a new instance of the TopologyHelperMock
func NewTopologyHelperMock() *TopologyHelperMock {
	return &TopologyHelperMock{}
}

// Prepare sets the expected return values of the Process method
func (t *TopologyHelperMock) Prepare(patch models.TopologyPatch, err error) {
	t.patch = patch
	t.err = err
}

// Process is a mock implementation of the Process method
func (t *TopologyHelperMock) Process(_ context.Context, instance *clv1beta1.Instance, currentTopology *models.Topology,
	availableImageChannels map[string]string, desiredImageChannel string) (models.TopologyPatch, error) {
	t.IsProcessCalled = true
	t.Instance = instance
	t.CurrentTopology = currentTopology
	t.ImageChannel = desiredImageChannel
	t.AvailableImageChannels = availableImageChannels

	return t.patch, t.err
}
