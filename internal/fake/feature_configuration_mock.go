package fake

import (
	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
)

type FeatureConfigurationMock struct {
	ProvidedInstance         *clv1beta1.Instance
	IsIsFeatureSetCalled     bool
	IsIsFeatureAllowedCalled bool
	preparedFeatures         map[string]bool
	allowedFeatures          []string
	supportedFeatures        []string
}

func NewFeatureConfigurationMock() *FeatureConfigurationMock {
	return &FeatureConfigurationMock{
		preparedFeatures: map[string]bool{},
	}
}

func (m *FeatureConfigurationMock) PrepareFeature(feature string, set bool) {
	m.preparedFeatures[feature] = set
}

func (m *FeatureConfigurationMock) PrepareSupportedFeatureFlags(supportedFeatures []string) {
	m.supportedFeatures = supportedFeatures
}

func (m *FeatureConfigurationMock) PrepareAllowedFeatureFlags(allowedFeatures []string) {
	m.allowedFeatures = allowedFeatures
}

func (m *FeatureConfigurationMock) IsFeatureSet(instance *clv1beta1.Instance, feature string) bool {
	m.IsIsFeatureSetCalled = true
	m.ProvidedInstance = instance
	return m.preparedFeatures[feature]
}

func (m *FeatureConfigurationMock) IsFeatureAllowed(instance *clv1beta1.Instance, feature string) (bool, string) {
	m.IsIsFeatureAllowedCalled = true
	m.ProvidedInstance = instance
	for _, allowedFeature := range m.allowedFeatures {
		if allowedFeature == feature {
			return true, allowedFeature
		}
	}
	return false, ""
}

func (m *FeatureConfigurationMock) GetSupportedFeatureFlags() []string {
	return m.supportedFeatures
}
