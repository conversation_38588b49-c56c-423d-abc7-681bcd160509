package utils_test

import (
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test K8s Resource Configuration", func() {
	Describe("GetDataPrepperCircuitBreakerFromHeap", func() {
		It("should return 100% of the heap value if the percentage is greater than 100", func() {
			result, err := utils.GetDataPrepperCircuitBreakerFromHeap(150, "1000m")
			Expect(err).To(BeNil())
			Expect(result).To(Equal("1000mb"))
		})

		It("should return the correct percentage of the heap value", func() {
			result, err := utils.GetDataPrepperCircuitBreakerFromHeap(50, "1000m")
			Expect(err).To(BeNil())
			Expect(result).To(Equal("500mb"))
		})

		It("should return error for unsupported units", func() {
			_, err := utils.GetDataPrepperCircuitBreakerFromHeap(50, "1000g")
			Expect(err).To(HaveOccurred())
		})

		It("should return error for invalid numeric values", func() {
			_, err := utils.GetDataPrepperCircuitBreakerFromHeap(50, "invalid")
			Expect(err).To(HaveOccurred())
		})
	})

	Describe("GetHeapFromMemory", func() {
		It("should return a reasonable heap size based on the given percentage", func() {
			result, err := utils.GetHeapFromMemory(50, "2Gi")
			Expect(err).To(BeNil())
			Expect(result).To(Equal("1024m")) // 2Gi * 50% = 1Gi = 1024m
		})

		It("should return error for invalid memory strings", func() {
			_, err := utils.GetHeapFromMemory(50, "invalid")
			Expect(err).To(HaveOccurred())
		})

		It("should return error for invalid numeric values", func() {
			_, err := utils.GetHeapFromMemory(50, "invalidGi")
			Expect(err).To(HaveOccurred())
		})
	})
})
