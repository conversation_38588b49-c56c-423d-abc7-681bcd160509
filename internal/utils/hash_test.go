package utils_test

import (
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Testing Hash", func() {
	var annotations map[string]string

	BeforeEach(func() {
		annotations = make(map[string]string)
	})

	When("SetHashFromCM is called", func() {
		var cm *corev1.ConfigMap

		BeforeEach(func() {
			cm = &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test-cm",
				},
				Data: map[string]string{
					"key1": "value1",
					"key2": "value2",
				},
			}
		})

		It("should set the hash annotation", func() {
			err := utils.SetHashFromCM(annotations, cm)
			Expect(err).To(BeNil())
			Expect(annotations["operator.cloud-logging.sap.com/test-cm-hash"]).ToNot(BeEmpty())
		})

		It("should set a different hash after a value changed", func() {
			err := utils.SetHashFromCM(annotations, cm)
			Expect(err).To(BeNil())
			Expect(annotations).To(HaveKey("operator.cloud-logging.sap.com/test-cm-hash"))
			hash1 := annotations["operator.cloud-logging.sap.com/test-cm-hash"]

			// change a value in the data
			cm.Data["key1"] = "value1_changed"

			err = utils.SetHashFromCM(annotations, cm)
			Expect(err).To(BeNil())
			Expect(annotations).To(HaveKey("operator.cloud-logging.sap.com/test-cm-hash"))
			hash2 := annotations["operator.cloud-logging.sap.com/test-cm-hash"]

			Expect(hash1).ToNot(Equal(hash2))
		})

		It("should set a different hash after a key has changed", func() {
			err := utils.SetHashFromCM(annotations, cm)
			Expect(err).To(BeNil())
			Expect(annotations).To(HaveKey("operator.cloud-logging.sap.com/test-cm-hash"))
			hash1 := annotations["operator.cloud-logging.sap.com/test-cm-hash"]

			// change a field in the data
			cm.Data = map[string]string{
				"key1":         "value1",
				"key2_changed": "value2",
			}

			err = utils.SetHashFromCM(annotations, cm)
			Expect(err).To(BeNil())
			Expect(annotations).To(HaveKey("operator.cloud-logging.sap.com/test-cm-hash"))
			hash2 := annotations["operator.cloud-logging.sap.com/test-cm-hash"]

			Expect(hash1).ToNot(Equal(hash2))
		})
	})

	When("SetHashFromSecret is called", func() {
		var secret *corev1.Secret

		BeforeEach(func() {
			secret = &corev1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test-secret",
				},
				Data: map[string][]byte{
					"key1": []byte("value1"),
					"key2": []byte("value2"),
				},
			}
		})

		It("should set the hash annotation", func() {
			err := utils.SetHashFromSecret(annotations, secret)
			Expect(err).To(BeNil())
			Expect(annotations["operator.cloud-logging.sap.com/test-secret-hash"]).ToNot(BeEmpty())
		})

		It("should set a different hash after a value changed", func() {
			err := utils.SetHashFromSecret(annotations, secret)
			Expect(err).To(BeNil())
			Expect(annotations).To(HaveKey("operator.cloud-logging.sap.com/test-secret-hash"))
			hash1 := annotations["operator.cloud-logging.sap.com/test-secret-hash"]

			// change a value in the data
			secret.Data["key1"] = []byte("value1_changed")

			err = utils.SetHashFromSecret(annotations, secret)
			Expect(err).To(BeNil())
			Expect(annotations).To(HaveKey("operator.cloud-logging.sap.com/test-secret-hash"))
			hash2 := annotations["operator.cloud-logging.sap.com/test-secret-hash"]

			Expect(hash1).ToNot(Equal(hash2))
		})

		It("should set a different hash after a key has changed", func() {
			err := utils.SetHashFromSecret(annotations, secret)
			Expect(err).To(BeNil())
			Expect(annotations).To(HaveKey("operator.cloud-logging.sap.com/test-secret-hash"))
			hash1 := annotations["operator.cloud-logging.sap.com/test-secret-hash"]

			// change a field in the data
			secret.Data = map[string][]byte{
				"key1":         []byte("value1"),
				"key2_changed": []byte("value2"),
			}

			err = utils.SetHashFromSecret(annotations, secret)
			Expect(err).To(BeNil())
			Expect(annotations).To(HaveKey("operator.cloud-logging.sap.com/test-secret-hash"))
			hash2 := annotations["operator.cloud-logging.sap.com/test-secret-hash"]

			Expect(hash1).ToNot(Equal(hash2))
		})
	})
})
