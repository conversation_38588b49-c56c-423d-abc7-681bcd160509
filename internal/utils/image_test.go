package utils_test

import (
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	corev1 "k8s.io/api/core/v1"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test Image", func() {

	BeforeEach(func() {
		Expect(config.GetInstance().Read("../config/test_config/config.yaml", false)).To(Succeed())
	})

	Context("GetImageTag()", func() {
		When("the image has a tag", func() {
			It("should return the tag", func() {
				tag := utils.GetImageTag("my-image:latest")
				Expect(tag).Should(Equal("latest"))
			})
		})

		When("the image does not have a tag", func() {
			It("should return an empty string", func() {
				tag := utils.GetImageTag("my-image")
				Expect(tag).Should(Equal(""))
			})
		})
	})

	Context("GetImageFromContainer()", func() {
		var containers []corev1.Container

		BeforeEach(func() {
			containers = []corev1.Container{
				{
					Name:  "test-container-1",
					Image: "test-image1:test-tag1",
				},
				{
					Name:  "test-container-2",
					Image: "test-image2:test-tag2",
				},
				{
					Name:  "test-container-3",
					Image: "test-image3:test-tag3",
				},
			}
		})

		When("the container has an image", func() {
			It("should return the image", func() {
				image, ok := utils.GetImageFromContainer(containers, "test-container-2")
				Expect(ok).Should(BeTrue())
				Expect(image).Should(Equal("test-image2:test-tag2"))
			})
		})

		When("the container does not exist", func() {
			It("should return an empty image", func() {
				image, ok := utils.GetImageFromContainer(containers, "other-container")
				Expect(ok).Should(BeFalse())
				Expect(image).Should(Equal(""))
			})
		})
	})

	Context("GetTargetImage()", func() {
		When("stable channel is desired", func() {
			It("should use the desired stable version if there is no discovered image", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:2.19.1",
					"stable":      "opensearch-stable:1.3.20",
				}

				var discoveredImages []string
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "stable")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-stable:1.3.20"))
				Expect(image.Version.Original()).Should(Equal("1.3.20"))
			})

			It("should not downgrade if discovered version is higher", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:2.19.1",
					"preview":     "opensearch-preview:2.18.2",
					"stable":      "opensearch-stable:1.3.20",
				}

				discoveredImages := []string{"opensearch-dev:2.18.3", "opensearch-dev:2.19.1"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "stable")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-dev:2.19.1"))
				Expect(image.Version.Original()).Should(Equal("2.19.1"))
			})

			It("should use correct preview version for update and stay on it for repeated execution", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:3.1.1",
					"preview":     "opensearch-preview:2.20.2",
					"stable":      "opensearch-stable:1.3.20",
				}

				discoveredImages := []string{"opensearch-dev:2.18.3", "opensearch-dev:2.19.1"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "stable")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-preview:2.20.2"))
				Expect(image.Version.Original()).Should(Equal("2.20.2"))

				discoveredImages = []string{"opensearch-preview:2.20.2", "opensearch-preview:2.20.2"}
				image, err = utils.GetTargetImage(availableImages, discoveredImages, "stable")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-preview:2.20.2"))
				Expect(image.Version.Original()).Should(Equal("2.20.2"))
			})

			It("should use updated development version if discovered version is higher AND a more recent development version is available", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:2.19.2",
					"preview":     "opensearch-preview:2.18.2",
					"stable":      "opensearch-stable:1.3.20",
				}

				discoveredImages := []string{"opensearch-dev:2.18.1", "opensearch-dev:2.19.1"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "stable")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-dev:2.19.2"))
				Expect(image.Version.Original()).Should(Equal("2.19.2"))
			})

			It("should ignore any development versions and preserve stable if there is no difference between discovered and desired images", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:2.19.2",
					"stable":      "opensearch-stable:1.3.20",
				}

				discoveredImages := []string{"opensearch-stable:1.3.20", "opensearch-stable:1.3.20"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "stable")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-stable:1.3.20"))
				Expect(image.Version.Original()).Should(Equal("1.3.20"))
			})

			It("should ignore any development versions and update stable if there is difference between discovered and desired images", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:2.19.2",
					"stable":      "opensearch-stable:1.3.21",
				}

				discoveredImages := []string{"opensearch-stable:1.3.20", "opensearch-stable:1.3.20"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "stable")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-stable:1.3.21"))
				Expect(image.Version.Original()).Should(Equal("1.3.21"))
			})

			It("should switch stable image if there a force upgrade happened (invalidation of preview feature flag)", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:2.20.2",
					"preview":     "opensearch-preview:2.19.2",
					"stable":      "opensearch-stable:2.19.2",
				}

				discoveredImages := []string{"opensearch-preview:2.19.2", "opensearch-preview:2.19.2"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "stable")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-stable:2.19.2"))
				Expect(image.Version.Original()).Should(Equal("2.19.2"))
			})

			It("should keep the discovered version, if the stable version is lower and the other options are major upgrades", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:2.19.1",
					"preview":     "opensearch-preview:2.18.2",
					"stable":      "opensearch-stable:1.3.20",
				}

				discoveredImages := []string{"opensearch-stable:1.3.21", "opensearch-stable:1.3.21"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "stable")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-stable:1.3.21"))
				Expect(image.Version.Original()).Should(Equal("1.3.21"))
			})
		})

		When("non-stable channel is desired", func() {
			It("should preserve discovered image in case the channel is unknown", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:2.19.1",
					"stable":      "opensearch-stable:1.3.20",
				}

				discoveredImages := []string{"opensearch-dev:2.18.2", "opensearch-dev:2.19.2"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "somechannel")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-dev:2.19.2"))
				Expect(image.Version.Original()).Should(Equal("2.19.2"))
			})

			It("should not downgrade if discovered version is higher", func() {
				availableImages := map[string]string{
					"development": "opensearch-dev:2.19.1",
					"stable":      "opensearch-stable:1.3.20",
				}

				discoveredImages := []string{"opensearch-dev:2.18.2", "opensearch-dev:2.19.2"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "development")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-dev:2.19.2"))
				Expect(image.Version.Original()).Should(Equal("2.19.2"))
			})

			It("should upgrade if discovered version is lower", func() {
				availableImages := map[string]string{
					"development": "opensearch-devnew:2.19.2",
					"stable":      "opensearch-stable:1.3.20",
				}

				discoveredImages := []string{"opensearch-dev:2.18.2", "opensearch-dev:2.19.1"}
				image, err := utils.GetTargetImage(availableImages, discoveredImages, "development")

				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-devnew:2.19.2"))
				Expect(image.Version.Original()).Should(Equal("2.19.2"))
			})
		})
	})

	Context("GetMatchingImage()", func() {
		When("the support version is invalid", func() {
			It("should return an error", func() {
				availableImages := map[string]string{}

				image, err := utils.GetMatchingImage(availableImages, "", "stable", "x.y.invalid")
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).Should(Equal("failed to parse support version 'x.y.invalid': Invalid Semantic Version"))
				Expect(image).Should(Equal(utils.VersionTuple{}))
			})
		})

		When("a single matching channel is available", func() {
			It("should return the matching image version", func() {
				availableImages := map[string]string{
					"stable":      "opensearch-stable:1.3.20",
					"preview":     "opensearch-preview:2.18.2",
					"development": "opensearch-dev:2.19.1",
				}

				image, err := utils.GetMatchingImage(availableImages, "", "stable", "1.3.20")
				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-stable:1.3.20"))
			})

			When("the desired channel does not match any available images", func() {
				It("should still return the first matching image version", func() {
					availableImages := map[string]string{
						"stable":      "opensearch-stable:1.3.20",
						"preview":     "opensearch-preview:2.18.2",
						"development": "opensearch-dev:2.19.1",
					}

					image, err := utils.GetMatchingImage(availableImages, "", "preview", "1.3.20")
					Expect(err).NotTo(HaveOccurred())
					Expect(image.Image).Should(Equal("opensearch-stable:1.3.20"))
				})
			})
		})

		When("multiple matching channels are available", func() {
			It("should return the matching image version for the desired channel", func() {
				availableImages := map[string]string{
					"stable":      "opensearch-stable:1.3.20",
					"preview":     "opensearch-preview:1.3.20",
					"development": "opensearch-dev:2.19.1",
				}

				image, err := utils.GetMatchingImage(availableImages, "", "preview", "1.3.20")
				Expect(err).NotTo(HaveOccurred())
				Expect(image.Image).Should(Equal("opensearch-preview:1.3.20"))
			})
		})

		When("no matching images are available", func() {
			It("should return an error", func() {
				availableImages := map[string]string{
					"stable":      "opensearch-stable:1.3.20",
					"preview":     "opensearch-preview:2.18.2",
					"development": "opensearch-dev:2.19.1",
				}

				image, err := utils.GetMatchingImage(availableImages, "", "stable", "1.3.21")
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).Should(Equal("no matching image version found for support version '1.3.21'"))
				Expect(image).Should(Equal(utils.VersionTuple{}))
			})

			When("the discoverd image is set and matches the support version", func() {
				It("should return the discovered image version", func() {
					availableImages := map[string]string{
						"stable":      "opensearch-stable:2.17.0",
						"preview":     "opensearch-preview:2.18.0",
						"development": "opensearch-dev:2.19.0",
					}

					image, err := utils.GetMatchingImage(availableImages, "opensearch-discovered:1.3.20", "unknown", "1.3.20")
					Expect(err).NotTo(HaveOccurred())
					Expect(image.Image).Should(Equal("opensearch-discovered:1.3.20"))
				})
			})

			When("the discovered image does not match the support version", func() {
				It("should return an error", func() {
					availableImages := map[string]string{
						"stable":      "opensearch-stable:2.17.0",
						"preview":     "opensearch-preview:2.18.0",
						"development": "opensearch-dev:2.19.0",
					}

					image, err := utils.GetMatchingImage(availableImages, "opensearch-discovered:1.3.21", "unknown", "1.3.20")
					Expect(err).To(HaveOccurred())
					Expect(image).Should(Equal(utils.VersionTuple{}))
				})
			})
		})
	})
})
