package utils_test

import (
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test Constrain", func() {
	When("Constrain is called", func() {
		It("should return the expected value", func() {
			Expect(utils.Constrain(5, 1, 10)).Should(Equal(5))
			Expect(utils.Constrain(0, 1, 10)).Should(Equal(1))
			Expect(utils.Constrain(11, 1, 10)).Should(Equal(10))
		})
	})
})
