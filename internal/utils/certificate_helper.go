package utils

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
)

func ConvertCertToPEM(bytes []byte) []byte {
	return convertToPEM("CERTIFICATE", bytes)
}

func ConvertKeyToPEM(bytes []byte) []byte {
	return convertToPEM("PRIVATE KEY", bytes)
}

func convertToPEM(tp string, bytes []byte) []byte {
	b := pem.Block{Type: tp, Bytes: bytes}
	return pem.EncodeToMemory(&b)
}

func ConvertToPKCS8(key *rsa.PrivateKey) ([]byte, error) {
	return x509.MarshalPKCS8PrivateKey(key)
}

// ParseCertAndKeyData decodes and parses the certificate and private key data from byte slices.
// It returns the parsed x509.Certificate and rsa.PrivateKey objects.
//
// The `certData` parameter should contain the certificate data in PEM format,
// and the `keyData` parameter should contain the private key data in PEM format.
// Please note that the key velue returned will be in PKCS#1 format.
func ParseCertAndKeyData(certData, keyData []byte) (*x509.Certificate, *rsa.PrivateKey, error) {
	certBlock, _ := pem.Decode(certData)
	if certBlock == nil {
		return nil, nil, errors.New("failed to decode certificate data")
	}

	caCert, err := x509.ParseCertificate(certBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse certificate: %w", err)
	}

	keyBlock, _ := pem.Decode(keyData)
	if keyBlock == nil {
		return nil, nil, errors.New("failed to decode key data")
	}

	caPrivateKey, err := x509.ParsePKCS1PrivateKey(keyBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	return caCert, caPrivateKey, nil
}
