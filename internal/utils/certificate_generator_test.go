package utils_test

import (
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"math/big"
	"time"

	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test Certificate Generator", func() {
	const keySize = 3072

	var (
		caCert *x509.Certificate
		caKey  *rsa.PrivateKey
		err    error
	)

	Context("Generate a CA", func() {
		It("returns certificate and private key", func() {
			template := &x509.Certificate{
				SerialNumber: big.NewInt(2020),
				Subject: pkix.Name{
					Organization:       []string{"SAP SE"},
					Country:            []string{"DE"},
					Province:           []string{"Baden-Wuerttemberg"},
					Locality:           []string{"Karlsruhe"},
					OrganizationalUnit: []string{"Cloud Logging"},
					CommonName:         "caCert",
				},
				NotBefore: time.Now(),
				NotAfter:  time.Now().AddDate(10, 0, 0),
			}
			caCert, caKey, err = utils.GenerateCA(template, keySize)
			Ω(err).Should(BeNil())
			Ω(caCert.IsCA).Should(BeTrue())
			Ω(caCert.BasicConstraintsValid).Should(BeTrue())
			Ω(caCert.Subject.CommonName).Should(Equal("caCert"))
			Ω(caCert.Subject.Organization[0]).Should(Equal("SAP SE"))
			Ω(caCert.Subject.Country[0]).Should(Equal("DE"))
			Ω(caCert.Subject.Province[0]).Should(Equal("Baden-Wuerttemberg"))
			Ω(caCert.Subject.OrganizationalUnit[0]).Should(Equal("Cloud Logging"))
			Ω(caCert.Subject.Locality[0]).Should(Equal("Karlsruhe"))
			Ω(caKey).ShouldNot(BeNil())
		})
	})

	Context("Generate certificate and key", func() {
		It("returns a valid certificate and private key", func() {
			template := &x509.Certificate{
				SerialNumber: big.NewInt(2020),
				Subject: pkix.Name{
					Organization:       []string{"SAP SE"},
					Country:            []string{"DE"},
					Province:           []string{"Baden-Wuerttemberg"},
					Locality:           []string{"Karlsruhe"},
					OrganizationalUnit: []string{"Cloud Logging"},
					CommonName:         "node",
				},
				NotBefore: time.Now(),
				NotAfter:  time.Now().AddDate(10, 0, 0),
			}

			var (
				nodeCert *x509.Certificate
				nodeKey  *rsa.PrivateKey
			)

			nodeCert, nodeKey, err = utils.GenerateCert(template, caCert, caKey, keySize)
			Ω(err).Should(BeNil())
			Ω(nodeCert.IsCA).Should(BeFalse())

			rootPool := x509.NewCertPool()
			rootPool.AddCert(caCert)
			verifyOpts := x509.VerifyOptions{
				Roots: rootPool,
			}
			_, err = nodeCert.Verify(verifyOpts)
			Ω(err).Should(BeNil())

			Ω(nodeKey.Validate()).Should(BeNil())
		})
	})

	Context("Generate CA certificate and key without template", func() {
		It("returns a valid CA certificate and private key", func() {
			commonName := "caCert"
			certValidityPeriod := time.Now().Add(10 * time.Hour)
			serialNumber := big.NewInt(202020)
			caCert, caKey, err = utils.GenerateCustomCA(commonName, keySize, certValidityPeriod, serialNumber)
			Ω(err).Should(BeNil())
			Ω(caCert.IsCA).Should(BeTrue())
			Ω(caCert.BasicConstraintsValid).Should(BeTrue())
			Ω(caCert.Subject.CommonName).Should(Equal("caCert"))
			Ω(caCert.Subject.Organization[0]).Should(Equal("SAP SE"))
			Ω(caCert.Subject.Country[0]).Should(Equal("DE"))
			Ω(caCert.Subject.Province[0]).Should(Equal("Baden-Wuerttemberg"))
			Ω(caCert.Subject.OrganizationalUnit[0]).Should(Equal("Cloud Logging"))
			Ω(caCert.Subject.Locality[0]).Should(Equal("Karlsruhe"))
			Ω(caKey).ShouldNot(BeNil())
		})
	})

	Context("Generate certificate and key without template", func() {
		It("returns a valid certificate and private key", func() {
			commonName := "test"
			certValidityPeriod := time.Now().Add(10 * time.Hour)
			serialNumber := big.NewInt(202020)
			cert, key, err := utils.GenerateCustomCert(commonName, keySize, caCert, caKey,
				certValidityPeriod, serialNumber)
			Ω(err).Should(BeNil())
			Ω(cert.IsCA).Should(BeFalse())

			rootPool := x509.NewCertPool()
			rootPool.AddCert(caCert)
			verifyOpts := x509.VerifyOptions{
				Roots: rootPool,
			}
			_, err = cert.Verify(verifyOpts)
			Ω(err).Should(BeNil())

			Ω(key.Validate()).Should(BeNil())
		})
	})
})
