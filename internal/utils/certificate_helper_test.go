package utils_test

import (
	"crypto/rand"
	"crypto/rsa"
	"encoding/pem"

	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test Certificate Helper", func() {
	Describe("ConvertCertToPEM", func() {
		It("converts a certificate to PEM", func() {
			content := []byte("test")
			pemCert := utils.ConvertCertToPEM(content)
			block, _ := pem.Decode(pemCert)
			Ω(block.Type).Should(Equal("CERTIFICATE"))
			Ω(block.Bytes).Should(Equal(content))
		})
	})

	Describe("ConvertKeyToPEM", func() {
		It("converts a ket to PEM", func() {
			content := []byte("test")
			pemKey := utils.ConvertKeyToPEM(content)
			block, _ := pem.Decode(pemKey)
			Ω(block.Type).Should(Equal("PRIVATE KEY"))
			Ω(block.Bytes).Should(Equal(content))
		})
	})

	Describe("ConvertToPKCS8", func() {
		It("converts a key to PKCS8 without error", func() {
			key, _ := rsa.GenerateKey(rand.Reader, 3072)
			_, err := utils.ConvertToPKCS8(key)
			Ω(err).ShouldNot(HaveOccurred())
		})
	})

	Describe("ParseCertAndKeyData", func() {
		Context("when certificate data is invalid", func() {
			It("returns an error if certificate data is empty", func() {
				_, _, err := utils.ParseCertAndKeyData(nil, []byte("valid-key-data"))
				Ω(err).Should(HaveOccurred())
				Ω(err.Error()).Should(ContainSubstring("failed to decode certificate data"))
			})

			It("returns an error if certificate data cannot be decoded", func() {
				_, _, err := utils.ParseCertAndKeyData([]byte("invalid-cert-data"), []byte("valid-key-data"))
				Ω(err).Should(HaveOccurred())
				Ω(err.Error()).Should(ContainSubstring("failed to decode certificate data"))
			})

			It("returns an error if certificate data cannot be parsed", func() {
				invalidCertPemBlock := fake.InvalidCertString
				_, _, err := utils.ParseCertAndKeyData([]byte(invalidCertPemBlock), []byte("valid-key-data"))
				Ω(err).Should(HaveOccurred())
				Ω(err.Error()).Should(ContainSubstring("failed to parse certificate"))
			})
		})

		Context("when key data is invalid", func() {
			It("returns an error if key data is empty", func() {
				validCertPemBlock := fake.CertString
				_, _, err := utils.ParseCertAndKeyData([]byte(validCertPemBlock), nil)
				Ω(err).Should(HaveOccurred())
				Ω(err.Error()).Should(ContainSubstring("failed to decode key data"))
			})

			It("returns an error if key data cannot be decoded", func() {
				validCertPemBlock := fake.CertString
				_, _, err := utils.ParseCertAndKeyData([]byte(validCertPemBlock), []byte("invalid-key-data"))
				Ω(err).Should(HaveOccurred())
				Ω(err.Error()).Should(ContainSubstring("failed to decode key data"))
			})

			It("returns an error if private key cannot be parsed", func() {
				validCertPemBlock := fake.CertString
				invalidKeyPemBlock := fake.InvalidKeyString
				_, _, err := utils.ParseCertAndKeyData([]byte(validCertPemBlock), []byte(invalidKeyPemBlock))
				Ω(err).Should(HaveOccurred())
				Ω(err.Error()).Should(ContainSubstring("failed to parse private key"))
			})
		})

		Context("when certificate and key data are valid", func() {
			It("returns the parsed certificate and private key", func() {
				validCertPemBlock := fake.CACertString
				validKeyPemBlock := fake.CAKeyString
				cert, privateKey, err := utils.ParseCertAndKeyData([]byte(validCertPemBlock), []byte(validKeyPemBlock))
				Ω(err).ShouldNot(HaveOccurred())
				Ω(cert).ShouldNot(BeNil())
				Ω(privateKey).ShouldNot(BeNil())
			})
		})
	})
})
