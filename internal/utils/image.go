package utils

import (
	"errors"
	"fmt"
	"sort"
	"strings"

	semverv3 "github.com/Masterminds/semver/v3"
	corev1 "k8s.io/api/core/v1"
)

// VersionTuple holds an image along with its version.
// It can be associated with a channel.
type VersionTuple struct {
	Version *semverv3.Version
	Image   string
	Channel string
}

// GetImageTag returns the tag of the given image.
func GetImageTag(image string) string {
	arr := strings.Split(image, ":")
	if len(arr) >= 2 {
		return arr[len(arr)-1]
	}
	return ""
}

// GetVersionFromImage parses the image tag and returns the version.
// E.g. "my-image:1.2.3" -> "1.2.3"
func GetVersionFromImage(image string) (*semverv3.Version, error) {
	imageTag := GetImageTag(image)
	version, err := semverv3.NewVersion(imageTag)

	if err != nil {
		return nil, fmt.Errorf("failed to parse image version tag '%s': %w", imageTag, err)
	}

	return version, err
}

// GetImageFromContainer returns the image of named container from the given list of containers.
func GetImageFromContainer(containers []corev1.Container, containerName string) (string, bool) {
	for _, container := range containers {
		if container.Name == containerName {
			return container.Image, true
		}
	}
	return "", false
}

// GetTargetImage determines the target image by traversing the available image versions of different channels (sorted low to high version)
// to compare the compatibility with the highest discovered image version. The traversal is starting with the desired channel as a starting index.
// As long the version is not compatible with the highest discovered image the index will be incremented. An image version is considered compatible
// once it is equal or greater than the compared version. With this approach version downgrades will be prevented.
// If no compatibility has been found, the highest discovered image will be preserved as target image.
func GetTargetImage(availableChannelImages map[string]string, discoveredImages []string, desiredChannel string) (VersionTuple, error) {
	var targetVersion VersionTuple

	// Structure available images
	availableImageVersions := make([]VersionTuple, 0)
	for channel, image := range availableChannelImages {
		// Parse image tag and get version
		version, err := GetVersionFromImage(image)
		if err != nil {
			return VersionTuple{}, fmt.Errorf("failed to get version for image channel '%s' and image '%s': %w", channel, image, err)
		}

		imageVersion := VersionTuple{Version: version, Image: image, Channel: channel}
		availableImageVersions = append(availableImageVersions, imageVersion)
	}

	// Sort available images - lowest will be the first
	sort.Slice(availableImageVersions, func(i, j int) bool {
		return availableImageVersions[i].Version.Compare(availableImageVersions[j].Version) < 0
	})

	// Determine highest discoveredImages version
	highestDiscoveredImageVersion := VersionTuple{Version: semverv3.MustParse("0.0.0"), Image: "", Channel: ""}
	for _, image := range discoveredImages {
		version, err := GetVersionFromImage(image)
		if err != nil {
			return VersionTuple{}, fmt.Errorf("failed to get version for discovered image '%s': %w", image, err)
		}

		imageVersion := VersionTuple{Version: version, Image: image, Channel: "discovered"}
		if highestDiscoveredImageVersion.Version == nil || imageVersion.Version.Compare(highestDiscoveredImageVersion.Version) > 0 {
			highestDiscoveredImageVersion = imageVersion
		}
	}

	// Determine target image version by traversing the available imageVersions and checking their compatibility.
	// Prioritize the desired channel and fallback to the next available version, if necessary.
	// Only permit major updates if the target image version is provided by the desired channel.
	foundChannel := false
	for _, version := range availableImageVersions {
		if version.Channel == desiredChannel {
			foundChannel = true
			compatible, _ := isVersionCompatible(highestDiscoveredImageVersion.Version, version.Version)
			if compatible {
				targetVersion = version
				break
			}
		}

		if foundChannel {
			compatible, majorUpgrade := isVersionCompatible(highestDiscoveredImageVersion.Version, version.Version)
			if compatible && !majorUpgrade {
				targetVersion = version
				break
			}
		}
	}

	// Fallback to discovered version in case no compatible version could be determined.
	if targetVersion.Version == nil {
		if highestDiscoveredImageVersion.Image == "" {
			return VersionTuple{}, errors.New("no compatible image version found")
		}

		targetVersion = highestDiscoveredImageVersion
	}

	return targetVersion, nil
}

// GetMatchingImage tries to find an image version that equals the given support version and the desired channel.
func GetMatchingImage(availableChannelImages map[string]string, discoveredImage string, desiredChannel string, supportVersionStr string) (VersionTuple, error) {
	// Parse the support version
	supportVersion, err := semverv3.NewVersion(supportVersionStr)
	if err != nil {
		return VersionTuple{}, fmt.Errorf("failed to parse support version '%s': %w", supportVersionStr, err)
	}

	// Structure available images
	availableImageVersions := make([]VersionTuple, 0)
	for channel, image := range availableChannelImages {
		// Parse image tag and get version
		version, err := GetVersionFromImage(image)
		if err != nil {
			return VersionTuple{}, fmt.Errorf("failed to get version for image channel '%s' and image '%s': %w", channel, image, err)
		}

		imageVersion := VersionTuple{Version: version, Image: image, Channel: channel}
		availableImageVersions = append(availableImageVersions, imageVersion)
	}

	// Parse the discovered image and get its version, if available
	discoveredImageVersion := VersionTuple{Version: nil, Image: "", Channel: "discovered"}
	if discoveredImage != "" {
		version, err := GetVersionFromImage(discoveredImage)
		if err != nil {
			return VersionTuple{}, fmt.Errorf("failed to get version for discovered image '%s': %w", discoveredImage, err)
		}

		discoveredImageVersion.Version = version
		discoveredImageVersion.Image = discoveredImage
	}

	// Filter available images by support version
	matchingImages := make([]VersionTuple, 0)
	for _, image := range availableImageVersions {
		if image.Version.Equal(supportVersion) {
			matchingImages = append(matchingImages, image)
		}
	}

	// If no matching images are available, try to fallback to the discovered image version
	if len(matchingImages) == 0 {
		if discoveredImageVersion.Version != nil {
			if discoveredImageVersion.Version.Equal(supportVersion) {
				return discoveredImageVersion, nil
			}
		}

		return VersionTuple{}, fmt.Errorf("no matching image version found for support version '%s'", supportVersionStr)
	}

	// Try to find and return the desired channel in the matching images
	for _, image := range matchingImages {
		if image.Channel == desiredChannel {
			return image, nil
		}
	}

	// Fallback to the first matching image if no desired channel is found
	return matchingImages[0], nil
}

// isVersionCompatible checks if the new version is equal or greater than the current version.
// The second return value indicates if a possible upgrade would be a major upgrade.
func isVersionCompatible(currentVersion, newVersion *semverv3.Version) (bool, bool) {
	// If the new version is nil, it is not compatible
	if newVersion == nil {
		return false, false
	}

	// If there is no current version, the new version is always compatible
	if currentVersion == nil {
		return true, false
	}

	// Check if the new version is greater than or equal to the current version
	if newVersion.Compare(currentVersion) >= 0 {
		return true, newVersion.Major() > currentVersion.Major()
	}

	return false, false
}
