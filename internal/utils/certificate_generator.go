package utils

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"fmt"
	"math/big"
	"time"
)

const (
	CertificateOrganization       = "SAP SE"
	CertificateCountry            = "DE"
	CertificateProvince           = "Baden-Wuerttemberg"
	CertificateLocality           = "Karlsruhe"
	CertificateOrganizationalUnit = "Cloud Logging"
)

// GenerateCA returns a x509 CA certificate and private key for a given certificate template.
func GenerateCA(template *x509.Certificate,
	keySize int) (*x509.Certificate, *rsa.PrivateKey, error) {
	rootCAPrivateKey, err := rsa.GenerateKey(rand.Reader, keySize)
	if err != nil {
		return nil, nil, err
	}

	// make sure those two fields are set to true if it should be CA
	template.IsCA = true
	template.BasicConstraintsValid = true

	rootCACert, err := generateCertificate(template, template, &rootCAPrivateKey.PublicKey, rootCAPrivateKey)
	if err != nil {
		return nil, nil, err
	}

	return rootCACert, rootCAPrivateKey, nil
}

// GenerateCert returns a x509 certificate and private key for a given certificate template.
func GenerateCert(template, caCert *x509.Certificate,
	caPrivateKey *rsa.PrivateKey, keySize int) (*x509.Certificate, *rsa.PrivateKey, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, keySize)
	if err != nil {
		return nil, nil, err
	}

	cert, err := generateCertificate(template, caCert, &privateKey.PublicKey, caPrivateKey)
	if err != nil {
		return nil, nil, err
	}

	return cert, privateKey, nil
}

// generateCertificate a x509 certificate.
func generateCertificate(template, parent *x509.Certificate, publicKey *rsa.PublicKey,
	privateKey *rsa.PrivateKey) (*x509.Certificate, error) {
	certBytes, err := x509.CreateCertificate(rand.Reader, template, parent, publicKey, privateKey)
	if err != nil {
		return nil, err
	}

	cert, err := x509.ParseCertificate(certBytes)
	if err != nil {
		return nil, err
	}

	return cert, nil
}

// GenerateCustomCA returns a x509 CA certificate and private key for the given certificate parameters.
func GenerateCustomCA(commonName string, keySize int, certValidityPeriod time.Time,
	serialNumber *big.Int) (*x509.Certificate, *rsa.PrivateKey, error) {
	template := getTemplate(commonName, certValidityPeriod, serialNumber)

	rootCACert, rootCAKey, err := GenerateCA(template, keySize)
	if err != nil {
		return nil, nil, fmt.Errorf("cannot create CA certificate: %w", err)
	}

	return rootCACert, rootCAKey, nil
}

// GenerateCustomCert returns a x509 certificate and private key for the given certificate parameters.
func GenerateCustomCert(commonName string, keySize int,
	rootCACert *x509.Certificate, rootCAKey *rsa.PrivateKey, certValidityPeriod time.Time,
	serialNumber *big.Int) (*x509.Certificate, *rsa.PrivateKey, error) {
	template := getTemplate(commonName, certValidityPeriod, serialNumber)

	cert, privateKey, err := GenerateCert(template, rootCACert, rootCAKey, keySize)
	if err != nil {
		return nil, nil, fmt.Errorf("cannot create certificate: %w", err)
	}

	return cert, privateKey, nil
}

// getTemplate gives a generic template for all internal CAs and certificates.
func getTemplate(commonName string, certValidityPeriod time.Time,
	serialNumber *big.Int) *x509.Certificate {
	return &x509.Certificate{
		SerialNumber: serialNumber, // must always be unique
		Subject: pkix.Name{
			Organization:       []string{CertificateOrganization},
			Country:            []string{CertificateCountry},
			Province:           []string{CertificateProvince},
			Locality:           []string{CertificateLocality},
			OrganizationalUnit: []string{CertificateOrganizationalUnit},
			CommonName:         commonName,
		},
		NotBefore:             time.Now(),
		NotAfter:              certValidityPeriod,
		BasicConstraintsValid: true,
		IsCA:                  false,
	}
}
