package utils_test

import (
	"math/big"

	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test Random", func() {
	Context("with a valid size", func() {
		It("should generate a random integer of the correct byte size", func() {
			size := 8
			randomInt, err := utils.GenerateRandomInt(size)
			Expect(err).ToNot(HaveOccurred())
			Expect(randomInt).To(BeAssignableToTypeOf(&big.Int{}))
			Expect(randomInt.BitLen()).To(BeNumerically(">=", size*8-7)) // Check size in bits, accounting for leading zeros
		})

		It("should return a non-nil random integer", func() {
			size := 16
			randomInt, err := utils.GenerateRandomInt(size)
			Expect(err).ToNot(HaveOccurred())
			Expect(randomInt).ToNot(BeNil())
		})
	})

	Context("with invalid size", func() {
		It("should return an error when size is negative", func() {
			size := -1
			_, err := utils.GenerateRandomInt(size)
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(Equal("invalid size provided: '-1'"))
		})
	})
})
