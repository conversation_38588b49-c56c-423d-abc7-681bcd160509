package ticker

import (
	"context"
	"fmt"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"

	"github.com/go-logr/logr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type runnableTicker struct {
	logger   logr.Logger
	client   client.Client
	interval time.Duration
	runnable Runnable
}

// NewRunnableTicker creates a new ticker which lists all instances and runs the given runnable at a fixed interval.
func NewRunnableTicker(client client.Client, runnable Runnable, interval time.Duration) RunnableTicker {
	return &runnableTicker{
		logger:   ctrl.Log.WithName(fmt.Sprintf("%s-ticker", runnable.GetName())),
		client:   client,
		runnable: runnable,
		interval: interval,
	}
}

// Start runs the runnable at a fixed interval once the given elected channel is closed.
// It runs until the given context is canceled.
func (t *runnableTicker) Start(ctx context.Context, elected <-chan struct{}) {
	go func() {
		// wait for leader election
		<-elected
		log := t.logger.WithValues("runnable", t.runnable.GetName())
		log.Info("Start ticker", "interval", t.interval.String())
		ticker := time.NewTicker(t.interval)
		defer ticker.Stop()

		for {
			// run PreRun() once before the first Run() call in the cycle
			err := t.runnable.PreRun(ctx)
			if err != nil {
				log.Error(err, "Error while executing pre-run for runnable")
			}

			// list all instances and run the runnable for each instance
			instanceList := &clv1beta1.InstanceList{}
			err = t.client.List(ctx, instanceList)
			if err != nil {
				log.Error(err, "Failed to list instances")
			} else {
				for _, instance := range instanceList.Items {
					err = t.runnable.Run(ctx, &instance)
					if err != nil {
						log.Error(err, "Error while executing runnable for instance", "instance", instance.Name)
					}
				}
			}

			select {
			case <-ticker.C:
			case <-ctx.Done():
				log.Info("Stop ticker")
				return
			}
		}
	}()
}
