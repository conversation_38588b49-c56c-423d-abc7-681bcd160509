package ticker_test

import (
	"context"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/ticker"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("RunnableTicker", func() {
	const (
		instanceName1 = "test-instance1"
		instanceName2 = "test-instance2"
		namespace1    = "test-namespace1"
		namespace2    = "test-namespace2"
	)

	var (
		runnableTicker ticker.RunnableTicker
		runnableMock   *fake.RunnableMock
		elected        chan struct{}
		ctx            context.Context
		cancel         context.CancelFunc
	)

	BeforeEach(func() {
		runnableMock = fake.NewRunnableMock()
		elected = make(chan struct{})
		//nolint:fatcontext // We need a context to be able to cancel the ticker.
		ctx, cancel = context.WithCancel(context.Background())

		// Run ticker at high frequency to speed up tests
		runnableTicker = ticker.NewRunnableTicker(k8sClient, runnableMock, 100*time.Millisecond)

		// Create instances and namespaces
		instance1 := &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      instanceName1,
				Namespace: namespace1,
			},
			Spec: clv1beta1.InstanceSpec{},
		}

		instance2 := &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      instanceName2,
				Namespace: namespace2,
			},
			Spec: clv1beta1.InstanceSpec{},
		}

		ns1 := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace1,
			},
		}

		ns2 := &corev1.Namespace{
			ObjectMeta: metav1.ObjectMeta{
				Name: namespace2,
			},
		}

		err := k8sClient.Get(ctx, types.NamespacedName{Name: ns1.Name}, ns1)
		if err != nil && apierrors.IsNotFound(err) {
			Expect(k8sClient.Create(ctx, ns1)).To(Succeed())
		}

		err = k8sClient.Get(ctx, types.NamespacedName{Name: ns2.Name}, ns2)
		if err != nil && apierrors.IsNotFound(err) {
			Expect(k8sClient.Create(ctx, ns2)).To(Succeed())
		}

		Expect(k8sClient.Create(ctx, instance1)).To(Succeed())
		Expect(k8sClient.Create(ctx, instance2)).To(Succeed())
	})

	AfterEach(func() {
		Expect(k8sClient.DeleteAllOf(context.Background(), &clv1beta1.Instance{}, client.InNamespace(namespace1))).To(Succeed())
		Expect(k8sClient.DeleteAllOf(context.Background(), &clv1beta1.Instance{}, client.InNamespace(namespace2))).To(Succeed())
	})

	Context("Start()", func() {
		BeforeEach(func() {
			runnableTicker.Start(ctx, elected)
		})

		When("the CLOE pod is elected as leader", func() {
			BeforeEach(func() {
				close(elected)
			})

			It("should run the PreRun of the RunnableMock", func() {
				Eventually(func() bool {
					return runnableMock.IsPreRunCalled
				}).Should(BeTrue())
			})

			It("should run the RunnableMock for all instances", func() {
				Eventually(func() bool {
					return runnableMock.IsRunCalled
				}).Should(BeTrue())
				Expect(runnableMock.InstanceNames).To(ConsistOf(instanceName1, instanceName2))
			})

			It("should run the RunnableMock at the given interval", func() {
				Eventually(func() bool {
					return runnableMock.IsRunCalled
				}).Should(BeTrue())

				runnableMock.PrepareRun(nil)

				Eventually(func() bool {
					return runnableMock.IsRunCalled
				}).Should(BeTrue())
			})

			When("the context is canceled", func() {
				BeforeEach(func() {
					cancel()
				})

				It("should stop running the RunnableMock", func() {
					Consistently(func() bool {
						return runnableMock.IsRunCalled
					}, 300*time.Millisecond).Should(BeFalse())
				})
			})
		})

		When("the CLOE pod is not elected as leader", func() {
			It("should not run the RunnableMock", func() {
				Consistently(func() bool {
					return runnableMock.IsRunCalled
				}, 300*time.Millisecond).Should(BeFalse())
			})

			It("should not run the PreRun of the RunnableMock", func() {
				Consistently(func() bool {
					return runnableMock.IsPreRunCalled
				}, 300*time.Millisecond).Should(BeFalse())
			})
		})
	})
})
