package status

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"

	"github.com/go-logr/logr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type statusConditionUpdater struct {
	client               client.Client
	componentReconcilers []reconcilers.ComponentReconciler

	logr.Logger
}

const (
	statusConditionUpdaterName = "status-condition-updater"
)

// NewStatusConditionUpdater creates a new ConditionUpdater which implements the Runnable interface to be
// executed at an interval by a RunnableTicker.
func NewStatusConditionUpdater(client client.Client, componentReconcilers []reconcilers.ComponentReconciler) ConditionUpdater {
	return &statusConditionUpdater{
		client:               client,
		componentReconcilers: componentReconcilers,
		Logger:               ctrl.Log.WithName(statusConditionUpdaterName),
	}
}

// <PERSON> runs the status condition update procedure for the given instance.
func (s *statusConditionUpdater) Run(ctx context.Context, instance *clv1beta1.Instance) error {
	changed := false
	for _, reconciler := range s.componentReconcilers {
		changed = reconciler.UpdateStatusConditions(ctx, instance) || changed
	}

	if !changed {
		return nil
	}

	err := s.client.Status().Update(ctx, instance)
	if err != nil {
		s.Logger.Error(err, "Failed to update instance status", "instance", instance.Name)
	}

	return err
}

// PreRun runs once before the first Run call in the cycle.
func (s *statusConditionUpdater) PreRun(context.Context) error {
	return nil
}

// GetName returns the name of the updater.
func (s *statusConditionUpdater) GetName() string {
	return statusConditionUpdaterName
}
