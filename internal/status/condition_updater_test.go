package status_test

import (
	"context"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/fake"
	"github.tools.sap/perfx/cloud-logging-operator/internal/reconcilers"
	"github.tools.sap/perfx/cloud-logging-operator/internal/status"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Test status condition updater", func() {
	var (
		statusConditionUpdater   status.ConditionUpdater
		componentReconcilerMock1 *fake.ComponentReconcilerMock
		componentReconcilerMock2 *fake.ComponentReconcilerMock
		instance                 *clv1beta1.Instance
		instanceNamespacedName   types.NamespacedName
		ctx                      = context.Background()
	)

	BeforeEach(func() {
		componentReconcilerMock1 = fake.NewComponentReconcilerMock([]string{})
		componentReconcilerMock2 = fake.NewComponentReconcilerMock([]string{})
		statusConditionUpdater = status.NewStatusConditionUpdater(k8sClient, []reconcilers.ComponentReconciler{
			componentReconcilerMock1, componentReconcilerMock2})

		instance = &clv1beta1.Instance{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "instance-test",
				Namespace: "default",
			},
			Spec: clv1beta1.InstanceSpec{
				OpenSearch: clv1beta1.OpenSearch{
					Cluster: clv1beta1.Cluster{
						NodePools: []clv1beta1.NodePool{
							{
								Roles: []clv1beta1.NodeRole{"data"},
							},
						},
					},
				},
			},
		}

		instanceNamespacedName = types.NamespacedName{
			Name:      instance.Name,
			Namespace: instance.Namespace,
		}

		Expect(k8sClient.Create(context.Background(), instance)).To(Succeed())
	})

	AfterEach(func() {
		Expect(k8sClient.Delete(context.Background(), instance)).To(Succeed())
	})

	Context("Run", func() {
		When("a reconciler reports a status condition change", func() {
			BeforeEach(func() {
				componentReconcilerMock1.PrepareUpdateStatusCondition(true, metav1.Condition{
					Type:   "condition1",
					Status: metav1.ConditionTrue,
					Reason: "TestReason",
				})
			})

			It("should update the status of the instance in the cluster", func() {
				Expect(statusConditionUpdater.Run(ctx, instance)).To(Succeed())
				Expect(componentReconcilerMock1.IsUpdateStatusConditionsCalled).Should(BeTrue())
				Expect(componentReconcilerMock2.IsUpdateStatusConditionsCalled).Should(BeTrue())

				updatedInstance := &clv1beta1.Instance{}
				err := k8sClient.Get(ctx, instanceNamespacedName, updatedInstance)
				Expect(err).NotTo(HaveOccurred())
				Expect(updatedInstance.Status.Conditions).To(HaveLen(1))
				Expect(updatedInstance.Status.Conditions[0].Type).To(Equal("condition1"))
				Expect(updatedInstance.Status.Conditions[0].Status).To(Equal(metav1.ConditionTrue))
				Expect(updatedInstance.Status.Conditions[0].Reason).To(Equal("TestReason"))
			})
		})

		When("no reconciler reports a status condition change", func() {
			BeforeEach(func() {
				componentReconcilerMock1.PrepareUpdateStatusCondition(false, metav1.Condition{})
				componentReconcilerMock2.PrepareUpdateStatusCondition(false, metav1.Condition{})
			})

			It("should not update the status of the instance in the cluster", func() {
				Expect(statusConditionUpdater.Run(ctx, instance)).To(Succeed())
				Expect(componentReconcilerMock1.IsUpdateStatusConditionsCalled).Should(BeTrue())
				Expect(componentReconcilerMock2.IsUpdateStatusConditionsCalled).Should(BeTrue())

				updatedInstance := &clv1beta1.Instance{}
				err := k8sClient.Get(ctx, instanceNamespacedName, updatedInstance)
				Expect(err).NotTo(HaveOccurred())
				Expect(updatedInstance.Status.Conditions).To(BeEmpty())
			})
		})
	})
})
