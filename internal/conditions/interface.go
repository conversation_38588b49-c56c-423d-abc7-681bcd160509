package conditions

import (
	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ConditionHelper is an interface that provides methods to set and get conditions on an Instance CR.
type ConditionHelper interface {
	// GetCondition gets a condition by its type or returns nil if it does not exist.
	GetCondition(instance *clv1beta1.Instance, conditionName string) *metav1.Condition

	// GetConditionStatus returns the status of a condition or ConditionUnknown, if it does not exist
	GetConditionStatus(instance *clv1beta1.Instance, conditionName string) metav1.ConditionStatus

	// CheckAllConditionsTrue checks whether all given conditions have status true
	CheckAllConditionsTrue(instance *clv1beta1.Instance, conditionNames ...string) bool

	// SetCondition adds or updates a condition for the given instance.
	SetCondition(instance *clv1beta1.Instance, condition metav1.Condition) bool

	// SetReconcileCondition sets a condition for a given reconcile result.
	// It is a convenient method to avoid code duplications / deviations across reconcilers
	SetReconcileCondition(instance *clv1beta1.Instance, conditionName string,
		result reconciler.CombinedResult, labels ...string) bool

	// RemoveCondition removes a condition from the given instance.
	RemoveCondition(instance *clv1beta1.Instance, conditionName string) bool

	// SortConditions sorts the conditions by type to improve readability of the instance CR.
	SortConditions(instance *clv1beta1.Instance)
}
