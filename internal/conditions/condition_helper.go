package conditions

import (
	"fmt"
	"sort"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type conditionHelper struct{}

// NewConditionHelper provides a new helper for working with conditions.
// It only works on the local representation of the instance CR. Reading or
// writing changes from/to the K8s cluster has to be implemented elsewhere.
func NewConditionHelper() ConditionHelper {
	return &conditionHelper{}
}

// GetCondition gets a condition by its type or returns nil if it does not exist.
func (c *conditionHelper) GetCondition(instance *clv1beta1.Instance,
	conditionType string) *metav1.Condition {
	return meta.FindStatusCondition(instance.Status.Conditions, conditionType)
}

// GetConditionStatus returns the status of a condition or ConditionUnknown, if it does not exist
func (c *conditionHelper) GetConditionStatus(instance *clv1beta1.Instance,
	conditionType string) metav1.ConditionStatus {
	if condition := meta.FindStatusCondition(instance.Status.Conditions, conditionType); condition != nil {
		return condition.Status
	}
	return metav1.ConditionUnknown
}

// CheckAllConditionsTrue checks whether all given conditions have status true
func (c *conditionHelper) CheckAllConditionsTrue(instance *clv1beta1.Instance,
	conditionTypes ...string) bool {
	for _, conditionType := range conditionTypes {
		if c.GetConditionStatus(instance, conditionType) != metav1.ConditionTrue {
			return false
		}
	}
	return true
}

// SetCondition adds or updates a condition for the given instance.
func (c *conditionHelper) SetCondition(
	instance *clv1beta1.Instance,
	condition metav1.Condition,
) bool {
	return meta.SetStatusCondition(&instance.Status.Conditions, condition)
}

// SetReconcileCondition sets a condition for a given reconcile result.
// It is a convenient method to avoid code duplications / deviations across reconcilers.
// Labels can be added to provide component/reconciler specific details.
func (c *conditionHelper) SetReconcileCondition(instance *clv1beta1.Instance,
	conditionName string, result reconciler.CombinedResult, labels ...string) bool {
	// Determine condition based on error and result:
	// error                -> false
	// requeue              -> unknown (TODO: should we use false here instead?)
	// no error, no requeue -> true

	if result.Err != nil {
		return c.SetCondition(instance, metav1.Condition{
			Type:    conditionName,
			Status:  metav1.ConditionFalse,
			Reason:  Failed,
			Message: fmt.Sprintf("Error: %s", result.Err.Error()),
		})
	}

	if result.Result.Requeue || result.Result.RequeueAfter > 0 {
		return c.SetCondition(instance, metav1.Condition{
			Type:    conditionName,
			Status:  metav1.ConditionUnknown,
			Reason:  Requeued,
			Message: "Requeued reconciliation",
		})
	}

	labelsStr := ""
	if len(labels) > 0 {
		labelsStr = fmt.Sprintf(" (%s)", utils.Join(labels, ", "))
	}

	return c.SetCondition(instance, metav1.Condition{
		Type:    conditionName,
		Status:  metav1.ConditionTrue,
		Reason:  Succeeded,
		Message: fmt.Sprintf("Successfully reconciled resources%s", labelsStr),
	})
}

// RemoveCondition removes a condition from the given instance.
func (c *conditionHelper) RemoveCondition(instance *clv1beta1.Instance,
	conditionType string) bool {
	return meta.RemoveStatusCondition(&instance.Status.Conditions, conditionType)
}

// SortConditions sorts the conditions by type to improve readability of the instance CR
func (c *conditionHelper) SortConditions(instance *clv1beta1.Instance) {
	sort.Slice(instance.Status.Conditions, func(i, j int) bool {
		return instance.Status.Conditions[i].Type < instance.Status.Conditions[j].Type
	})
}
