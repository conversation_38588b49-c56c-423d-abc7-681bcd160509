package conditions_test

import (
	"errors"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"

	"github.com/cisco-open/operator-tools/pkg/reconciler"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("ConditionHelper", func() {
	const (
		testCondition1 = "test/condition1"
		testCondition2 = "test/condition2"
		testCondition3 = "test/condition3"
	)

	var (
		instance        *clv1beta1.Instance
		conditionHelper conditions.ConditionHelper
		reconcileResult reconciler.CombinedResult
	)

	BeforeEach(func() {
		instance = &clv1beta1.Instance{
			Status: clv1beta1.InstanceStatus{
				Conditions: []metav1.Condition{
					{
						Type:   testCondition1,
						Status: metav1.ConditionTrue,
					},
					{
						Type:   testCondition2,
						Status: metav1.ConditionFalse,
					},
				},
			},
		}
		conditionHelper = conditions.NewConditionHelper()
	})

	Context("GetCondition", func() {
		When("getting the instance condition by condition type", func() {
			It("should return the expected condition", func() {
				condition := conditionHelper.GetCondition(instance, testCondition1)
				Expect(condition.Type).Should(Equal(testCondition1))
				Expect(condition.Status).Should(Equal(metav1.ConditionTrue))
			})
		})
	})

	Context("GetConditionStatus", func() {
		When("getting instance condition status for valid condition type", func() {
			It("should return the corresponding condition status", func() {
				status := conditionHelper.GetConditionStatus(instance, testCondition1)
				Expect(status).Should(Equal(metav1.ConditionTrue))
			})
		})

		When("getting instance condition status for invalid condition type", func() {
			It("should return an unknown status", func() {
				status := conditionHelper.GetConditionStatus(instance, "someRandomType")
				Expect(status).Should(Equal(metav1.ConditionUnknown))
			})
		})
	})

	Context("CheckAllConditionsTrue", func() {
		When("all instance conditions for the checked condition types are true", func() {
			BeforeEach(func() {
				instance = &clv1beta1.Instance{
					Status: clv1beta1.InstanceStatus{
						Conditions: []metav1.Condition{
							{
								Type:   testCondition1,
								Status: metav1.ConditionTrue,
							},
							{
								Type:   testCondition2,
								Status: metav1.ConditionTrue,
							},
							{
								Type:   testCondition3,
								Status: metav1.ConditionFalse,
							},
						},
					},
				}
			})

			It("should return true", func() {
				allConditionTrue := conditionHelper.CheckAllConditionsTrue(instance, testCondition1, testCondition2)
				Expect(allConditionTrue).Should(BeTrue())
			})
		})

		When("any of the instance conditions for the checked condition types is false", func() {
			It("should return false", func() {
				allConditionTrue := conditionHelper.CheckAllConditionsTrue(instance, testCondition1, testCondition2)
				Expect(allConditionTrue).Should(BeFalse())
			})
		})
	})

	Context("SetCondition", func() {
		When("setting a new instance condition", func() {
			It("should add the new condition", func() {
				newCondition := metav1.Condition{
					Type:   "newCondition",
					Status: metav1.ConditionTrue,
				}

				changed := conditionHelper.SetCondition(instance, newCondition)
				Expect(changed).Should(BeTrue())
				Expect(instance.Status.Conditions).Should(HaveLen(3))
				Expect(instance.Status.Conditions[2].Type).Should(Equal("newCondition"))
				Expect(instance.Status.Conditions[2].Status).Should(Equal(metav1.ConditionTrue))
			})
		})

		When("setting an existing instance condition", func() {
			It("should update the condition", func() {
				updatedCondition := metav1.Condition{
					Type:   testCondition1,
					Status: metav1.ConditionFalse,
				}

				changed := conditionHelper.SetCondition(instance, updatedCondition)
				Expect(changed).Should(BeTrue())
				Expect(instance.Status.Conditions).Should(HaveLen(2))
				Expect(instance.Status.Conditions[0].Type).Should(Equal(testCondition1))
				Expect(instance.Status.Conditions[0].Status).Should(Equal(metav1.ConditionFalse))

				changed = conditionHelper.SetCondition(instance, updatedCondition)
				Expect(changed).Should(BeFalse())
			})
		})
	})

	Context("SetReconcileCondition", func() {
		When("the reconciler result contains no error", func() {
			BeforeEach(func() {
				reconcileResult = reconciler.CombinedResult{Err: nil}
			})

			When("in the reconciler result no requeue is scheduled", func() {
				BeforeEach(func() {
					reconcileResult.Result = reconcile.Result{}
				})

				It("should update the instance condition entries to a successful state", func() {
					conditionHelper.SetReconcileCondition(instance, testCondition1, reconcileResult, "Present")
					Expect(instance.Status.Conditions[0].Status).To(Equal(metav1.ConditionTrue))
					Expect(instance.Status.Conditions[0].Reason).To(Equal(conditions.Succeeded))
					Expect(instance.Status.Conditions[0].Message).To(Equal("Successfully reconciled resources (Present)"))
				})
			})

			When("in the reconciler result a requeue is scheduled", func() {
				BeforeEach(func() {
					reconcileResult.Result = reconcile.Result{Requeue: true}
				})

				It("should update the instance condition entries to an unknown state", func() {
					conditionHelper.SetReconcileCondition(instance, testCondition1, reconcileResult)
					Expect(instance.Status.Conditions[0].Status).To(Equal(metav1.ConditionUnknown))
					Expect(instance.Status.Conditions[0].Reason).To(Equal(conditions.Requeued))
					Expect(instance.Status.Conditions[0].Message).To(Equal("Requeued reconciliation"))
				})
			})

			When("in the reconciler result requeueAfter is greater than 0", func() {
				BeforeEach(func() {
					reconcileResult.Result = reconcile.Result{RequeueAfter: time.Minute}
				})

				It("should update the instance condition entries to an unknown state", func() {
					conditionHelper.SetReconcileCondition(instance, testCondition1, reconcileResult)
					Expect(instance.Status.Conditions[0].Status).To(Equal(metav1.ConditionUnknown))
					Expect(instance.Status.Conditions[0].Reason).To(Equal(conditions.Requeued))
					Expect(instance.Status.Conditions[0].Message).To(Equal("Requeued reconciliation"))
				})
			})
		})

		When("the reconciler result contains an error", func() {
			BeforeEach(func() {
				reconcileResult = reconciler.CombinedResult{Result: reconcile.Result{}, Err: errors.New("thereIsAResultError")}
			})

			It("should update the instance condition entries to a failed state", func() {
				conditionHelper.SetReconcileCondition(instance, testCondition1, reconcileResult)
				Expect(instance.Status.Conditions[0].Status).To(Equal(metav1.ConditionFalse))
				Expect(instance.Status.Conditions[0].Reason).To(Equal(conditions.Failed))
				Expect(instance.Status.Conditions[0].Message).To(Equal("Error: thereIsAResultError"))
			})
		})
	})

	Context("RemoveCondition", func() {
		When("removing an existing condition", func() {
			It("should remove the condition", func() {
				changed := conditionHelper.RemoveCondition(instance, testCondition1)
				Expect(changed).Should(BeTrue())
				Expect(instance.Status.Conditions).Should(HaveLen(1))
				Expect(instance.Status.Conditions[0].Type).Should(Equal(testCondition2))
			})
		})

		When("removing a non-existing condition", func() {
			It("should do nothing", func() {
				changed := conditionHelper.RemoveCondition(instance, "unknownCondition")
				Expect(changed).Should(BeFalse())
				Expect(instance.Status.Conditions).Should(HaveLen(2))
			})
		})
	})

	Context("SortConditions", func() {
		When("the list of conditions is not sorted yet", func() {
			BeforeEach(func() {
				instance = &clv1beta1.Instance{
					Status: clv1beta1.InstanceStatus{
						Conditions: []metav1.Condition{
							{
								Type:   testCondition3,
								Status: metav1.ConditionTrue,
							},
							{
								Type:   testCondition1,
								Status: metav1.ConditionTrue,
							},
							{
								Type:   testCondition2,
								Status: metav1.ConditionFalse,
							},
						},
					},
				}
			})

			It("should sort the conditions by type", func() {
				conditionHelper.SortConditions(instance)
				Expect(instance.Status.Conditions).Should(HaveLen(3))
				Expect(instance.Status.Conditions[0].Type).Should(Equal(testCondition1))
				Expect(instance.Status.Conditions[1].Type).Should(Equal(testCondition2))
				Expect(instance.Status.Conditions[2].Type).Should(Equal(testCondition3))
			})
		})
	})
})
