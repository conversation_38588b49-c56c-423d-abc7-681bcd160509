package templating

import (
	"bytes"
	"fmt"
	"path/filepath"
	"sync"
	"text/template"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/yaml"
)

type TemplatedConfig struct {
	configTemplates []configTemplate
	mux             sync.Mutex
}

type TemplatedConfigMode int

const (
	//nolint:revive // keep all CAPS
	PARSE_AS_FILES TemplatedConfigMode = iota
	//nolint:revive // keep all CAPS
	MERGE_ALL
)

type configTemplate struct {
	template *template.Template
	file     string
	path     string
}

// NewTemplatedConfig creates a new TemplatedConfig instance for parsing and merging one or more template files into
// a key:value configuration map to be stored in a Secret or ConfigMap.
func NewTemplatedConfig(folder string, files ...string) TemplatedConfig {
	configTemplates := make([]configTemplate, 0)
	for _, file := range files {
		path := filepath.Join(folder, file)
		configTemplates = append(configTemplates, configTemplate{
			template: nil,
			file:     file,
			path:     path,
		})
	}

	return TemplatedConfig{
		configTemplates: configTemplates,
	}
}

// Merge parses (once) and merges all registered templates with the given 'vars' data.
// There are two modes available for conducting the result:
//   - PARSE_AS_FILES: Add a file entry to the result map with the template filename as key and the processed
//     data as string value.
//   - MERGE_ALL: Unmarshal the processed data to the result map for each template. This requires the processed
//     template data to be a simple key:value map. All values are strings (requirement for ConfigMaps).
func (t *TemplatedConfig) Merge(mode TemplatedConfigMode, vars any) (map[string]string, error) {
	// prevent concurrent map access when using more than one worker
	t.mux.Lock()
	defer t.mux.Unlock()

	result := make(map[string]string)

	// parse all templates that were not parsed yet
	err := t.parseFilesIfRequired()
	if err != nil {
		return result, err
	}

	// execute each template with the given vars and add it to the result map
	for _, configTemplate := range t.configTemplates {
		var buffer bytes.Buffer
		err = configTemplate.template.Execute(&buffer, vars)
		if err != nil {
			return result, fmt.Errorf("failed to apply template '%s' to the specified data object. Error: %s", configTemplate.path, err.Error())
		}

		// Add processed data to the result map using the specified mode
		switch mode {
		case PARSE_AS_FILES:
			result[configTemplate.file] = buffer.String()
		case MERGE_ALL:
			err = yaml.Unmarshal(buffer.Bytes(), &result)
			if err != nil {
				return result, fmt.Errorf("failed to unmarshal processed template '%s'. Error: %s", configTemplate.path, err.Error())
			}
		}
	}
	return result, nil
}

// PopulateConfigMap executes the Merge operation for the given vars data with the given mode. (See Merge method comment
// for details on available modes). The result gets stored to the Data field of the provided ConfigMap.
// Any existing data get overwritten.
func (t *TemplatedConfig) PopulateConfigMap(mode TemplatedConfigMode, vars any, configMap *corev1.ConfigMap) error {
	data, err := t.Merge(mode, vars)
	if err != nil {
		return err
	}
	configMap.Data = data
	return nil
}

// PopulateSecret executes the Merge operation for the given vars data with the given mode. (See Merge method comment
// for details on available modes). The result gets stored into the StringData field of the provided Secret.
// Any existing data get overwritten.
func (t *TemplatedConfig) PopulateSecret(mode TemplatedConfigMode, vars any, secret *corev1.Secret) error {
	data, err := t.Merge(mode, vars)
	if err != nil {
		return err
	}
	secret.Data = make(map[string][]byte)
	secret.StringData = data
	return nil
}

// parseFilesIfRequired parses and stores each template file, that was not stored yet.
func (t *TemplatedConfig) parseFilesIfRequired() error {
	for index, configTemplate := range t.configTemplates {
		if configTemplate.template == nil {
			parsedTemplate, err := template.New(configTemplate.file).ParseFiles(configTemplate.path)
			if err != nil {
				return fmt.Errorf("failed to parse template file '%s'. Error: %s", configTemplate.path, err.Error())
			}
			configTemplate.template = parsedTemplate
			t.configTemplates[index] = configTemplate
		}
	}
	return nil
}
