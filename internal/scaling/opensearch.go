package scaling

import (
	"context"
	"errors"
	"fmt"
	"math"
	"time"

	clv1beta1 "github.tools.sap/perfx/cloud-logging-operator/api/v1beta1"
	"github.tools.sap/perfx/cloud-logging-operator/internal/conditions"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services"
	clientModels "github.tools.sap/perfx/cloud-logging-operator/internal/services/clients/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/services/models"
	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"

	"github.com/go-logr/logr"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/util/retry"
	"k8s.io/utils/ptr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type openSearchAutoscaler struct {
	client                    client.Client
	openSearchTopologyService services.OpenSearchTopologyService
	openSearchService         services.OpenSearchService
	conditionHelper           conditions.ConditionHelper
	timeProvider              services.TimeProvider
	logr.Logger
}

type diskStats struct {
	maxDataNodeCount     int32
	minDataNodeCount     int32
	currentDataNodeCount int32
	diskUsageThreshold   float64
	currentDiskUsage     float64
}

const (
	autoscalerName = "opensearch-autoscaler"

	// minimumAge is the minimum age an instance must have before autoscaling is attempted.
	minimumAge = 5 * time.Minute
)

// NewOpenSearchAutoscaler creates a new OpenSearchAutoscaler which implements the Runnable interface to be
// executed at an interval by an RunnableTicker.
func NewOpenSearchAutoscaler(client client.Client, openSearchTopologyService services.OpenSearchTopologyService,
	openSearchService services.OpenSearchService, conditionHelper conditions.ConditionHelper,
	timeProvider services.TimeProvider) OpenSearchAutoscaler {
	return &openSearchAutoscaler{
		client:                    client,
		openSearchTopologyService: openSearchTopologyService,
		openSearchService:         openSearchService,
		conditionHelper:           conditionHelper,
		timeProvider:              timeProvider,
		Logger:                    ctrl.Log.WithName(autoscalerName),
	}
}

// Run runs the autoscaling procedure for the given instance.
// It also updates the OpenSearchAutoscaling condition in the instance status.
// Autoscaling is skipped if the OpenSearch resources are not reconciled or available.
func (s *openSearchAutoscaler) Run(ctx context.Context, instance *clv1beta1.Instance) error {
	log := s.Logger.WithValues("instance", instance.Name)

	// Only proceed if the OpenSearch resources are successfully reconciled and available
	if s.conditionHelper.GetConditionStatus(instance, conditions.OpenSearchReconciled) != metav1.ConditionTrue ||
		s.conditionHelper.GetConditionStatus(instance, conditions.OpenSearchAvailable) != metav1.ConditionTrue {
		log.V(1).Info("Skipping autoscaling as OpenSearch resources are not reconciled or available")
		return nil
	}

	// Skip if the instance is too young.
	// This prevents errors in edge cases where the instance is not fully initialized yet.
	if s.timeProvider.Now().Before(instance.CreationTimestamp.Add(minimumAge)) {
		log.V(1).Info("Instance is not old enough for autoscaling", "creationTimestamp", instance.CreationTimestamp)
		return nil
	}

	err := s.scaleInstance(ctx, log, instance)
	if err != nil {
		updErr := s.updateCondition(ctx, instance, metav1.Condition{
			Type:    conditions.OpenSearchAutoscaling,
			Status:  metav1.ConditionFalse,
			Reason:  conditions.Failed,
			Message: fmt.Sprintf("Failed to scale instance: %s", err.Error()),
		})

		if updErr != nil {
			log.Error(err, "Failed to update condition after autoscaling error")
		}
	}

	return err
}

// PreRun runs once before the first Run call in the cycle.
func (s *openSearchAutoscaler) PreRun(context.Context) error {
	s.Logger.Info("Running OpenSearch autoscaler for all instances")
	return nil
}

// GetName returns the name of the autoscaler.
func (s *openSearchAutoscaler) GetName() string {
	return autoscalerName
}

// scaleInstance scales the given OpenSearch instance based on the disk usage.
func (s *openSearchAutoscaler) scaleInstance(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance) error {
	group, found, err := s.findNodeGroupWithAutoscaling(ctx, instance)
	if err != nil {
		return fmt.Errorf("failed to find node-group with autoscaling: %w", err)
	}

	if !found {
		return s.updateCondition(ctx, instance, metav1.Condition{
			Type:    conditions.OpenSearchAutoscaling,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.Disabled,
			Message: "Instance has no node-groups with autoscaling, skipping autoscaling",
		})
	}

	ds, err := s.getCurrentStats(ctx, instance, group)
	if err != nil {
		return fmt.Errorf("failed to get current stats: %w", err)
	}

	err = s.checkAndFixCorruptEvacuation(ctx, log, instance, &group, ds)
	if err != nil {
		return fmt.Errorf("failed to check and fix corrupt evacuation: %w", err)
	}

	logWithNodeGroup := log.WithValues("nodeGroup", group.NodePool.Name)
	if ds.currentDiskUsage < ds.diskUsageThreshold {
		return s.scaleIn(ctx, logWithNodeGroup, instance, group, ds)
	}
	return s.scaleOut(ctx, logWithNodeGroup, instance, group, ds)
}

// checkAndFixCorruptEvacuation checks if the evacuation is stuck and resets it if necessary.
// We consider the evacuation to be stuck if the configured evacuating node is not the target node.
func (s *openSearchAutoscaler) checkAndFixCorruptEvacuation(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, group *models.NodeGroup, ds diskStats) error {
	excludedNodes, err := s.openSearchService.GetExcludedNodes(ctx, instance)
	if err != nil {
		log.Error(err, "Failed to get evacuating node")
		return err
	}

	if len(excludedNodes) == 0 {
		return nil
	}

	if len(excludedNodes) > 1 {
		log.Info("Multiple nodes are excluded from routing allocation. Resetting evacuation")
		err = s.openSearchService.SetExcludedNodes(ctx, instance)
		if err != nil {
			return fmt.Errorf("failed to reset evacuation: %w", err)
		}
		return nil
	}

	if !utils.Contains(excludedNodes, group.GetStatefulSetPodName(ds.currentDataNodeCount-1)) {
		log.Info("Evacuating node is not the target node. Resetting evacuation")
		err = s.openSearchService.SetExcludedNodes(ctx, instance)
		if err != nil {
			return fmt.Errorf("failed to reset evacuation: %w", err)
		}
	}

	return nil
}

// findNodeGroupWithAutoscaling returns the first node-group with autoscaling enabled.
// If no node-group with autoscaling is found, it returns nil.
func (s *openSearchAutoscaler) findNodeGroupWithAutoscaling(ctx context.Context, instance *clv1beta1.Instance) (models.NodeGroup, bool, error) {
	topology, err := s.openSearchTopologyService.Discover(ctx, instance)
	if err != nil {
		return models.NodeGroup{}, false, fmt.Errorf("failed to discover the current topology: %w", err)
	}

	for _, group := range topology.NodeGroups {
		if group.NodePool == nil {
			continue
		}

		// Skip NodeGroups without autoscaling
		if group.NodePool.Autoscaler.MinReplicas == group.NodePool.Autoscaler.MaxReplicas {
			continue
		}

		return group, true, nil
	}
	return models.NodeGroup{}, false, nil
}

// scaleIn scales in the OpenSearch instance by evacuating a data node, if eligible.
func (s *openSearchAutoscaler) scaleIn(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, group models.NodeGroup, ds diskStats) error {
	health, err := s.openSearchService.GetHealth(ctx, instance)
	if err != nil {
		return fmt.Errorf("failed to get OpenSearch health: %w", err)
	}

	if health.RelocatingShards > 0 {
		log.Info("Scaling postponed due to relocating shards")
		return nil
	}

	excludedNodes, err := s.openSearchService.GetExcludedNodes(ctx, instance)
	if err != nil {
		return fmt.Errorf("failed to determine node exclusion status: %w", err)
	}

	if utils.Contains(excludedNodes, group.GetStatefulSetPodName(ds.currentDataNodeCount-1)) {
		err = s.finalizeOngoingEvacuation(ctx, log, instance, group, ds)
		if err != nil {
			return fmt.Errorf("failed to finalize ongoing evacuation: %w", err)
		}
	} else {
		err = s.triggerNewEvacuationIfEligible(ctx, log, instance, group, ds)
		if err != nil {
			return fmt.Errorf("failed to trigger new evacuation: %w", err)
		}
	}
	return nil
}

// finalizeOngoingEvacuation checks if the ongoing evacuation is complete and scales in the OpenSearch instance afterwards.
func (s *openSearchAutoscaler) finalizeOngoingEvacuation(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, group models.NodeGroup, ds diskStats) error {
	completed, err := s.openSearchService.IsEvacuationComplete(ctx, instance, group.GetStatefulSetPodName(ds.currentDataNodeCount-1))
	if err != nil {
		return fmt.Errorf("failed to determine evacuation status: %w", err)
	}
	if !completed {
		log.Info("Waiting for completion of previous evacuation")
		return nil
	}

	err = s.scaleInStatefulSet(ctx, log, instance, group, ds)
	if err != nil {
		return fmt.Errorf("failed to scale-in OpenSearch node-group '%s': %w", group.NodePool.Name, err)
	}

	if err = s.openSearchService.SetExcludedNodes(ctx, instance); err != nil {
		return fmt.Errorf("failed to reset evacuation: %w", err)
	}

	return nil
}

// triggerNewEvacuationIfEligible triggers a new evacuation if the cluster is eligible for scaling in and
// the disk usage after scaling in is expected to be below the threshold.
func (s *openSearchAutoscaler) triggerNewEvacuationIfEligible(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, group models.NodeGroup, ds diskStats) error {
	eligible, reason, err := s.isClusterEvacuationEligible(ctx, log, instance, ds)
	if err != nil {
		return fmt.Errorf("failed to determine if cluster is eligible for evacuation: %w", err)
	}

	if !eligible {
		return s.updateCondition(ctx, instance, metav1.Condition{
			Type:    conditions.OpenSearchAutoscaling,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.NotEligible,
			Message: fmt.Sprintf("Not eligible for an evacuation: %s", reason),
		})
	}

	scaleInCoolDownPercentage := 0.20
	diskUsageAfterScaleIn := ds.currentDiskUsage * float64(ds.currentDataNodeCount) / float64(ds.currentDataNodeCount-1)

	if diskUsageAfterScaleIn > (ds.diskUsageThreshold - scaleInCoolDownPercentage) {
		log.V(1).Info("No need to scale in. The disks are optimally used")
		return s.updateCondition(ctx, instance, metav1.Condition{
			Type:    conditions.OpenSearchAutoscaling,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.NotEligible,
			Message: "The disks are optimally used",
		})
	}

	err = s.evacuate(ctx, log, instance, group, ds)
	if err != nil {
		return fmt.Errorf("failed to update OpenSearch for node evacuation: %w", err)
	}

	return nil
}

// isClusterEvacuationEligible checks if the cluster is eligible for scaling in.
// The cluster is eligible if it is not already using the minimum allowed number of nodes and is in a healthy state.
// It returns a boolean indicating eligibility, a reason string, and an error if any occurred.
func (s *openSearchAutoscaler) isClusterEvacuationEligible(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, ds diskStats) (bool, string, error) {
	if ds.currentDataNodeCount <= ds.minDataNodeCount {
		log.V(1).Info("No need to scale in. Cluster is already using minimum allowed number of data nodes")
		return false, "using minimum data node count", nil
	}

	health, err := s.openSearchService.GetHealth(ctx, instance)
	if err != nil {
		return false, "", fmt.Errorf("failed to get OpenSearch health: %w", err)
	}

	if health.Status != clientModels.HealthStatusGreen {
		log.Info("Scaling postponed due to cluster not being green")
		return false, "cluster health is not green", nil
	}

	return true, "", nil
}

// scaleOut scales out the OpenSearch instance if the disk usage is above the threshold.
func (s *openSearchAutoscaler) scaleOut(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, group models.NodeGroup, ds diskStats) error {
	var additionalNodes int32
	if ds.currentDataNodeCount < ds.maxDataNodeCount {
		// remainingPercentage eliminates two immediate/consecutive scale-outs by avoiding
		// cases where the disk usage post scale-out is exactly max percentage
		remainingPercentage := 0.05
		if ds.diskUsageThreshold <= 0.05 {
			remainingPercentage = 0.00
		}

		calculatedDesiredNode := int32(math.Ceil(ds.currentDiskUsage * float64(ds.currentDataNodeCount) /
			(ds.diskUsageThreshold - remainingPercentage)))

		desiredNodes := utils.Constrain(calculatedDesiredNode, ds.minDataNodeCount, ds.maxDataNodeCount)
		additionalNodes = desiredNodes - ds.currentDataNodeCount
	}

	if additionalNodes == 0 {
		log.V(1).Info("No need to scale out. The disks are optimally used")
		return s.updateCondition(ctx, instance, metav1.Condition{
			Type:    conditions.OpenSearchAutoscaling,
			Status:  metav1.ConditionTrue,
			Reason:  conditions.NotEligible,
			Message: "The disks are optimally used",
		})
	}

	err := s.scaleOutStatefulSet(ctx, log, instance, group, additionalNodes, ds)
	if err != nil {
		return fmt.Errorf("failed to scale out OpenSearch node-group '%s': %w", group.NodePool.Name, err)
	}
	return nil
}

// scaleInStatefulSet scales in the given StatefulSet by reducing the number of replicas by 1.
func (s *openSearchAutoscaler) scaleInStatefulSet(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, group models.NodeGroup, ds diskStats) error {
	newReplicas := ds.currentDataNodeCount - 1
	if newReplicas < ds.minDataNodeCount {
		log.V(1).Info("No need to scale in. Cluster is already using minimum allowed number of nodes")
		return nil
	}

	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		if err := s.client.Get(ctx, types.NamespacedName{
			Namespace: group.StatefulSet.Namespace,
			Name:      group.StatefulSet.Name,
		}, group.StatefulSet); err != nil {
			return err
		}

		group.StatefulSet.Spec.Replicas = ptr.To(newReplicas)
		return s.client.Update(ctx, group.StatefulSet)
	})

	if err != nil {
		return fmt.Errorf("failed to scale-in StatefulSet: %w", err)
	}

	log.Info("Scale in node-group", "oldReplicas", ds.currentDataNodeCount, "newReplicas", newReplicas)

	return s.updateCondition(ctx, instance, metav1.Condition{
		Type:    conditions.OpenSearchAutoscaling,
		Status:  metav1.ConditionTrue,
		Reason:  conditions.ScaleInSucceeded,
		Message: fmt.Sprintf("Scaled in node-group '%s' from %d to %d nodes", group.NodePool.Name, ds.currentDataNodeCount, newReplicas),
	})
}

// scaleOutStatefulSet scales out the given StatefulSet by increasing the number of replicas by the given amount.
func (s *openSearchAutoscaler) scaleOutStatefulSet(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, group models.NodeGroup, additionalNodes int32, ds diskStats) error {
	newReplicas := ds.currentDataNodeCount + additionalNodes

	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		if err := s.client.Get(ctx, types.NamespacedName{
			Namespace: group.StatefulSet.Namespace,
			Name:      group.StatefulSet.Name,
		}, group.StatefulSet); err != nil {
			return err
		}

		group.StatefulSet.Spec.Replicas = ptr.To(newReplicas)
		return s.client.Update(ctx, group.StatefulSet)
	})

	if err != nil {
		return fmt.Errorf("failed to scale-out StatefulSet: %w", err)
	}

	log.Info("Scale out node-group", "oldReplicas", ds.currentDataNodeCount, "newReplicas", newReplicas)

	return s.updateCondition(ctx, instance, metav1.Condition{
		Type:    conditions.OpenSearchAutoscaling,
		Status:  metav1.ConditionTrue,
		Reason:  conditions.ScaleOutSucceeded,
		Message: fmt.Sprintf("Scaled out node-group '%s' from %d to %d nodes", group.NodePool.Name, ds.currentDataNodeCount, newReplicas),
	})
}

// evacuate updates OpenSearch to evacuate the last node in the given node-group.
func (s *openSearchAutoscaler) evacuate(ctx context.Context, log logr.Logger, instance *clv1beta1.Instance, group models.NodeGroup, ds diskStats) error {
	targetNode := group.GetStatefulSetPodName(ds.currentDataNodeCount - 1)

	log.Info("Triggering evacuation for scale-in", "targetNode", targetNode)

	if err := s.openSearchService.SetExcludedNodes(ctx, instance, targetNode); err != nil {
		return fmt.Errorf("failed to evacuate data node: %w", err)
	}

	return s.updateCondition(ctx, instance, metav1.Condition{
		Type:    conditions.OpenSearchAutoscaling,
		Status:  metav1.ConditionTrue,
		Reason:  conditions.EvacuatingNode,
		Message: fmt.Sprintf("Evacuating data node '%s' for scale-in", targetNode),
	})
}

// getCurrentStats returns the current disk usage, node count, and other stats for the given instance and node-group.
func (s *openSearchAutoscaler) getCurrentStats(ctx context.Context, instance *clv1beta1.Instance, group models.NodeGroup) (diskStats, error) {
	currentDiskUsage, err := s.openSearchService.GetDiskUsage(ctx, instance)
	if err != nil {
		return diskStats{}, err
	}

	if group.NodePool.Autoscaler.MinReplicas < 1 {
		return diskStats{}, errors.New("NodePool.Autoscaler.MinReplicas should be >= 1")
	}

	return diskStats{
		maxDataNodeCount:     utils.Constrain(instance.Spec.OpenSearch.Cluster.MaxDataNodes, group.NodePool.Autoscaler.MinReplicas, group.NodePool.Autoscaler.MaxReplicas),
		minDataNodeCount:     group.NodePool.Autoscaler.MinReplicas,
		diskUsageThreshold:   float64(group.NodePool.Autoscaler.DiskUsageThreshold) / 100,
		currentDataNodeCount: ptr.Deref(group.StatefulSet.Spec.Replicas, 1),
		currentDiskUsage:     currentDiskUsage,
	}, nil
}

// updateCondition updates the given condition in the instance status.
func (s *openSearchAutoscaler) updateCondition(ctx context.Context, instance *clv1beta1.Instance, condition metav1.Condition) error {
	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		if err := s.client.Get(ctx, types.NamespacedName{
			Namespace: instance.Namespace,
			Name:      instance.Name,
		}, instance); err != nil {
			return err
		}

		if s.conditionHelper.SetCondition(instance, condition) {
			return s.client.Status().Update(ctx, instance)
		}
		return nil
	})

	if err != nil {
		return fmt.Errorf("failed to update '%s' condition in instance.status: %w", condition.Type, err)
	}

	return nil
}
