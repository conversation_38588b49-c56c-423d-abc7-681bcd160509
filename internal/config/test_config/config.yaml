instanceReconciler:
  requeueDelay: 20m
  maxConcurrentReconciles: 5
images:
  instanceMaintainer: "instance-maintainer:test"
  fluentd: "fluentd:test"
  dataPrepper: "data-prepper:test"
  dashboards:
    stable: "dashboards:1.0.0"
    preview: "dashboards:2.0.0"
    development: "dashboards:2.1.0"
  openSearch:
    stable: "opensearch:1.0.0"
    preview: "opensearch:2.0.0"
    development: "opensearch:2.1.0"
  openSearchInit: "opensearch-init:1.0.0"
  elasticsearchExporter: "elasticsearch-exporter:test"
imagePullSecrets:
  - test-secret-1
  - test-secret-2
imagePullPolicy: "IfNotPresent"
ingress: "test.ingress.sap.com"
allowedProdFeatureFlags: ["skipReconcileContent", "upgradeToOpenSearchV2"]
aliasRolloverEnabled: true
