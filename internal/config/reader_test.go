package config_test

import (
	"time"

	"github.tools.sap/perfx/cloud-logging-operator/internal/config"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Testing Reader", func() {
	var (
		configReader config.Reader
	)

	BeforeEach(func() {
		configReader = config.GetInstance()
	})

	When("configuration file exist", func() {
		It("does not return an error", func() {
			err := configReader.Read("test_config/config.yaml", false)
			Expect(err).ShouldNot(HaveOccurred())
		})

		It("does return the expected instanceReconciler config", func() {
			err := configReader.Read("test_config/config.yaml", false)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(configReader.GetInstanceReconciler().RequeueDelay).Should(Equal(20 * time.Minute))
			Expect(configReader.GetInstanceReconciler().MaxConcurrentReconciles).Should(Equal(5))
		})

		It("does return the expected images", func() {
			err := configReader.Read("test_config/config.yaml", false)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(configReader.GetImages().InstanceMaintainer).Should(Equal("instance-maintainer:test"))
			Expect(configReader.GetImages().Fluentd).Should(Equal("fluentd:test"))
			Expect(configReader.GetImages().DataPrepper).Should(Equal("data-prepper:test"))
			Expect(configReader.GetImages().Dashboards.Stable).Should(Equal("dashboards:1.0.0"))
			Expect(configReader.GetImages().Dashboards.Development).Should(Equal("dashboards:2.1.0"))
			Expect(configReader.GetImages().Dashboards.Preview).Should(Equal("dashboards:2.0.0"))
			Expect(configReader.GetImages().ElasticsearchExporter).Should(Equal("elasticsearch-exporter:test"))
		})

		It("does return the expected image pull secrets", func() {
			err := configReader.Read("test_config/config.yaml", false)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(configReader.GetImagePullSecrets()).Should(ConsistOf("test-secret-1", "test-secret-2"))
		})

		It("does return the expected image pull policy", func() {
			err := configReader.Read("test_config/config.yaml", false)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(configReader.GetImagePullPolicy()).Should(Equal("IfNotPresent"))
		})

		It("does return the expected ingress", func() {
			err := configReader.Read("test_config/config.yaml", false)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(configReader.GetIngress()).Should(Equal("test.ingress.sap.com"))
		})

		It("does return the expected allowed prod feature flags", func() {
			err := configReader.Read("test_config/config.yaml", false)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(configReader.GetAllowedProdFeatureFlags()).Should(HaveLen(2))
			Expect(configReader.GetAllowedProdFeatureFlags()).Should(ContainElement("skipReconcileContent"))
			Expect(configReader.GetAllowedProdFeatureFlags()).Should(ContainElement("upgradeToOpenSearchV2"))
		})

		It("does return the expected aliasRolloverEnabled value", func() {
			err := configReader.Read("test_config/config.yaml", false)
			Expect(err).ShouldNot(HaveOccurred())
			Expect(configReader.GetAliasRolloverEnabled()).Should(BeTrue())
		})
	})

	When("configuration file does not exist", func() {
		When("allowDefault is set to false", func() {
			It("returns an error", func() {
				err := configReader.Read("test_config/not_existing_config.yaml", false)
				Expect(err).Should(HaveOccurred())
			})
		})

		When("allowDefault is set to true", func() {
			It("does not return an error", func() {
				err := configReader.Read("test_config/not_existing_config.yaml", true)
				Expect(err).ShouldNot(HaveOccurred())
			})

			It("does return the default instanceReconciler config", func() {
				err := configReader.Read("test_config/not_existing_config.yaml", true)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(configReader.GetInstanceReconciler().RequeueDelay).Should(Equal(30 * time.Minute))
				Expect(configReader.GetInstanceReconciler().MaxConcurrentReconciles).Should(Equal(1))
			})

			It("does return the default images", func() {
				err := configReader.Read("test_config/not_existing_config.yaml", true)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(configReader.GetImages().InstanceMaintainer).Should(Equal(""))
				Expect(configReader.GetImages().Fluentd).Should(Equal(""))
				Expect(configReader.GetImages().DataPrepper).Should(Equal(""))
				Expect(configReader.GetImages().Dashboards.Stable).Should(Equal(""))
				Expect(configReader.GetImages().OpenSearch.Stable).Should(Equal(""))
				Expect(configReader.GetImages().OpenSearch.Preview).Should(Equal(""))
				Expect(configReader.GetImages().OpenSearch.Preview).Should(Equal(""))
				Expect(configReader.GetImages().Dashboards.Development).Should(Equal(""))
				Expect(configReader.GetImages().OpenSearch.Development).Should(Equal(""))
				Expect(configReader.GetImages().OpenSearchInit).Should(Equal(""))
			})

			It("does return the default image pull secrets", func() {
				err := configReader.Read("test_config/not_existing_config.yaml", true)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(configReader.GetImagePullSecrets()).Should(ConsistOf("perfxregistrycred", "perfxprodregistrycred"))
			})

			It("does return the default image pull policy", func() {
				err := configReader.Read("test_config/not_existing_config.yaml", true)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(configReader.GetImagePullPolicy()).Should(Equal("Always"))
			})

			It("does return the default ingress", func() {
				err := configReader.Read("test_config/not_existing_config.yaml", true)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(configReader.GetIngress()).Should(Equal(""))
			})

			It("does return the default allowed prod feature flags", func() {
				err := configReader.Read("test_config/not_existing_config.yaml", true)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(configReader.GetAllowedProdFeatureFlags()).Should(HaveLen(0))
			})

			It("does return the default aliasRolloverEnabled value", func() {
				err := configReader.Read("test_config/not_existing_config.yaml", true)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(configReader.GetAliasRolloverEnabled()).Should(BeFalse())
			})
		})
	})
})
