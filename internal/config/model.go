package config

import "time"

type RootConfig struct {
	InstanceReconciler      InstanceReconcilerConfig `yaml:"instanceReconciler"`
	Images                  ImagesConfig             `yaml:"images"`
	ImagePullSecrets        []string                 `yaml:"imagePullSecrets"`
	ImagePullPolicy         string                   `yaml:"imagePullPolicy"`
	Ingress                 string                   `yaml:"ingress"`
	AllowedProdFeatureFlags []string                 `yaml:"allowedProdFeatureFlags"`
	AliasRolloverEnabled    bool                     `yaml:"aliasRolloverEnabled"`
}

type InstanceReconcilerConfig struct {
	RequeueDelay            time.Duration `yaml:"requeueDelay"`
	MaxConcurrentReconciles int           `yaml:"maxConcurrentReconciles"`
}

type ImagesConfig struct {
	InstanceMaintainer    string        `yaml:"instanceMaintainer"`
	DataPrepper           string        `yaml:"dataPrepper"`
	Fluentd               string        `yaml:"fluentd"`
	Dashboards            ImageChannels `yaml:"dashboards"`
	OpenSearch            ImageChannels `yaml:"openSearch"`
	OpenSearchInit        string        `yaml:"openSearchInit"`
	ElasticsearchExporter string        `yaml:"elasticsearchExporter"`
}

type ImageChannels struct {
	Stable      string `yaml:"stable"`
	Preview     string `yaml:"preview"`
	Development string `yaml:"development"`
}

const (
	StableChannel      string = "stable"
	PreviewChannel     string = "preview"
	DevelopmentChannel string = "development"
)
