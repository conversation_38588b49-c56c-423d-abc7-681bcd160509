package config

import "time"

// getDefaultConfig returns the default configuration
func getDefaultConfig() RootConfig {
	return RootConfig{
		Images: ImagesConfig{
			InstanceMaintainer: "",
			Fluentd:            "",
			DataPrepper:        "",
			Dashboards: ImageChannels{
				Development: "",
				Preview:     "",
				Stable:      "",
			},
			OpenSearch: ImageChannels{
				Development: "",
				Preview:     "",
				Stable:      "",
			},
			OpenSearchInit:        "",
			ElasticsearchExporter: "",
		},
		ImagePullSecrets: []string{
			"perfxregistrycred",
			"perfxprodregistrycred",
		},
		ImagePullPolicy: "Always",
		InstanceReconciler: InstanceReconcilerConfig{
			RequeueDelay:            30 * time.Minute,
			MaxConcurrentReconciles: 1,
		},
		Ingress:                 "",
		AllowedProdFeatureFlags: []string{},
		AliasRolloverEnabled:    false,
	}
}
