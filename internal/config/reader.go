package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/go-logr/logr"
	"gopkg.in/yaml.v3"
	ctrl "sigs.k8s.io/controller-runtime"
)

type configReader struct {
	data   RootConfig
	logger logr.Logger
}

//nolint:gochecknoglobals // configReaderInstance is a singelton and therefore needs to be a global variable.
var configReaderInstance Reader

func GetInstance() Reader {
	if configReaderInstance == nil {
		configReaderInstance = &configReader{
			data:   getDefaultConfig(),
			logger: ctrl.Log.WithName("config-reader"),
		}
	}
	return configReaderInstance
}

// Read reads a configuration file located in the filesystem.
func (r *configReader) Read(configFile string, allowDefault bool) error {
	// reset the configuration to default
	r.data = getDefaultConfig()

	r.logger.Info("Load configuration", "file", configFile)
	buf, err := os.ReadFile(filepath.Clean(configFile))
	if err != nil {
		if allowDefault {
			r.logger.Info("Configuration file not found, using default configuration")
			return nil
		}
		return fmt.Errorf("failed to read config file '%s'. Error: %w", configFile, err)
	}

	err = yaml.Unmarshal(buf, &r.data)
	if err != nil {
		return fmt.Errorf("failed to unmarshal config file '%s'. Error: %w", configFile, err)
	}
	return err
}

// GetInstanceReconciler gets the instanceReconciler parameters
func (r *configReader) GetInstanceReconciler() InstanceReconcilerConfig {
	return r.data.InstanceReconciler
}

// GetImages gets the images parameter
func (r *configReader) GetImages() ImagesConfig {
	return r.data.Images
}

// GetImagePullSecrets gets the imagePullSecrets parameter
func (r *configReader) GetImagePullSecrets() []string {
	return r.data.ImagePullSecrets
}

// GetImagePullPolicy gets the imagePullPolicy parameter
func (r *configReader) GetImagePullPolicy() string {
	return r.data.ImagePullPolicy
}

// GetAllowedProdFeatureFlags gets the allowedProdFeatureFlags parameter
func (r *configReader) GetAllowedProdFeatureFlags() []string {
	return r.data.AllowedProdFeatureFlags
}

// GetEnableAliasRollover gets the enableAliasRollover parameter
func (r *configReader) GetAliasRolloverEnabled() bool {
	return r.data.AliasRolloverEnabled
}

// GetIngress gets the cluster-wide ingress
func (r *configReader) GetIngress() string {
	return r.data.Ingress
}
