package config

const (
	// instance-maintainer related feature flags:

	FeatureFlagSkipReconcileContent            = "skipReconcileContent"
	FeatureFlagSkipReconcileAccessControl      = "skipReconcileAccessControl"
	FeatureFlagSkipReconcileComposableTemplate = "skipReconcileComposableTemplate"
	FeatureFlagSkipReconcileISMPolicy          = "skipReconcileISMPolicy"
	FeatureFlagSkipReconcileSavedObjects       = "skipReconcileSavedObjects"
	FeatureFlagUseOpenSearchDevelopmentVersion = "useOpenSearchDevelopmentVersion"
	FeatureFlagUpgradeToOpenSearchV2           = "upgradeToOpenSearchV2"
)

func GetSupportedFeatureFlags() []string {
	return []string{
		FeatureFlagSkipReconcileContent,
		FeatureFlagSkipReconcileAccessControl,
		FeatureFlagSkipReconcileComposableTemplate,
		FeatureFlagSkipReconcileISMPolicy,
		FeatureFlagSkipReconcileSavedObjects,
		FeatureFlagUseOpenSearchDevelopmentVersion,
		FeatureFlagUpgradeToOpenSearchV2,
	}
}
