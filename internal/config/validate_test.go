package config_test

import (
	"github.tools.sap/perfx/cloud-logging-operator/internal/config"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var _ = Describe("Testing config validation", func() {
	var (
		configReader config.Reader
	)

	BeforeEach(func() {
		configReader = config.GetInstance()
		err := configReader.Read("test_config/config.yaml", false)
		Expect(err).ShouldNot(HaveOccurred())
	})

	Context("Validate()", func() {
		When("all required images are set", func() {
			It("should not return an error", func() {
				err := config.Validate()
				Expect(err).ShouldNot(HaveOccurred())
			})
		})

		// TODO: Add more test cases for validation errors.
		// Currently, our config cannot easily be changed for testing purposes, so we cannot really
		// test the validation without having tons of test config files.
		// TODO: Refactor config to allow easier testing.
	})
})
