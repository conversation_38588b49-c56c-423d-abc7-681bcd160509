package config

// <PERSON> provides access to configuration values read from a config yaml file.
type Reader interface {
	// Read reads a configuration file located in the filesystem.
	Read(path string, allowDefault bool) error

	// GetInstanceReconciler gets the instanceReconciler parameters
	GetInstanceReconciler() InstanceReconcilerConfig

	// GetImages gets the images parameter
	GetImages() ImagesConfig

	// GetImagePullSecrets gets the imagePullSecrets parameter
	GetImagePullSecrets() []string

	// GetImagePullPolicy gets the imagePullPolicy parameter
	GetImagePullPolicy() string

	// GetAllowedProdFeatureFlags gets the allowedProdFeatureFlags parameter
	GetAllowedProdFeatureFlags() []string

	// GetAliasRolloverEnabled gets the aliasRolloverEnabled parameter
	GetAliasRolloverEnabled() bool

	// GetIngress get the cluster-wide ingress
	GetIngress() string
}
