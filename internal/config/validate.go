package config

import (
	"errors"
	"fmt"

	"github.tools.sap/perfx/cloud-logging-operator/internal/utils"
)

// Validate checks the configuration for required fields and consistency.
func Validate() error {
	cfg := GetInstance()

	// Validate that the required images are set

	if cfg.GetImages().InstanceMaintainer == "" {
		return errors.New("'images.openSearch.instanceMaintainer' must be set")
	}

	if cfg.GetImages().Fluentd == "" {
		return errors.New("'images.fluentd' must be set")
	}

	if cfg.GetImages().DataPrepper == "" {
		return errors.New("'images.dataPrepper' must be set")
	}

	if cfg.GetImages().OpenSearchInit == "" {
		return errors.New("'images.openSearchInit' must be set")
	}

	if cfg.GetImages().ElasticsearchExporter == "" {
		return errors.New("'images.elasticsearchExporter' must be set")
	}

	if cfg.GetImages().OpenSearch.Stable == "" {
		return errors.New("'images.openSearch.stable' must be set")
	}

	if cfg.GetImages().Dashboards.Stable == "" {
		return errors.New("'images.dashboards.stable' must be set")
	}

	// Validate that the OpenSearch and Dashboards images have the same version for each channel

	if err := validateImageVersions(StableChannel, cfg.GetImages().OpenSearch.Stable, cfg.GetImages().Dashboards.Stable); err != nil {
		return err
	}

	if err := validateImageVersions(PreviewChannel, cfg.GetImages().OpenSearch.Preview, cfg.GetImages().Dashboards.Preview); err != nil {
		return err
	}

	if err := validateImageVersions(DevelopmentChannel, cfg.GetImages().OpenSearch.Development, cfg.GetImages().Dashboards.Development); err != nil {
		return err
	}

	return nil
}

// validateImageVersions checks if the OpenSearch and Dashboards images for a given channel have the same version.
func validateImageVersions(channel, openSearchImage, dashboardsImage string) error {
	if openSearchImage == "" && dashboardsImage == "" {
		return nil
	}

	versionOS, err := utils.GetVersionFromImage(openSearchImage)
	if err != nil {
		return fmt.Errorf("config: 'images.openSearch.%s' has no valid version: %w", channel, err)
	}

	versionOSD, err := utils.GetVersionFromImage(dashboardsImage)
	if err != nil {
		return fmt.Errorf("config: 'images.dashboards.%s' has no valid version: %w", channel, err)
	}

	if !versionOS.Equal(versionOSD) {
		return fmt.Errorf("config: 'images.openSearch.%s' and 'images.dashboards.%s' must have the same version, but got '%s' and '%s'", channel, channel, versionOS, versionOSD)
	}

	return nil
}
